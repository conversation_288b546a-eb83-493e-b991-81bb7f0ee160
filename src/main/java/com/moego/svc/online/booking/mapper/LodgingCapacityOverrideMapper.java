package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.LodgingCapacityOverrideDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.LodgingCapacityOverride;
import com.moego.svc.online.booking.typehandler.CapacityDateRangeListTypeHandler;
import com.moego.svc.online.booking.typehandler.CapacityOverrideUnitTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface LodgingCapacityOverrideMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<LodgingCapacityOverrideMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    BasicColumn[] selectList = BasicColumn.columnList(id, settingId, dateRanges, capacity, unitType, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<LodgingCapacityOverride> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<LodgingCapacityOverride> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LodgingCapacityOverrideResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="setting_id", property="settingId", jdbcType=JdbcType.BIGINT),
        @Result(column="date_ranges", property="dateRanges", typeHandler=CapacityDateRangeListTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="capacity", property="capacity", jdbcType=JdbcType.INTEGER),
        @Result(column="unit_type", property="unitType", typeHandler=CapacityOverrideUnitTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<LodgingCapacityOverride> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LodgingCapacityOverrideResult")
    Optional<LodgingCapacityOverride> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int insertMultiple(Collection<LodgingCapacityOverride> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, lodgingCapacityOverride, c ->
            c.map(settingId).toProperty("settingId")
            .map(dateRanges).toProperty("dateRanges")
            .map(capacity).toProperty("capacity")
            .map(unitType).toProperty("unitType")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int insertSelective(LodgingCapacityOverride row) {
        return MyBatis3Utils.insert(this::insert, row, lodgingCapacityOverride, c ->
            c.map(settingId).toPropertyWhenPresent("settingId", row::getSettingId)
            .map(dateRanges).toPropertyWhenPresent("dateRanges", row::getDateRanges)
            .map(capacity).toPropertyWhenPresent("capacity", row::getCapacity)
            .map(unitType).toPropertyWhenPresent("unitType", row::getUnitType)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default Optional<LodgingCapacityOverride> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default List<LodgingCapacityOverride> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default List<LodgingCapacityOverride> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default Optional<LodgingCapacityOverride> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, lodgingCapacityOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    static UpdateDSL<UpdateModel> updateAllColumns(LodgingCapacityOverride row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalTo(row::getSettingId)
                .set(dateRanges).equalTo(row::getDateRanges)
                .set(capacity).equalTo(row::getCapacity)
                .set(unitType).equalTo(row::getUnitType)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LodgingCapacityOverride row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalToWhenPresent(row::getSettingId)
                .set(dateRanges).equalToWhenPresent(row::getDateRanges)
                .set(capacity).equalToWhenPresent(row::getCapacity)
                .set(unitType).equalToWhenPresent(row::getUnitType)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    default int updateByPrimaryKeySelective(LodgingCapacityOverride row) {
        return update(c ->
            c.set(settingId).equalToWhenPresent(row::getSettingId)
            .set(dateRanges).equalToWhenPresent(row::getDateRanges)
            .set(capacity).equalToWhenPresent(row::getCapacity)
            .set(unitType).equalToWhenPresent(row::getUnitType)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
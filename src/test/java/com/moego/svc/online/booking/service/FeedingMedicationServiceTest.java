package com.moego.svc.online.booking.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.business_customer.v1.BusinessPetFeedingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationModel;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetMedicationScheduleServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleResponse;
import com.moego.idl.service.business_customer.v1.ListPetMedicationScheduleResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@ExtendWith(SpringExtension.class)
class FeedingMedicationServiceTest {

    @Mock
    private BusinessPetMedicationScheduleServiceGrpc.BusinessPetMedicationScheduleServiceBlockingStub
            petMedicationScheduleService;

    @Mock
    private BusinessPetFeedingScheduleServiceGrpc.BusinessPetFeedingScheduleServiceBlockingStub
            petFeedingScheduleService;

    @InjectMocks
    private FeedingMedicationService feedingMedicationService;

    @Test
    void testSyncPetDetailDef_WithBoardingService() {
        Long companyId = 1L;
        Long petId = 100L;
        BookingRequestModel bookingRequest = createBookingRequestWithBoarding(petId);

        when(petMedicationScheduleService.listPetMedicationSchedule(any()))
                .thenReturn(ListPetMedicationScheduleResponse.getDefaultInstance());
        when(petFeedingScheduleService.listPetFeedingSchedule(any()))
                .thenReturn(ListPetFeedingScheduleResponse.getDefaultInstance());

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequest);

        verify(petMedicationScheduleService).batchCreateMedicationSchedule(any());
        verify(petFeedingScheduleService).batchCreateFeedingSchedule(any());
    }

    @Test
    void testSyncPetDetailDef_WithDaycareService() {
        Long companyId = 1L;
        Long petId = 200L;
        BookingRequestModel bookingRequest = createBookingRequestWithDaycare(petId);

        when(petMedicationScheduleService.listPetMedicationSchedule(any()))
                .thenReturn(ListPetMedicationScheduleResponse.getDefaultInstance());
        when(petFeedingScheduleService.listPetFeedingSchedule(any()))
                .thenReturn(ListPetFeedingScheduleResponse.getDefaultInstance());

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequest);

        verify(petMedicationScheduleService).batchCreateMedicationSchedule(any());
        verify(petFeedingScheduleService).batchCreateFeedingSchedule(any());
    }

    @Test
    void testSyncPetDetailDef_WithExistingSchedules() {
        Long companyId = 1L;
        Long petId = 300L;
        BookingRequestModel bookingRequest = createBookingRequestWithBoarding(petId);

        ListPetMedicationScheduleResponse medicationResponse = ListPetMedicationScheduleResponse.newBuilder()
                .addMedications(BusinessPetMedicationModel.newBuilder()
                        .setId(1L)
                        .setPetId(petId)
                        .build())
                .addSchedules(BusinessPetScheduleSettingModel.newBuilder()
                        .setScheduleId(1L)
                        .build())
                .build();
        ListPetFeedingScheduleResponse feedingResponse = ListPetFeedingScheduleResponse.newBuilder()
                .addFeedings(BusinessPetFeedingModel.newBuilder()
                        .setId(2L)
                        .setPetId(petId)
                        .build())
                .addSchedules(BusinessPetScheduleSettingModel.newBuilder()
                        .setScheduleId(2L)
                        .build())
                .build();

        when(petMedicationScheduleService.listPetMedicationSchedule(any())).thenReturn(medicationResponse);
        when(petFeedingScheduleService.listPetFeedingSchedule(any())).thenReturn(feedingResponse);

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequest);

        verify(petMedicationScheduleService, never()).batchCreateMedicationSchedule(any());
        verify(petFeedingScheduleService, never()).batchCreateFeedingSchedule(any());
    }

    @Test
    void testSyncPetDetailDef_WithEmptySchedules() {
        Long companyId = 1L;
        BookingRequestModel bookingRequest = BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(100L)
                                        .build())
                                .build())
                        .build())
                .build();

        feedingMedicationService.syncPetDetailDef(companyId, bookingRequest);

        verify(petMedicationScheduleService, never()).listPetMedicationSchedule(any());
        verify(petFeedingScheduleService, never()).listPetFeedingSchedule(any());
        verify(petMedicationScheduleService, never()).batchCreateMedicationSchedule(any());
        verify(petFeedingScheduleService, never()).batchCreateFeedingSchedule(any());
    }

    private BookingRequestModel createBookingRequestWithBoarding(Long petId) {
        return BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(petId)
                                        .build())
                                .addMedications(MedicationModel.newBuilder()
                                        .setId(1L)
                                        .addTime(MedicationModel.MedicationSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .build())
                                .addFeedings(FeedingModel.newBuilder()
                                        .setId(2L)
                                        .addTime(FeedingModel.FeedingSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();
    }

    private BookingRequestModel createBookingRequestWithDaycare(Long petId) {
        return BookingRequestModel.newBuilder()
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                                .setService(DaycareServiceDetailModel.newBuilder()
                                        .setPetId(petId)
                                        .build())
                                .addMedications(MedicationModel.newBuilder()
                                        .setId(1L)
                                        .addTime(MedicationModel.MedicationSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .build())
                                .addFeedings(FeedingModel.newBuilder()
                                        .setId(2L)
                                        .addTime(FeedingModel.FeedingSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .build())
                                .build())
                        .build())
                .build();
    }
}

package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.MedicationDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.Medication;
import com.moego.svc.online.booking.typehandler.MedicationDateTypeTypeHandler;
import com.moego.svc.online.booking.typehandler.MedicationSpecificDatesTypeHandler;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface MedicationMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<MedicationMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, serviceDetailId, serviceDetailType, time, amount, unit, medicationName, notes, createdAt, updatedAt, deletedAt, amountStr, dateType, specificDates);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<Medication> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<Medication> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="MedicationResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_id", property="serviceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_type", property="serviceDetailType", jdbcType=JdbcType.INTEGER),
        @Result(column="time", property="time", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="amount", property="amount", jdbcType=JdbcType.NUMERIC),
        @Result(column="unit", property="unit", jdbcType=JdbcType.VARCHAR),
        @Result(column="medication_name", property="medicationName", jdbcType=JdbcType.VARCHAR),
        @Result(column="notes", property="notes", jdbcType=JdbcType.VARCHAR),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="amount_str", property="amountStr", jdbcType=JdbcType.VARCHAR),
        @Result(column="date_type", property="dateType", typeHandler=MedicationDateTypeTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="specific_dates", property="specificDates", typeHandler=MedicationSpecificDatesTypeHandler.class, jdbcType=JdbcType.OTHER)
    })
    List<Medication> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("MedicationResult")
    Optional<Medication> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int insertMultiple(Collection<Medication> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, medication, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(serviceDetailId).toProperty("serviceDetailId")
            .map(serviceDetailType).toProperty("serviceDetailType")
            .map(time).toProperty("time")
            .map(amount).toProperty("amount")
            .map(unit).toProperty("unit")
            .map(medicationName).toProperty("medicationName")
            .map(notes).toProperty("notes")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(amountStr).toProperty("amountStr")
            .map(dateType).toProperty("dateType")
            .map(specificDates).toProperty("specificDates")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int insertSelective(Medication row) {
        return MyBatis3Utils.insert(this::insert, row, medication, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(serviceDetailId).toPropertyWhenPresent("serviceDetailId", row::getServiceDetailId)
            .map(serviceDetailType).toPropertyWhenPresent("serviceDetailType", row::getServiceDetailType)
            .map(time).toPropertyWhenPresent("time", row::getTime)
            .map(amount).toPropertyWhenPresent("amount", row::getAmount)
            .map(unit).toPropertyWhenPresent("unit", row::getUnit)
            .map(medicationName).toPropertyWhenPresent("medicationName", row::getMedicationName)
            .map(notes).toPropertyWhenPresent("notes", row::getNotes)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(amountStr).toPropertyWhenPresent("amountStr", row::getAmountStr)
            .map(dateType).toPropertyWhenPresent("dateType", row::getDateType)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default Optional<Medication> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default List<Medication> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default List<Medication> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default Optional<Medication> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, medication, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    static UpdateDSL<UpdateModel> updateAllColumns(Medication row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(serviceDetailId).equalTo(row::getServiceDetailId)
                .set(serviceDetailType).equalTo(row::getServiceDetailType)
                .set(time).equalTo(row::getTime)
                .set(amount).equalTo(row::getAmount)
                .set(unit).equalTo(row::getUnit)
                .set(medicationName).equalTo(row::getMedicationName)
                .set(notes).equalTo(row::getNotes)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(amountStr).equalTo(row::getAmountStr)
                .set(dateType).equalTo(row::getDateType)
                .set(specificDates).equalTo(row::getSpecificDates);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(Medication row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
                .set(serviceDetailType).equalToWhenPresent(row::getServiceDetailType)
                .set(time).equalToWhenPresent(row::getTime)
                .set(amount).equalToWhenPresent(row::getAmount)
                .set(unit).equalToWhenPresent(row::getUnit)
                .set(medicationName).equalToWhenPresent(row::getMedicationName)
                .set(notes).equalToWhenPresent(row::getNotes)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(amountStr).equalToWhenPresent(row::getAmountStr)
                .set(dateType).equalToWhenPresent(row::getDateType)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    default int updateByPrimaryKeySelective(Medication row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
            .set(serviceDetailType).equalToWhenPresent(row::getServiceDetailType)
            .set(time).equalToWhenPresent(row::getTime)
            .set(amount).equalToWhenPresent(row::getAmount)
            .set(unit).equalToWhenPresent(row::getUnit)
            .set(medicationName).equalToWhenPresent(row::getMedicationName)
            .set(notes).equalToWhenPresent(row::getNotes)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(amountStr).equalToWhenPresent(row::getAmountStr)
            .set(dateType).equalToWhenPresent(row::getDateType)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BoardingAutoAssignDynamicSqlSupport.boardingAutoAssign;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.svc.online.booking.entity.BoardingAutoAssign;
import com.moego.svc.online.booking.mapper.BoardingAutoAssignMapper;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class BoardingAutoAssignService {

    private final BoardingAutoAssignMapper boardingAutoAssignMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public BoardingAutoAssign get(long id) {
        return boardingAutoAssignMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(BoardingAutoAssign entity) {
        boardingAutoAssignMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(BoardingAutoAssign entity) {
        entity.setUpdatedAt(new Date());
        return boardingAutoAssignMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return boardingAutoAssignMapper.update(c -> c.set(boardingAutoAssign.deletedAt)
                .equalTo(new Date())
                .where(boardingAutoAssign.id, isEqualTo(id))
                .and(boardingAutoAssign.deletedAt, isNull()));
    }

    public Map<Long, BoardingAutoAssign> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return boardingAutoAssignMapper
                .select(c -> c.where(boardingAutoAssign.bookingRequestId, isIn(bookingRequestIds))
                        .and(boardingAutoAssign.deletedAt, isNull()))
                .stream()
                .collect(Collectors.toMap(BoardingAutoAssign::getBookingRequestId, Function.identity(), (a, b) -> a));
    }
}

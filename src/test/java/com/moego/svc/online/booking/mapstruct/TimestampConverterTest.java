package com.moego.svc.online.booking.mapstruct;

import static java.time.ZoneOffset.UTC;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.google.protobuf.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

class TimestampConverterTest {

    private TimestampConverter converter;

    @BeforeEach
    void setUp() {
        converter = Mappers.getMapper(TimestampConverter.class);
    }

    @Test
    void testDateToTimestamp() {
        // Test with a non-null date
        Date date = new Date(1623456789000L); // 2021-06-11 20:19:49 UTC
        Timestamp timestamp = converter.dateToTimestamp(date);

        assertNotNull(timestamp);
        assertEquals(1623456789, timestamp.getSeconds());
        assertEquals(0, timestamp.getNanos());

        // Test with null date
        Timestamp nullTimestamp = converter.dateToTimestamp(null);
        assertEquals(Timestamp.getDefaultInstance(), nullTimestamp);
    }

    @Test
    void testTimestampToDate() {
        // Test with a non-null timestamp
        Timestamp timestamp = Timestamp.newBuilder().setSeconds(1623456789).build();
        Date date = converter.timestampToDate(timestamp);

        assertNotNull(date);
        assertEquals(1623456789000L, date.getTime());

        // Test with null timestamp
        Date nullDate = converter.timestampToDate(null);
        assertNull(nullDate);
    }

    @Test
    void testRoundTrip() {
        // Test conversion from Date to Timestamp and back
        Date originalDate = new Date();
        Timestamp timestamp = converter.dateToTimestamp(originalDate);
        Date convertedDate = converter.timestampToDate(timestamp);

        // The converted date should be equal to the original date, ignoring milliseconds
        assertEquals(originalDate.getTime() / 1000, convertedDate.getTime() / 1000);
    }

    @Test
    void toLocalDateTime_ShouldConvertTimestampToLocalDateTime() {
        // Arrange
        var seconds = 1734912000L; // 2024-12-23 00:00:00 UTC
        var nanos = 123456789;
        var timestamp =
                Timestamp.newBuilder().setSeconds(seconds).setNanos(nanos).build();

        // Act
        var result = converter.toLocalDateTime(timestamp);

        // Assert
        var expected = LocalDateTime.of(2024, 12, 23, 0, 0, 0, 123456789);
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toTimestamp_WhenLocalDateTimeIsNull_ShouldReturnDefaultInstance() {
        // Act
        var result = converter.toTimestamp(null);

        // Assert
        var expected = Timestamp.getDefaultInstance();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toTimestamp_ShouldConvertLocalDateTimeToTimestamp() {
        // Arrange
        var dateTime = LocalDateTime.of(2024, 12, 23, 12, 0, 0, 123456789);

        // Act
        var result = converter.toTimestamp(dateTime);

        // Assert
        var expected = Timestamp.newBuilder()
                .setSeconds(dateTime.toEpochSecond(UTC))
                .setNanos(dateTime.getNano())
                .build();
        assertThat(result).isEqualTo(expected);
    }
}

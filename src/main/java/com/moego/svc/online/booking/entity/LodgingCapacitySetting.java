package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table lodging_capacity_setting
 */
public class LodgingCapacitySetting {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.service_item_type")
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   if limit requests based on  lodging/area capacity
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.is_capacity_limited")
    private Boolean isCapacityLimited;

    /**
     * Database Column Remarks:
     *   limit requests based on service related lodging/area capacity
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.capacity_limit")
    private Integer capacityLimit;

    /**
     * Database Column Remarks:
     *   当达到 capacity 限制时是否允许提交 waitlist
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.allow_waitlist_signups")
    private Boolean allowWaitlistSignups;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.service_item_type")
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.service_item_type")
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.is_capacity_limited")
    public Boolean getIsCapacityLimited() {
        return isCapacityLimited;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.is_capacity_limited")
    public void setIsCapacityLimited(Boolean isCapacityLimited) {
        this.isCapacityLimited = isCapacityLimited;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.capacity_limit")
    public Integer getCapacityLimit() {
        return capacityLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.capacity_limit")
    public void setCapacityLimit(Integer capacityLimit) {
        this.capacityLimit = capacityLimit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.allow_waitlist_signups")
    public Boolean getAllowWaitlistSignups() {
        return allowWaitlistSignups;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.allow_waitlist_signups")
    public void setAllowWaitlistSignups(Boolean allowWaitlistSignups) {
        this.allowWaitlistSignups = allowWaitlistSignups;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyId=").append(companyId);
        sb.append(", businessId=").append(businessId);
        sb.append(", serviceItemType=").append(serviceItemType);
        sb.append(", isCapacityLimited=").append(isCapacityLimited);
        sb.append(", capacityLimit=").append(capacityLimit);
        sb.append(", allowWaitlistSignups=").append(allowWaitlistSignups);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LodgingCapacitySetting other = (LodgingCapacitySetting) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getServiceItemType() == null ? other.getServiceItemType() == null : this.getServiceItemType().equals(other.getServiceItemType()))
            && (this.getIsCapacityLimited() == null ? other.getIsCapacityLimited() == null : this.getIsCapacityLimited().equals(other.getIsCapacityLimited()))
            && (this.getCapacityLimit() == null ? other.getCapacityLimit() == null : this.getCapacityLimit().equals(other.getCapacityLimit()))
            && (this.getAllowWaitlistSignups() == null ? other.getAllowWaitlistSignups() == null : this.getAllowWaitlistSignups().equals(other.getAllowWaitlistSignups()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getServiceItemType() == null) ? 0 : getServiceItemType().hashCode());
        result = prime * result + ((getIsCapacityLimited() == null) ? 0 : getIsCapacityLimited().hashCode());
        result = prime * result + ((getCapacityLimit() == null) ? 0 : getCapacityLimit().hashCode());
        result = prime * result + ((getAllowWaitlistSignups() == null) ? 0 : getAllowWaitlistSignups().hashCode());
        return result;
    }
}
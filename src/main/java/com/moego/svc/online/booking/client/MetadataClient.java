package com.moego.svc.online.booking.client;

import com.moego.idl.models.metadata.v1.ValueModel;
import com.moego.idl.service.metadata.v1.DescribeValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Slf4j
@Component
@RequiredArgsConstructor
public class MetadataClient {
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataClient;

    public static final String BD_WHITE_LIST_KEY = "allow_boarding_and_daycare";

    public Long getKeyIDByName(String keyName) {
        return metadataClient
                .getKey(GetKeyRequest.newBuilder().setName(keyName).build())
                .getKey()
                .getId();
    }

    List<ValueModel> getValueByOwnerIDs(Long keyId, List<Long> ownerIds) {
        if (CollectionUtils.isEmpty(ownerIds)) {
            return List.of();
        }

        return metadataClient
                .describeValues(DescribeValuesRequest.newBuilder()
                        .setKeyId(keyId)
                        .addAllOwnerIds(ownerIds)
                        .setPagination(
                                PaginationRequest.newBuilder().setPageNum(1).setPageSize(ownerIds.size()))
                        .build())
                .getValuesList();
    }

    public Map<Long, Boolean> batchCheckBDWhiteList(List<Long> companyIds) {
        if (CollectionUtils.isEmpty(companyIds)) {
            return Map.of();
        }
        companyIds = companyIds.stream().distinct().toList();
        Map<Long, Boolean> result = companyIds.stream().collect(Collectors.toMap(k -> k, v -> false));
        Long keyId = getKeyIDByName(BD_WHITE_LIST_KEY);
        List<ValueModel> valueModels = getValueByOwnerIDs(keyId, companyIds);
        for (ValueModel value : valueModels) {
            result.put(value.getOwnerId(), value.getValue().equals("true"));
        }
        return result;
    }
}

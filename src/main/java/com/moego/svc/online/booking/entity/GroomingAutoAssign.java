package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 * Database Table Remarks:
 *   appointment auto assign record
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table grooming_auto_assign
 */
public class GroomingAutoAssign {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of staff, auto assign staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.staff_id")
    private Long staffId;

    /**
     * Database Column Remarks:
     *   auto assign appointment time, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.start_time")
    private Integer startTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.deleted_at")
    private Date deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.grooming_service_detail_id")
    private Long groomingServiceDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.grooming_service_detail_id")
    public Long getGroomingServiceDetailId() {
        return groomingServiceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.grooming_service_detail_id")
    public void setGroomingServiceDetailId(Long groomingServiceDetailId) {
        this.groomingServiceDetailId = groomingServiceDetailId;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", staffId=").append(staffId);
        sb.append(", startTime=").append(startTime);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", groomingServiceDetailId=").append(groomingServiceDetailId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GroomingAutoAssign other = (GroomingAutoAssign) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getGroomingServiceDetailId() == null ? other.getGroomingServiceDetailId() == null : this.getGroomingServiceDetailId().equals(other.getGroomingServiceDetailId()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getGroomingServiceDetailId() == null) ? 0 : getGroomingServiceDetailId().hashCode());
        return result;
    }
}
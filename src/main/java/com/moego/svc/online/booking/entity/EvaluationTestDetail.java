package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Database Table Remarks:
 *   The evaluation test detail
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table evaluation_test_detail
 */
public class EvaluationTestDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   The id of current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.evaluation_id")
    private Long evaluationId;

    /**
     * Database Column Remarks:
     *   The price of current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_price")
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   The duration of current evaluation test, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.duration")
    private Integer duration;

    /**
     * Database Column Remarks:
     *   The start date of the evaluation test, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_date")
    private String startDate;

    /**
     * Database Column Remarks:
     *   The start time of the evaluation test, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   The end date of the evaluation test, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_date")
    private String endDate;

    /**
     * Database Column Remarks:
     *   The end time of the evaluation test, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_time")
    private Integer endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.deleted_at")
    private Date deletedAt;

    /**
     * Database Column Remarks:
     *   evaluation 绑定的 service id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_id")
    private Long serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.evaluation_id")
    public Long getEvaluationId() {
        return evaluationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.evaluation_id")
    public void setEvaluationId(Long evaluationId) {
        this.evaluationId = evaluationId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.duration")
    public Integer getDuration() {
        return duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_date")
    public String getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_date")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_date")
    public String getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_date")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_id")
    public Long getServiceId() {
        return serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", petId=").append(petId);
        sb.append(", evaluationId=").append(evaluationId);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", duration=").append(duration);
        sb.append(", startDate=").append(startDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", endDate=").append(endDate);
        sb.append(", endTime=").append(endTime);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", serviceId=").append(serviceId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        EvaluationTestDetail other = (EvaluationTestDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getEvaluationId() == null ? other.getEvaluationId() == null : this.getEvaluationId().equals(other.getEvaluationId()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getDuration() == null ? other.getDuration() == null : this.getDuration().equals(other.getDuration()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getServiceId() == null ? other.getServiceId() == null : this.getServiceId().equals(other.getServiceId()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getEvaluationId() == null) ? 0 : getEvaluationId().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getDuration() == null) ? 0 : getDuration().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getServiceId() == null) ? 0 : getServiceId().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.entity;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Database Table Remarks:
 *   Stores information about medication events.
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table medication
 */
public class Medication {
    /**
     * Database Column Remarks:
     *   The primary key identifier for each medication event.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The booking request identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The service detail identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_id")
    private Long serviceDetailId;

    /**
     * Database Column Remarks:
     *   service detail type, 1: boarding, 2: daycare
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_type")
    private Integer serviceDetailType;

    /**
     * Database Column Remarks:
     *   Medication time.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.time")
    private String time;

    /**
     * Database Column Remarks:
     *   Medication amount, must be greater than 0.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount")
    private BigDecimal amount;

    /**
     * Database Column Remarks:
     *   Medication unit.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.unit")
    private String unit;

    /**
     * Database Column Remarks:
     *   Medication name.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.medication_name")
    private String medicationName;

    /**
     * Database Column Remarks:
     *   Additional notes about the medication.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.notes")
    private String notes;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.deleted_at")
    private Date deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount_str")
    private String amountStr;

    /**
     * Database Column Remarks:
     *   1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.date_type")
    private FeedingMedicationScheduleDateType dateType;

    /**
     * Database Column Remarks:
     *   specific_date list, yyyy-mm-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.specific_dates")
    private List<String> specificDates;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_id")
    public Long getServiceDetailId() {
        return serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_id")
    public void setServiceDetailId(Long serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_type")
    public Integer getServiceDetailType() {
        return serviceDetailType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_type")
    public void setServiceDetailType(Integer serviceDetailType) {
        this.serviceDetailType = serviceDetailType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.time")
    public String getTime() {
        return time;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.time")
    public void setTime(String time) {
        this.time = time;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount")
    public BigDecimal getAmount() {
        return amount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount")
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.unit")
    public String getUnit() {
        return unit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.unit")
    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.medication_name")
    public String getMedicationName() {
        return medicationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.medication_name")
    public void setMedicationName(String medicationName) {
        this.medicationName = medicationName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.notes")
    public String getNotes() {
        return notes;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.notes")
    public void setNotes(String notes) {
        this.notes = notes;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount_str")
    public String getAmountStr() {
        return amountStr;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount_str")
    public void setAmountStr(String amountStr) {
        this.amountStr = amountStr;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.date_type")
    public FeedingMedicationScheduleDateType getDateType() {
        return dateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.date_type")
    public void setDateType(FeedingMedicationScheduleDateType dateType) {
        this.dateType = dateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.specific_dates")
    public List<String> getSpecificDates() {
        return specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.specific_dates")
    public void setSpecificDates(List<String> specificDates) {
        this.specificDates = specificDates;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", serviceDetailId=").append(serviceDetailId);
        sb.append(", serviceDetailType=").append(serviceDetailType);
        sb.append(", time=").append(time);
        sb.append(", amount=").append(amount);
        sb.append(", unit=").append(unit);
        sb.append(", medicationName=").append(medicationName);
        sb.append(", notes=").append(notes);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", amountStr=").append(amountStr);
        sb.append(", dateType=").append(dateType);
        sb.append(", specificDates=").append(specificDates);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Medication other = (Medication) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getServiceDetailId() == null ? other.getServiceDetailId() == null : this.getServiceDetailId().equals(other.getServiceDetailId()))
            && (this.getServiceDetailType() == null ? other.getServiceDetailType() == null : this.getServiceDetailType().equals(other.getServiceDetailType()))
            && (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getMedicationName() == null ? other.getMedicationName() == null : this.getMedicationName().equals(other.getMedicationName()))
            && (this.getNotes() == null ? other.getNotes() == null : this.getNotes().equals(other.getNotes()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getAmountStr() == null ? other.getAmountStr() == null : this.getAmountStr().equals(other.getAmountStr()))
            && (this.getDateType() == null ? other.getDateType() == null : this.getDateType().equals(other.getDateType()))
            && (this.getSpecificDates() == null ? other.getSpecificDates() == null : this.getSpecificDates().equals(other.getSpecificDates()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getServiceDetailId() == null) ? 0 : getServiceDetailId().hashCode());
        result = prime * result + ((getServiceDetailType() == null) ? 0 : getServiceDetailType().hashCode());
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getMedicationName() == null) ? 0 : getMedicationName().hashCode());
        result = prime * result + ((getNotes() == null) ? 0 : getNotes().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getAmountStr() == null) ? 0 : getAmountStr().hashCode());
        result = prime * result + ((getDateType() == null) ? 0 : getDateType().hashCode());
        result = prime * result + ((getSpecificDates() == null) ? 0 : getSpecificDates().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.BoardingServiceDetailDynamicSqlSupport.boardingServiceDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.online.booking.mapper.BoardingServiceDetailMapper;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BoardingServiceDetailService {

    private final BoardingServiceDetailMapper boardingServiceDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final ServiceHelper serviceHelper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public BoardingServiceDetail get(long id) {
        return boardingServiceDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(BoardingServiceDetail entity) {

        populate(entity);

        boardingServiceDetailMapper.insertSelective(entity);

        return entity.getId();
    }

    private void populate(BoardingServiceDetail entity) {

        check(entity);

        var bookingRequest = bookingRequestMapper
                .selectByPrimaryKey(entity.getBookingRequestId())
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + entity.getBookingRequestId()));

        var builder = MustGetCustomizedServiceParam.builder();
        builder.serviceId(entity.getServiceId());
        builder.companyId(bookingRequest.getCompanyId());
        builder.businessId(bookingRequest.getBusinessId());
        builder.petId(entity.getPetId());

        var service = serviceHelper.mustGetCustomizedService(builder.build());

        if (entity.getServicePrice() == null) {
            entity.setServicePrice(BigDecimal.valueOf(service.getPrice()));
        }
        if (entity.getTaxId() == null) {
            entity.setTaxId(service.getTaxId());
        }
        if (entity.getEndDate() == null) {
            entity.setEndDate(entity.getStartDate());
        }
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(BoardingServiceDetail entity) {
        entity.setUpdatedAt(new Date());
        return boardingServiceDetailMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return boardingServiceDetailMapper.update(c -> c.set(boardingServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(boardingServiceDetail.id, isEqualTo(id))
                .and(boardingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List boarding service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId bookingRequestId
     * @return list of boarding service detail
     */
    public List<BoardingServiceDetail> listByBookingRequestId(long bookingRequestId) {
        return boardingServiceDetailMapper.select(
                c -> c.where(boardingServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(boardingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List boarding service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of boarding service detail
     */
    public List<BoardingServiceDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return boardingServiceDetailMapper.select(
                c -> c.where(boardingServiceDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(boardingServiceDetail.deletedAt, isNull()));
    }

    private static void check(BoardingServiceDetail entity) {
        if (!isNormal(entity.getBookingRequestId()))
            throw bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        if (!isNormal(entity.getPetId())) throw bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        if (!isNormal(entity.getServiceId())) throw bizException(Code.CODE_PARAMS_ERROR, "serviceId is required");
    }
}

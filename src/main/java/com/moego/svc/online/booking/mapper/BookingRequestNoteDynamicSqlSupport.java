package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingRequestNoteDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    public static final BookingRequestNote bookingRequestNote = new BookingRequestNote();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.id")
    public static final SqlColumn<Long> id = bookingRequestNote.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.company_id")
    public static final SqlColumn<Long> companyId = bookingRequestNote.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.customer_id")
    public static final SqlColumn<Long> customerId = bookingRequestNote.customerId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = bookingRequestNote.bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.note")
    public static final SqlColumn<String> note = bookingRequestNote.note;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = bookingRequestNote.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = bookingRequestNote.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_note.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = bookingRequestNote.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_note")
    public static final class BookingRequestNote extends AliasableSqlTable<BookingRequestNote> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> customerId = column("customer_id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<String> note = column("note", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public BookingRequestNote() {
            super("booking_request_note", BookingRequestNote::new);
        }
    }
}
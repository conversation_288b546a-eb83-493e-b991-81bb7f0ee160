package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.svc.online.booking.entity.BookingRequestAppointmentMapping;
import com.moego.svc.online.booking.mapper.BookingRequestAppointmentMappingMapper;
import java.util.Collection;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;

/**
 * {@link BookingRequestAppointmentMappingService}
 */
@ExtendWith(MockitoExtension.class)
class BookingRequestAppointmentMappingServiceTest {

    @Mock
    BookingRequestAppointmentMappingMapper bookingRequestAppointmentMappingMapper;

    @InjectMocks
    BookingRequestAppointmentMappingService bookingRequestAppointmentMappingService;

    /**
     * {@link BookingRequestAppointmentMappingService#insert(BookingRequestAppointmentMapping)}
     */
    @Nested
    @DisplayName("Tests for insert method")
    class InsertTests {

        @Test
        void insert_Success() {
            // Arrange
            var entity = new BookingRequestAppointmentMapping();
            entity.setBookingRequestId(1L);
            entity.setAppointmentId(2L);

            // Assume that the mapper sets the ID upon insertion
            doAnswer(invocation -> {
                        BookingRequestAppointmentMapping arg = invocation.getArgument(0);
                        arg.setId(100L);
                        return null;
                    })
                    .when(bookingRequestAppointmentMappingMapper)
                    .insertSelective(entity);

            // Act
            long resultId = bookingRequestAppointmentMappingService.insert(entity);

            // Assert
            assertThat(resultId)
                    .as("Verify that the returned ID matches the entity's ID after insertion")
                    .isEqualTo(100L);
            verify(bookingRequestAppointmentMappingMapper, times(1)).insertSelective(entity);
        }

        @Test
        void insert_InvalidBookingRequestId() {
            // Arrange
            var entity = new BookingRequestAppointmentMapping();
            entity.setBookingRequestId(0L); // Assuming 0 is invalid
            entity.setAppointmentId(2L);

            // Act & Assert
            assertThatThrownBy(() -> bookingRequestAppointmentMappingService.insert(entity))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("bookingRequestId is empty");

            verify(bookingRequestAppointmentMappingMapper, never()).insertSelective(any());
        }

        @Test
        void insert_InvalidAppointmentId() {
            // Arrange
            var entity = new BookingRequestAppointmentMapping();
            entity.setBookingRequestId(1L);
            entity.setAppointmentId(0L); // Assuming 0 is invalid

            // Act & Assert
            assertThatThrownBy(() -> bookingRequestAppointmentMappingService.insert(entity))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("appointmentId is empty");

            verify(bookingRequestAppointmentMappingMapper, never()).insertSelective(any());
        }
    }

    /**
     * {@link BookingRequestAppointmentMappingService#deleteByBookingRequestId(long)}
     */
    @Nested
    @DisplayName("Tests for deleteByBookingRequestId method")
    class DeleteByBookingRequestIdTests {

        @Test
        void deleteByBookingRequestId_Success() {
            // Arrange
            long bookingRequestId = 1L;
            int expectedDeletedRows = 3;

            when(bookingRequestAppointmentMappingMapper.delete(any(DeleteDSLCompleter.class)))
                    .thenReturn(expectedDeletedRows);

            // Act
            int actualDeletedRows = bookingRequestAppointmentMappingService.deleteByBookingRequestId(bookingRequestId);

            // Assert
            assertThat(actualDeletedRows)
                    .as("Verify that the number of deleted rows matches the expected value")
                    .isEqualTo(expectedDeletedRows);
            verify(bookingRequestAppointmentMappingMapper, times(1)).delete(any(DeleteDSLCompleter.class));
        }

        @Test
        void deleteByBookingRequestId_NoMappings() {
            // Arrange
            long bookingRequestId = 2L;

            when(bookingRequestAppointmentMappingMapper.delete(any(DeleteDSLCompleter.class)))
                    .thenReturn(0);

            // Act
            int actualDeletedRows = bookingRequestAppointmentMappingService.deleteByBookingRequestId(bookingRequestId);

            // Assert
            assertThat(actualDeletedRows)
                    .as("Verify that no rows are deleted when no mappings are found")
                    .isZero();
            verify(bookingRequestAppointmentMappingMapper, times(1)).delete(any(DeleteDSLCompleter.class));
        }
    }

    /**
     * {@link BookingRequestAppointmentMappingService#listByAppointmentIds(Collection)}
     */
    @Nested
    @DisplayName("Tests for listByAppointmentIds method")
    class ListByAppointmentIdsTests {

        @Test
        @DisplayName("Should return list of mappings when appointmentIds are provided")
        void listByAppointmentIds_WithIds() {
            // Arrange
            var appointmentIds = List.of(1L, 2L, 3L);
            var mapping1 = new BookingRequestAppointmentMapping();
            mapping1.setId(10L);
            mapping1.setBookingRequestId(100L);
            mapping1.setAppointmentId(1L);

            var mapping2 = new BookingRequestAppointmentMapping();
            mapping2.setId(20L);
            mapping2.setBookingRequestId(200L);
            mapping2.setAppointmentId(2L);

            var expectedList = List.of(mapping1, mapping2);

            when(bookingRequestAppointmentMappingMapper.select(any())).thenReturn(expectedList);

            // Act
            var actualList = bookingRequestAppointmentMappingService.listByAppointmentIds(appointmentIds);

            // Assert
            assertThat(actualList)
                    .as("Verify that the returned list matches the expected list")
                    .hasSize(expectedList.size())
                    .containsExactlyElementsOf(expectedList);
            verify(bookingRequestAppointmentMappingMapper, times(1)).select(any());
        }

        @Test
        @DisplayName("Should return empty list when appointmentIds are empty")
        void listByAppointmentIds_EmptyIds() {
            // Arrange
            var appointmentIds = List.<Long>of();

            // Act
            var actualList = bookingRequestAppointmentMappingService.listByAppointmentIds(appointmentIds);

            // Assert
            assertThat(actualList)
                    .as("Verify that the returned list is empty when no appointmentIds are provided")
                    .isEmpty();
            verify(bookingRequestAppointmentMappingMapper, never()).select(any());
        }
    }
}

package com.moego.svc.online.booking.service;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipUsageView;
import com.moego.idl.models.membership.v1.RedeemContext;
import com.moego.idl.models.membership.v1.RedeemScenario;
import com.moego.idl.models.membership.v1.RedeemScenarioItem;
import com.moego.idl.models.membership.v1.TargetType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsRequest;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.UpsertRecommendBenefitUsageRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.helper.OfferingHelper;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class MembershipService {

    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderClient;
    private final OfferingHelper offeringHelper;

    public boolean applyMemberships(
            Long businessId, Long companyId, Long staffId, long orderId, Collection<Long> needAppliedMembershipIds) {
        OrderDetailModel orderDetail = getOrderDetailByOrderId(businessId, orderId);
        long customerId = orderDetail.getOrder().getCustomerId();

        // membership info for apply benefits
        ListRecommendMembershipsResponse membershipsResponse = getListRecommendMembershipsResponse(
                businessId, companyId, needAppliedMembershipIds, orderDetail, customerId);
        log.debug("apply memberships: {}", membershipsResponse);
        List<MembershipUsageView> usageViews = membershipsResponse.getUsageViewsList();
        if (CollectionUtils.isEmpty(usageViews)) {
            // clear applied records
            membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                    .setOrderId(orderId)
                    .setCustomerId(orderDetail.getOrder().getCustomerId())
                    .build());
            return false;
        }

        // remove discounts
        List<OrderLineDiscountModel> deleteAllUsedDiscounts = orderDetail.getLineDiscountsList().stream()
                .map(discount -> discount.toBuilder().setIsDeleted(true).build())
                .toList();

        // apply discounts
        Map<Long, OrderLineDiscountModel> discountMap =
                usageViews.stream()
                        .collect(Collectors.groupingBy(MembershipUsageView::getOrderItemId))
                        .entrySet()
                        .stream()
                        .map(entry -> convertToLineDiscountModel(
                                entry.getKey(), entry.getValue(), orderId, businessId, staffId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(OrderLineDiscountModel::getOrderItemId, Function.identity()));

        if (CollectionUtils.isEmpty(discountMap)) {
            return false;
        }

        // update order
        UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(OrderModel.newBuilder()
                        .setId(orderId)
                        .setUpdateBy(staffId)
                        .setBusinessId(businessId)
                        .build())
                // .addAllLineItems(newItems)
                .addAllLineDiscounts(deleteAllUsedDiscounts)
                .addAllLineDiscounts(discountMap.values());
        orderClient.updateOrderIncremental(requestBuilder.build());

        // upsert applied records
        membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                .setOrderId(orderId)
                .setCustomerId(customerId)
                .addAllAllMemberships(membershipsResponse.getAllList())
                .addAllBenefitCombination(membershipsResponse.getBenefitCombinationList())
                .addAllUsageViews(usageViews)
                .build());
        return true;
    }

    private ListRecommendMembershipsResponse getListRecommendMembershipsResponse(
            Long businessId,
            Long companyId,
            Collection<Long> needAppliedMembershipIds,
            OrderDetailModel orderDetail,
            long customerId) {
        List<Long> serviceIds = orderDetail.getLineItemsList().stream()
                .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType()))
                .map(OrderLineItemModel::getObjectId)
                .distinct()
                .toList();
        Map<Long, ServiceBriefView> serviceMap = offeringHelper.getServiceMap(companyId, serviceIds);

        List<RedeemScenarioItem> list = orderDetail.getLineItemsList().stream()
                .map(item -> {
                    RedeemScenarioItem.Builder builder = RedeemScenarioItem.newBuilder()
                            .setTargetId(item.getObjectId())
                            .setTargetType(getTargetType(item.getType(), item.getObjectId(), serviceMap))
                            .setAmount(item.getQuantity())
                            .setPrice(item.getUnitPrice())
                            .setOrderItemId(item.getId());
                    return builder.build();
                })
                .filter(item -> !Objects.equals(item.getTargetType(), TargetType.TARGET_TYPE_UNSPECIFIED))
                .toList();

        RedeemContext.Builder builder = RedeemContext.newBuilder()
                .setScenario(RedeemScenario.REDEEM_BY_ONLINE_BOOKING_SUBMIT)
                .addAllItems(list);

        // membership info for apply benefits
        // get membership
        ListRecommendMembershipsRequest.Builder requestBuilder = ListRecommendMembershipsRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setContext(builder.build());
        if (!CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            requestBuilder.setFilter(ListRecommendMembershipsRequest.Filter.newBuilder()
                    .addAllTargetMembershipIds(needAppliedMembershipIds)
                    .build());
        }
        return membershipService.listRecommendedMemberships(requestBuilder.build());
    }

    private @Nullable OrderDetailModel getOrderDetailByOrderId(Long businessId, Long orderId) {
        if (orderId == null) {
            return null;
        }

        var builder = GetOrderRequest.newBuilder().setId(orderId).setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        if (businessId != null) {
            builder.setBusinessId(businessId);
        }
        OrderDetailModel orderDetail = orderClient.getOrderDetail(builder.build());
        if (!orderDetail.hasOrder()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }
        return orderDetail;
    }

    private @Nullable OrderLineDiscountModel convertToLineDiscountModel(
            Long orderItemId, List<MembershipUsageView> values, Long orderId, Long businessId, Long staffId) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }

        double discountAmount = values.stream()
                .map(MembershipUsageView::getPriceReduction)
                .reduce(Double::sum)
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "price reduction not found"));

        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder();
        discountBuilder.setDiscountType(DiscountType.AMOUNT.getType());
        discountBuilder.setDiscountAmount(discountAmount);
        discountBuilder.setOrderId(orderId);
        discountBuilder.setOrderItemId(orderItemId);
        discountBuilder.setApplyBy(staffId);
        discountBuilder.setBusinessId(businessId);
        discountBuilder.setApplyType(LineApplyType.TYPE_ITEM.getType());
        discountBuilder.setIsDeleted(false);
        return discountBuilder.build();
    }

    private TargetType getTargetType(String type, Long objectId, Map<Long, ServiceBriefView> serviceMap) {
        if (Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), type)) {
            if (!serviceMap.containsKey(objectId)) {
                return TargetType.TARGET_TYPE_UNSPECIFIED;
            }
            ServiceBriefView service = serviceMap.get(objectId);
            if (Objects.equals(ServiceType.SERVICE, service.getType())) {
                return TargetType.SERVICE;
            } else if (Objects.equals(ServiceType.ADDON, service.getType())) {
                return TargetType.ADDON;
            }
        } else if (Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), type)) {
            return TargetType.PRODUCT;
        }
        return TargetType.TARGET_TYPE_UNSPECIFIED;
    }
}

package com.moego.svc.online.booking.dto;

import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import lombok.Builder;

@Builder(toBuilder = true)
public record FinalWeekTimeRangeDTO(
        Map<LocalDate, List<DayTimeRangeDef>> arrivalTimeRangeMap,
        Map<LocalDate, List<DayTimeRangeDef>> pickUpTimeRangeMap,
        List<Long> settingIds) {}

package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table day_hour_limit_group
 */
public class DayHourLimitGroup {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.only_accept_selected")
    private Boolean onlyAcceptSelected;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.only_accept_selected")
    public Boolean getOnlyAcceptSelected() {
        return onlyAcceptSelected;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.only_accept_selected")
    public void setOnlyAcceptSelected(Boolean onlyAcceptSelected) {
        this.onlyAcceptSelected = onlyAcceptSelected;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", onlyAcceptSelected=").append(onlyAcceptSelected);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DayHourLimitGroup other = (DayHourLimitGroup) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getOnlyAcceptSelected() == null ? other.getOnlyAcceptSelected() == null : this.getOnlyAcceptSelected().equals(other.getOnlyAcceptSelected()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getOnlyAcceptSelected() == null) ? 0 : getOnlyAcceptSelected().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}
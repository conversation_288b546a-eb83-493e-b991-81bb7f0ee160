package com.moego.svc.online.booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class LodgingUtilsTest {

    @Test
    void calLodgingStatus_withEmptyLodgingUnitList_shouldReturnEmptyMap() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();
        List<LodgingUnitModel> lodgingUnitList = List.of();
        Map<Long, Map<String, Integer>> petCntPerLodgingPerDay = Map.of();

        Map<Long, LodgingOccupiedStatus> result =
                LodgingUtils.calLodgingStatus(List.of(lodgingType), lodgingUnitList, petCntPerLodgingPerDay);

        assertThat(result).isEmpty();
    }

    @Test
    void calLodgingStatus_withNoPetCount_shouldReturnVacantStatus() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();
        List<LodgingUnitModel> lodgingUnitList =
                List.of(LodgingUnitModel.newBuilder().setId(1L).build());
        Map<Long, Map<String, Integer>> petCntPerLodgingPerDay = Map.of();

        Map<Long, LodgingOccupiedStatus> result =
                LodgingUtils.calLodgingStatus(List.of(lodgingType), lodgingUnitList, petCntPerLodgingPerDay);

        assertThat(result).containsEntry(1L, LodgingOccupiedStatus.VACANT);
    }

    @Test
    void calLodgingStatus_withPetCountLessThanMax_shouldReturnPartiallyOccupiedStatus() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();
        List<LodgingUnitModel> lodgingUnitList =
                List.of(LodgingUnitModel.newBuilder().setId(1L).build());
        Map<Long, Map<String, Integer>> petCntPerLodgingPerDay = Map.of(1L, Map.of("2023-10-01", 3));

        Map<Long, LodgingOccupiedStatus> result =
                LodgingUtils.calLodgingStatus(List.of(lodgingType), lodgingUnitList, petCntPerLodgingPerDay);

        assertThat(result).containsEntry(1L, LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
    }

    @Test
    void calLodgingStatus_withPetCountEqualToMax_shouldReturnFullyOccupiedStatus() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();
        List<LodgingUnitModel> lodgingUnitList =
                List.of(LodgingUnitModel.newBuilder().setId(1L).build());
        Map<Long, Map<String, Integer>> petCntPerLodgingPerDay = Map.of(1L, Map.of("2023-10-01", 5));

        Map<Long, LodgingOccupiedStatus> result =
                LodgingUtils.calLodgingStatus(List.of(lodgingType), lodgingUnitList, petCntPerLodgingPerDay);

        assertThat(result).containsEntry(1L, LodgingOccupiedStatus.FULLY_OCCUPIED);
    }

    @Test
    void calLodgingStatus_withPetCountGreaterThanMax_shouldReturnFullyOccupiedStatus() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();
        List<LodgingUnitModel> lodgingUnitList =
                List.of(LodgingUnitModel.newBuilder().setId(1L).build());
        Map<Long, Map<String, Integer>> petCntPerLodgingPerDay = Map.of(1L, Map.of("2023-10-01", 6));

        Map<Long, LodgingOccupiedStatus> result =
                LodgingUtils.calLodgingStatus(List.of(lodgingType), lodgingUnitList, petCntPerLodgingPerDay);

        assertThat(result).containsEntry(1L, LodgingOccupiedStatus.FULLY_OCCUPIED);
    }

    @Test
    void calLodgingStatusPerDay_withZeroPetCount_shouldReturnVacant() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();

        LodgingOccupiedStatus result = LodgingUtils.calLodgingStatusPerDay(lodgingType, 0);

        assertThat(result).isEqualTo(LodgingOccupiedStatus.VACANT);
    }

    @Test
    void calLodgingStatusPerDay_withPetCountLessThanMax_shouldReturnPartiallyOccupied() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();

        LodgingOccupiedStatus result = LodgingUtils.calLodgingStatusPerDay(lodgingType, 3);

        assertThat(result).isEqualTo(LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
    }

    @Test
    void calLodgingStatusPerDay_withPetCountEqualToMax_shouldReturnFullyOccupied() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();

        LodgingOccupiedStatus result = LodgingUtils.calLodgingStatusPerDay(lodgingType, 5);

        assertThat(result).isEqualTo(LodgingOccupiedStatus.FULLY_OCCUPIED);
    }

    @Test
    void calLodgingStatusPerDay_withPetCountGreaterThanMax_shouldReturnFullyOccupied() {
        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setMaxPetNum(5).build();

        LodgingOccupiedStatus result = LodgingUtils.calLodgingStatusPerDay(lodgingType, 6);

        assertThat(result).isEqualTo(LodgingOccupiedStatus.FULLY_OCCUPIED);
    }
}

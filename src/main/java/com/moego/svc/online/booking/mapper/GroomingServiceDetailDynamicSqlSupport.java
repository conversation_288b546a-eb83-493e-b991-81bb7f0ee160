package com.moego.svc.online.booking.mapper;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class GroomingServiceDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_service_detail")
    public static final GroomingServiceDetail groomingServiceDetail = new GroomingServiceDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.id")
    public static final SqlColumn<Long> id = groomingServiceDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = groomingServiceDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.pet_id")
    public static final SqlColumn<Long> petId = groomingServiceDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of staff, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.staff_id")
    public static final SqlColumn<Long> staffId = groomingServiceDetail.staffId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.service_id")
    public static final SqlColumn<Long> serviceId = groomingServiceDetail.serviceId;

    /**
     * Database Column Remarks:
     *   The time of current service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.service_time")
    public static final SqlColumn<Integer> serviceTime = groomingServiceDetail.serviceTime;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = groomingServiceDetail.servicePrice;

    /**
     * Database Column Remarks:
     *   The start date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.start_date")
    public static final SqlColumn<String> startDate = groomingServiceDetail.startDate;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.start_time")
    public static final SqlColumn<Integer> startTime = groomingServiceDetail.startTime;

    /**
     * Database Column Remarks:
     *   The end date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.end_date")
    public static final SqlColumn<String> endDate = groomingServiceDetail.endDate;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.end_time")
    public static final SqlColumn<Integer> endTime = groomingServiceDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.created_at")
    public static final SqlColumn<Date> createdAt = groomingServiceDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = groomingServiceDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = groomingServiceDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.tax_id")
    public static final SqlColumn<Long> taxId = groomingServiceDetail.taxId;

    /**
     * Database Column Remarks:
     *   date type, see moego.models.appointment.v1.PetDetailDateType
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_service_detail.date_type")
    public static final SqlColumn<PetDetailDateType> dateType = groomingServiceDetail.dateType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_service_detail")
    public static final class GroomingServiceDetail extends AliasableSqlTable<GroomingServiceDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceTime = column("service_time", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<String> startDate = column("start_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<String> endDate = column("end_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<PetDetailDateType> dateType = column("date_type", JDBCType.INTEGER, "com.moego.svc.online.booking.typehandler.PetDetailDateTypeTypeHandler");

        public GroomingServiceDetail() {
            super("grooming_service_detail", GroomingServiceDetail::new);
        }
    }
}
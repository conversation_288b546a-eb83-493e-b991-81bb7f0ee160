package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import java.util.List;
import org.junit.jupiter.api.Test;

class TimeRangeConverterTest {

    @Test
    void toString_withValidDayTimeRangeDefList_shouldReturnJsonString() {
        List<DayTimeRangeDef> timeRanges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(10)
                        .build(),
                DayTimeRangeDef.newBuilder().setStartTime(780).setEndTime(1080).build());

        String result = TimeRangeConverter.INSTANCE.toString(timeRanges);
        String expect = "[{\"startTime\":540,\"endTime\":720,\"petCapacity\":10},{\"startTime\":780,\"endTime\":1080}]";

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void toString_withEmptyDayTimeRangeDefList_shouldReturnEmptyJsonArray() {
        List<DayTimeRangeDef> timeRanges = List.of();

        String result = TimeRangeConverter.INSTANCE.toString(timeRanges);
        String expect = "[]";

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void stringToDayTimeRangeList_withValidJsonString_shouldReturnDayTimeRangeDefList() {
        String jsonString =
                "[{\"startTime\":540,\"endTime\":720, \"petCapacity\":10},{\"startTime\":780,\"endTime\":1080}]";

        List<DayTimeRangeDef> result = TimeRangeConverter.INSTANCE.stringToDayTimeRangeList(jsonString);
        List<DayTimeRangeDef> expect = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(10)
                        .build(),
                DayTimeRangeDef.newBuilder().setStartTime(780).setEndTime(1080).build());
        assertThat(result).isEqualTo(expect);
    }

    @Test
    void stringToDayTimeRangeList_withEmptyJsonString_shouldReturnEmptyList() {
        String jsonString = "[]";

        List<DayTimeRangeDef> result = TimeRangeConverter.INSTANCE.stringToDayTimeRangeList(jsonString);

        List<DayTimeRangeDef> expect = List.of();
        assertThat(result).isEqualTo(expect);
    }
}

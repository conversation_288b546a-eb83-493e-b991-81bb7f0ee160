package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static com.moego.svc.online.booking.mapper.EvaluationTestDetailDynamicSqlSupport.evaluationTestDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.equalTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThan;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.EvaluationTestDetailMapper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class EvaluationTestDetailService {

    private final EvaluationTestDetailMapper evaluationTestDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public EvaluationTestDetail get(long id) {
        return evaluationTestDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(EvaluationTestDetail entity) {

        populate(entity);

        evaluationTestDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private void populate(EvaluationTestDetail entity) {

        check(entity);

        var bookingRequest = bookingRequestMapper
                .selectByPrimaryKey(entity.getBookingRequestId())
                .orElse(null);
        if (bookingRequest == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Booking request not found: " + entity.getBookingRequestId());
        }

        var resp = evaluationStub.getEvaluation(GetEvaluationRequest.newBuilder()
                .setBusinessId(bookingRequest.getBusinessId())
                .setId(entity.getEvaluationId())
                .build());
        if (!resp.hasEvaluationModel()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Evaluation not found: " + entity.getEvaluationId());
        }

        var evaluation = resp.getEvaluationModel();

        if (entity.getServicePrice() == null) {
            entity.setServicePrice(BigDecimal.valueOf(evaluation.getPrice()));
        }
        if (entity.getDuration() == null) {
            entity.setDuration(evaluation.getDuration());
        }
        if (StringUtils.hasText(entity.getStartDate())) {
            entity.setEndDate(entity.getStartDate());
        }
        if (entity.getStartTime() != null) {
            entity.setEndTime(entity.getStartTime() + entity.getDuration());
        }
    }

    private static void check(EvaluationTestDetail entity) {
        if (!CommonUtil.isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        }
        if (!CommonUtil.isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        }
        if (!CommonUtil.isNormal(entity.getEvaluationId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "evaluationId is required");
        }
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(EvaluationTestDetail entity) {
        entity.setUpdatedAt(new Date());
        return evaluationTestDetailMapper.updateByPrimaryKeySelective(entity);
    }

    public void updateMaxEndTime(Long bookingRequestId, List<Long> evaluationDetailIds, int maxEndTime) {
        if (CollectionUtils.isEmpty(evaluationDetailIds)) {
            return;
        }
        evaluationTestDetailMapper.update(c -> c.set(evaluationTestDetail.endTime)
                .equalTo(maxEndTime)
                .where(evaluationTestDetail.id, isIn(evaluationDetailIds))
                .and(evaluationTestDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(evaluationTestDetail.endTime, isGreaterThan(maxEndTime)));
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return evaluationTestDetailMapper.update(c -> c.set(evaluationTestDetail.deletedAt)
                .equalTo(new Date())
                .where(evaluationTestDetail.id, isEqualTo(id))
                .and(evaluationTestDetail.deletedAt, isNull()));
    }

    /**
     * List evaluation test detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId bookingRequestId
     * @return list of evaluation test detail
     */
    public List<EvaluationTestDetail> listByBookingRequestId(long bookingRequestId) {
        return evaluationTestDetailMapper.select(
                c -> c.where(evaluationTestDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(evaluationTestDetail.deletedAt, isNull()));
    }

    /**
     * List evaluation test detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of evaluation test detail
     */
    public List<EvaluationTestDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        return evaluationTestDetailMapper.select(
                c -> c.where(evaluationTestDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(evaluationTestDetail.deletedAt, isNull()));
    }

    /**
     * Count booking requests by filter and return a map of evaluation IDs to their usage status.
     * This method joins the booking_request and evaluation_test_detail tables to filter by company ID and status.
     *
     * @param companyId company ID
     * @param evaluationIds list of evaluation IDs to check
     * @return map of evaluation ID to boolean indicating if it's in use
     */
    public Map<Long, Integer> countBookingRequestByFilter(long companyId, List<Long> evaluationIds) {
        Map<Long, Integer> result = new HashMap<>();
        for (Long evaluationId : evaluationIds) {
            result.put(evaluationId, 0);
        }
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return result;
        }

        // 构建查询，通过 join 关联 booking_request 和 evaluation_test_detail 表
        var provider = select(evaluationTestDetail.evaluationId)
                .from(evaluationTestDetail)
                .join(bookingRequest)
                .on(evaluationTestDetail.bookingRequestId, equalTo(bookingRequest.id))
                .where(bookingRequest.companyId, isEqualTo(companyId))
                .and(evaluationTestDetail.evaluationId, isIn(evaluationIds))
                .and(
                        bookingRequest.status,
                        isIn(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.WAIT_LIST)))
                .and(evaluationTestDetail.deletedAt, isNull())
                .and(bookingRequest.deletedAt, isNull())
                .build()
                .render(RenderingStrategies.MYBATIS3);

        var evaluationDetails = evaluationTestDetailMapper.selectMany(provider);

        // 3. 分组统计
        Map<Long, Integer> evaluationCountMap = evaluationDetails.stream()
                .filter(detail -> evaluationIds.contains(detail.getEvaluationId()))
                .collect(Collectors.groupingBy(
                        EvaluationTestDetail::getEvaluationId,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));

        // 确保所有请求的 evaluationIds 都在返回结果
        return evaluationIds.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        evaluationId -> evaluationCountMap.getOrDefault(evaluationId, 0),
                        (o, n) -> o));
    }
}

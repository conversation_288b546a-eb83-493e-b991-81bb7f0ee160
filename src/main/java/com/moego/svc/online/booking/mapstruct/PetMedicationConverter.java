package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Mapper()
public interface PetMedicationConverter {

    String LABEL = "label";

    PetMedicationConverter INSTANCE = Mappers.getMapper(PetMedicationConverter.class);

    default BusinessPetMedicationScheduleDef toBusinessPetMedicationScheduleDef(Long petId, MedicationModel def) {
        return BusinessPetMedicationScheduleDef.newBuilder()
                .setPetId(petId)
                .setMedicationAmount(String.valueOf(def.getAmount()))
                .setMedicationUnit(def.getUnit())
                .setMedicationName(def.getMedicationName())
                .setMedicationNote(def.getNotes())
                .addAllMedicationTimes(def.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson(LABEL, time.getLabel())
                                .build())
                        .toList())
                .build();
    }
}

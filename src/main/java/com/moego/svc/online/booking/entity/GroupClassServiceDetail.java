package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Database Table Remarks:
 *   The group class service detail table
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table group_class_service_detail
 */
public class GroupClassServiceDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.pet_id")
    private Long petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.class_instance_id")
    private Long classInstanceId;

    /**
     * Database Column Remarks:
     *   The id of trainer, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.staff_id")
    private Long staffId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_id")
    private Long serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_price")
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   The date list of the group class session, ["yyyy-MM-dd"]
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.specific_dates")
    private String specificDates;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.end_time")
    private Integer endTime;

    /**
     * Database Column Remarks:
     *   Duration of each session in minutes, only for training
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.duration_per_session")
    private Integer durationPerSession;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.class_instance_id")
    public Long getClassInstanceId() {
        return classInstanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.class_instance_id")
    public void setClassInstanceId(Long classInstanceId) {
        this.classInstanceId = classInstanceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_id")
    public Long getServiceId() {
        return serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.specific_dates")
    public String getSpecificDates() {
        return specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.specific_dates")
    public void setSpecificDates(String specificDates) {
        this.specificDates = specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.duration_per_session")
    public Integer getDurationPerSession() {
        return durationPerSession;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.duration_per_session")
    public void setDurationPerSession(Integer durationPerSession) {
        this.durationPerSession = durationPerSession;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", petId=").append(petId);
        sb.append(", classInstanceId=").append(classInstanceId);
        sb.append(", staffId=").append(staffId);
        sb.append(", serviceId=").append(serviceId);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", specificDates=").append(specificDates);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", durationPerSession=").append(durationPerSession);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GroupClassServiceDetail other = (GroupClassServiceDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getClassInstanceId() == null ? other.getClassInstanceId() == null : this.getClassInstanceId().equals(other.getClassInstanceId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getServiceId() == null ? other.getServiceId() == null : this.getServiceId().equals(other.getServiceId()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getSpecificDates() == null ? other.getSpecificDates() == null : this.getSpecificDates().equals(other.getSpecificDates()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getDurationPerSession() == null ? other.getDurationPerSession() == null : this.getDurationPerSession().equals(other.getDurationPerSession()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getClassInstanceId() == null) ? 0 : getClassInstanceId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getServiceId() == null) ? 0 : getServiceId().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getSpecificDates() == null) ? 0 : getSpecificDates().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getDurationPerSession() == null) ? 0 : getDurationPerSession().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.mapstruct;

import static java.time.ZoneOffset.UTC;

import com.google.protobuf.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface TimestampConverter {
    @Named(value = "dateToTimestamp")
    default Timestamp dateToTimestamp(Date date) {
        if (date == null) {
            return Timestamp.getDefaultInstance();
        }
        return Timestamp.newBuilder().setSeconds(date.getTime() / 1000).build();
    }

    @Named(value = "timestampToDate")
    default Date timestampToDate(Timestamp timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp.getSeconds() * 1000);
    }

    default LocalDateTime toLocalDateTime(Timestamp timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), UTC);
    }

    default Timestamp toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return Timestamp.getDefaultInstance();
        }
        return Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(UTC))
                .setNanos(localDateTime.getNano())
                .build();
    }

    default com.google.type.Date map(LocalDate date) {
        return com.google.type.Date.newBuilder()
                .setYear(date.getYear())
                .setMonth(date.getMonthValue())
                .setDay(date.getDayOfMonth())
                .build();
    }
}

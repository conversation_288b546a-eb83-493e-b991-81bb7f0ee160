package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class GroomingAddOnDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    public static final GroomingAddOnDetail groomingAddOnDetail = new GroomingAddOnDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.id")
    public static final SqlColumn<Long> id = groomingAddOnDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = groomingAddOnDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of service detail
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = groomingAddOnDetail.serviceDetailId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.pet_id")
    public static final SqlColumn<Long> petId = groomingAddOnDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of staff, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.staff_id")
    public static final SqlColumn<Long> staffId = groomingAddOnDetail.staffId;

    /**
     * Database Column Remarks:
     *   The id of add-on service, aka. grooming service id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.add_on_id")
    public static final SqlColumn<Long> addOnId = groomingAddOnDetail.addOnId;

    /**
     * Database Column Remarks:
     *   The time of current service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_time")
    public static final SqlColumn<Integer> serviceTime = groomingAddOnDetail.serviceTime;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = groomingAddOnDetail.servicePrice;

    /**
     * Database Column Remarks:
     *   The start date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_date")
    public static final SqlColumn<String> startDate = groomingAddOnDetail.startDate;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_time")
    public static final SqlColumn<Integer> startTime = groomingAddOnDetail.startTime;

    /**
     * Database Column Remarks:
     *   The end date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_date")
    public static final SqlColumn<String> endDate = groomingAddOnDetail.endDate;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_time")
    public static final SqlColumn<Integer> endTime = groomingAddOnDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.created_at")
    public static final SqlColumn<Date> createdAt = groomingAddOnDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = groomingAddOnDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = groomingAddOnDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.tax_id")
    public static final SqlColumn<Long> taxId = groomingAddOnDetail.taxId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    public static final class GroomingAddOnDetail extends AliasableSqlTable<GroomingAddOnDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Long> addOnId = column("add_on_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceTime = column("service_time", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<String> startDate = column("start_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<String> endDate = column("end_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public GroomingAddOnDetail() {
            super("grooming_add_on_detail", GroomingAddOnDetail::new);
        }
    }
}
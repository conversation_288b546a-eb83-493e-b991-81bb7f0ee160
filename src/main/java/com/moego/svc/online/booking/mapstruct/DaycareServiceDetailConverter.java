package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateDaycareServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceDetailRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DaycareServiceDetailConverter {
    DaycareServiceDetailConverter INSTANCE = Mappers.getMapper(DaycareServiceDetailConverter.class);

    @Mapping(target = "specificDates", ignore = true)
    DaycareServiceDetailModel entityToModel(DaycareServiceDetail entity);

    @Mapping(target = "specificDates", ignore = true)
    DaycareServiceDetail createRequestToEntity(CreateDaycareServiceDetailRequest createRequest);

    default DaycareServiceDetail updateRequestToEntity(UpdateDaycareServiceDetailRequest updateRequest) {
        var detail = new DaycareServiceDetail();
        detail.setId(updateRequest.getId());
        if (updateRequest.hasSpecificDates()) {
            detail.setSpecificDates(
                    JsonUtil.toJson(updateRequest.getSpecificDates().getValuesList()));
        }
        if (updateRequest.hasStartTime()) {
            detail.setStartTime(updateRequest.getStartTime());
        }
        if (updateRequest.hasEndTime()) {
            detail.setEndTime(updateRequest.getEndTime());
        }
        return detail;
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    @AfterMapping
    default void setSpecificDates(
            DaycareServiceDetail source, @MappingTarget DaycareServiceDetailModel.Builder target) {
        Optional.ofNullable(source.getSpecificDates())
                .filter(StringUtils::hasText)
                .map(e -> JsonUtil.toList(e, String.class))
                .ifPresent(target::addAllSpecificDates);
    }

    @AfterMapping
    default void setSpecificDates(DaycareServiceDetailModel source, @MappingTarget DaycareServiceDetail target) {
        target.setSpecificDates(JsonUtil.toJson(source.getSpecificDatesList()));
    }

    @AfterMapping
    default void setSpecificDates(
            CreateDaycareServiceDetailRequest source, @MappingTarget DaycareServiceDetail target) {
        target.setSpecificDates(JsonUtil.toJson(source.getSpecificDatesList()));
    }
}

package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Mapper()
public interface PetFeedingConverter {

    String LABEL = "label";

    PetFeedingConverter INSTANCE = Mappers.getMapper(PetFeedingConverter.class);

    default BusinessPetFeedingScheduleDef toBusinessPetFeedingScheduleDef(Long petId, FeedingModel def) {
        return BusinessPetFeedingScheduleDef.newBuilder()
                .setPetId(petId)
                .setFeedingAmount(String.valueOf(def.getAmount()))
                .setFeedingUnit(def.getUnit())
                .setFeedingType(def.getFoodType())
                .setFeedingSource(def.getFoodSource())
                .setFeedingInstruction(def.getInstruction())
                .setFeedingNote(def.getNote())
                .addAllFeedingTimes(def.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson(LABEL, time.getLabel())
                                .build())
                        .toList())
                .build();
    }
}

package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.AutomationSettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.AutomationSetting;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AutomationSettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<AutomationSettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, companyId, serviceItemType, enableAutoAccept, autoAcceptCondition, createdAt, updatedAt, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<AutomationSetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<AutomationSetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AutomationSettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="enable_auto_accept", property="enableAutoAccept", jdbcType=JdbcType.BIT),
        @Result(column="auto_accept_condition", property="autoAcceptCondition", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.BIGINT)
    })
    List<AutomationSetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AutomationSettingResult")
    Optional<AutomationSetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int insertMultiple(Collection<AutomationSetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, automationSetting, c ->
            c.map(businessId).toProperty("businessId")
            .map(companyId).toProperty("companyId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(enableAutoAccept).toProperty("enableAutoAccept")
            .map(autoAcceptCondition).toProperty("autoAcceptCondition")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int insertSelective(AutomationSetting row) {
        return MyBatis3Utils.insert(this::insert, row, automationSetting, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(enableAutoAccept).toPropertyWhenPresent("enableAutoAccept", row::getEnableAutoAccept)
            .map(autoAcceptCondition).toPropertyWhenPresent("autoAcceptCondition", row::getAutoAcceptCondition)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default Optional<AutomationSetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default List<AutomationSetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default List<AutomationSetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default Optional<AutomationSetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, automationSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(AutomationSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(companyId).equalTo(row::getCompanyId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(enableAutoAccept).equalTo(row::getEnableAutoAccept)
                .set(autoAcceptCondition).equalTo(row::getAutoAcceptCondition)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AutomationSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(enableAutoAccept).equalToWhenPresent(row::getEnableAutoAccept)
                .set(autoAcceptCondition).equalToWhenPresent(row::getAutoAcceptCondition)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    default int updateByPrimaryKeySelective(AutomationSetting row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(enableAutoAccept).equalToWhenPresent(row::getEnableAutoAccept)
            .set(autoAcceptCondition).equalToWhenPresent(row::getAutoAcceptCondition)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}
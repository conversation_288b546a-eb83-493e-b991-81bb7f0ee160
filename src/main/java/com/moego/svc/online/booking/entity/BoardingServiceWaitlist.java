package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table boarding_service_waitlist
 */
public class BoardingServiceWaitlist {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.booking_request_id")
    private Long bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.service_detail_id")
    private Long serviceDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.start_date")
    private LocalDate startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.end_date")
    private LocalDate endDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.service_detail_id")
    public Long getServiceDetailId() {
        return serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.service_detail_id")
    public void setServiceDetailId(Long serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.start_date")
    public LocalDate getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.start_date")
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.end_date")
    public LocalDate getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.end_date")
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_waitlist")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", serviceDetailId=").append(serviceDetailId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_waitlist")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BoardingServiceWaitlist other = (BoardingServiceWaitlist) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getServiceDetailId() == null ? other.getServiceDetailId() == null : this.getServiceDetailId().equals(other.getServiceDetailId()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_waitlist")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getServiceDetailId() == null) ? 0 : getServiceDetailId().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
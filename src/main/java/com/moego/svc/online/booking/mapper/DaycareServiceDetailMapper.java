package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.DaycareServiceDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DaycareServiceDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DaycareServiceDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, petId, serviceId, specificDates, servicePrice, taxId, maxDuration, startTime, endTime, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DaycareServiceDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DaycareServiceDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DaycareServiceDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="specific_dates", property="specificDates", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.BIGINT),
        @Result(column="max_duration", property="maxDuration", jdbcType=JdbcType.INTEGER),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<DaycareServiceDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DaycareServiceDetailResult")
    Optional<DaycareServiceDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int insertMultiple(Collection<DaycareServiceDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, daycareServiceDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(petId).toProperty("petId")
            .map(serviceId).toProperty("serviceId")
            .map(specificDates).toProperty("specificDates")
            .map(servicePrice).toProperty("servicePrice")
            .map(taxId).toProperty("taxId")
            .map(maxDuration).toProperty("maxDuration")
            .map(startTime).toProperty("startTime")
            .map(endTime).toProperty("endTime")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int insertSelective(DaycareServiceDetail row) {
        return MyBatis3Utils.insert(this::insert, row, daycareServiceDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
            .map(maxDuration).toPropertyWhenPresent("maxDuration", row::getMaxDuration)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default Optional<DaycareServiceDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default List<DaycareServiceDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default List<DaycareServiceDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default Optional<DaycareServiceDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, daycareServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(DaycareServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(petId).equalTo(row::getPetId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(specificDates).equalTo(row::getSpecificDates)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(taxId).equalTo(row::getTaxId)
                .set(maxDuration).equalTo(row::getMaxDuration)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DaycareServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(taxId).equalToWhenPresent(row::getTaxId)
                .set(maxDuration).equalToWhenPresent(row::getMaxDuration)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    default int updateByPrimaryKeySelective(DaycareServiceDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .set(maxDuration).equalToWhenPresent(row::getMaxDuration)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.DogWalkingServiceDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.DogWalkingServiceDetail;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DogWalkingServiceDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DogWalkingServiceDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, petId, staffId, serviceId, serviceTime, servicePrice, taxId, startDate, startTime, endDate, endTime, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DogWalkingServiceDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DogWalkingServiceDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DogWalkingServiceDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_time", property="serviceTime", jdbcType=JdbcType.INTEGER),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<DogWalkingServiceDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DogWalkingServiceDetailResult")
    Optional<DogWalkingServiceDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int insertMultiple(Collection<DogWalkingServiceDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, dogWalkingServiceDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(petId).toProperty("petId")
            .map(staffId).toProperty("staffId")
            .map(serviceId).toProperty("serviceId")
            .map(serviceTime).toProperty("serviceTime")
            .map(servicePrice).toProperty("servicePrice")
            .map(taxId).toProperty("taxId")
            .map(startDate).toProperty("startDate")
            .map(startTime).toProperty("startTime")
            .map(endDate).toProperty("endDate")
            .map(endTime).toProperty("endTime")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int insertSelective(DogWalkingServiceDetail row) {
        return MyBatis3Utils.insert(this::insert, row, dogWalkingServiceDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(serviceTime).toPropertyWhenPresent("serviceTime", row::getServiceTime)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default Optional<DogWalkingServiceDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default List<DogWalkingServiceDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default List<DogWalkingServiceDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default Optional<DogWalkingServiceDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dogWalkingServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(DogWalkingServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(petId).equalTo(row::getPetId)
                .set(staffId).equalTo(row::getStaffId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(serviceTime).equalTo(row::getServiceTime)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(taxId).equalTo(row::getTaxId)
                .set(startDate).equalTo(row::getStartDate)
                .set(startTime).equalTo(row::getStartTime)
                .set(endDate).equalTo(row::getEndDate)
                .set(endTime).equalTo(row::getEndTime)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DogWalkingServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(serviceTime).equalToWhenPresent(row::getServiceTime)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(taxId).equalToWhenPresent(row::getTaxId)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    default int updateByPrimaryKeySelective(DogWalkingServiceDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(serviceTime).equalToWhenPresent(row::getServiceTime)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.AutomationSettingDynamicSqlSupport.automationSetting;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.ProfileUpdateCondition;
import com.moego.idl.models.online_booking.v1.VaccineStatusCondition;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.AutomationSetting;
import com.moego.svc.online.booking.mapper.AutomationSettingMapper;
import jakarta.annotation.Nonnull;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Service
@RequiredArgsConstructor
public class AutomationSettingService {

    private static final AutomationConditionDef DEFAULT_AUTOMATION_CONDITION = AutomationConditionDef.newBuilder()
            .setAcceptClientType(AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH)
            .setProfileUpdateCondition(ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL)
            .setVaccineStatusCondition(VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL)
            .build();

    private final AutomationSettingMapper automationSettingMapper;

    /**
     * Get automation setting, if not exist, return default setting.
     *
     * @param companyId company id
     * @param businessId business id
     * @param serviceItemType service item type
     * @return automation setting
     */
    @Nonnull
    public AutomationSetting getAutomationSetting(long companyId, long businessId, ServiceItemType serviceItemType) {
        Optional<AutomationSetting> result = getAutomationSettingOptional(companyId, businessId, serviceItemType);
        if (result.isPresent()) {
            return result.get();
        }

        AutomationSetting settingPO;
        settingPO = new AutomationSetting();
        settingPO.setBusinessId(businessId);
        settingPO.setCompanyId(companyId);
        settingPO.setServiceItemType(serviceItemType.getNumber());
        settingPO.setEnableAutoAccept(false);
        settingPO.setAutoAcceptCondition(JsonUtil.toJson(DEFAULT_AUTOMATION_CONDITION));
        settingPO.setCreatedAt(DateUtil.getNowDate());
        settingPO.setUpdatedAt(DateUtil.getNowDate());
        return settingPO;
    }

    private Optional<AutomationSetting> getAutomationSettingOptional(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        return automationSettingMapper.selectOne(c -> c.where(automationSetting.businessId, isEqualTo(businessId))
                .and(automationSetting.companyId, isEqualTo(companyId))
                .and(automationSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())));
    }
}

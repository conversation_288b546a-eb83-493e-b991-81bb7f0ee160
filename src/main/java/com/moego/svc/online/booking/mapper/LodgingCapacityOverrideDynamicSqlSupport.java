package com.moego.svc.online.booking.mapper;

import com.moego.idl.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange;
import com.moego.idl.models.online_booking.v1.CapacityOverrideUnitType;
import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class LodgingCapacityOverrideDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    public static final LodgingCapacityOverride lodgingCapacityOverride = new LodgingCapacityOverride();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.id")
    public static final SqlColumn<Long> id = lodgingCapacityOverride.id;

    /**
     * Database Column Remarks:
     *   The id of lodging_capacity_setting
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.setting_id")
    public static final SqlColumn<Long> settingId = lodgingCapacityOverride.settingId;

    /**
     * Database Column Remarks:
     *   array of date range json
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.date_ranges")
    public static final SqlColumn<List<CapacityDateRange>> dateRanges = lodgingCapacityOverride.dateRanges;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.capacity")
    public static final SqlColumn<Integer> capacity = lodgingCapacityOverride.capacity;

    /**
     * Database Column Remarks:
     *   1.pet number  2.percent
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.unit_type")
    public static final SqlColumn<CapacityOverrideUnitType> unitType = lodgingCapacityOverride.unitType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = lodgingCapacityOverride.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = lodgingCapacityOverride.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = lodgingCapacityOverride.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    public static final class LodgingCapacityOverride extends AliasableSqlTable<LodgingCapacityOverride> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> settingId = column("setting_id", JDBCType.BIGINT);

        public final SqlColumn<List<CapacityDateRange>> dateRanges = column("date_ranges", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.CapacityDateRangeListTypeHandler");

        public final SqlColumn<Integer> capacity = column("capacity", JDBCType.INTEGER);

        public final SqlColumn<CapacityOverrideUnitType> unitType = column("unit_type", JDBCType.SMALLINT, "com.moego.svc.online.booking.typehandler.CapacityOverrideUnitTypeHandler");

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public LodgingCapacityOverride() {
            super("lodging_capacity_override", LodgingCapacityOverride::new);
        }
    }
}
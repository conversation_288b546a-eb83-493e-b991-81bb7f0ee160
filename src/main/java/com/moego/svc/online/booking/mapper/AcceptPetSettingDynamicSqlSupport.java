package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AcceptPetSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_pet_setting")
    public static final AcceptPetSetting acceptPetSetting = new AcceptPetSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.id")
    public static final SqlColumn<Long> id = acceptPetSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.business_id")
    public static final SqlColumn<Long> businessId = acceptPetSetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.company_id")
    public static final SqlColumn<Long> companyId = acceptPetSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = acceptPetSetting.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.accepted_pet_types")
    public static final SqlColumn<Integer[]> acceptedPetTypes = acceptPetSetting.acceptedPetTypes;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.created_at")
    public static final SqlColumn<Date> createdAt = acceptPetSetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.updated_at")
    public static final SqlColumn<Date> updatedAt = acceptPetSetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_pet_setting.update_by")
    public static final SqlColumn<Long> updateBy = acceptPetSetting.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_pet_setting")
    public static final class AcceptPetSetting extends AliasableSqlTable<AcceptPetSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Integer[]> acceptedPetTypes = column("accepted_pet_types", JDBCType.ARRAY, "org.apache.ibatis.type.ArrayTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateBy = column("update_by", JDBCType.BIGINT);

        public AcceptPetSetting() {
            super("accept_pet_setting", AcceptPetSetting::new);
        }
    }
}
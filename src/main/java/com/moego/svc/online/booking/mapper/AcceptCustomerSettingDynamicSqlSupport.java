package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AcceptCustomerSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    public static final AcceptCustomerSetting acceptCustomerSetting = new AcceptCustomerSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.id")
    public static final SqlColumn<Long> id = acceptCustomerSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.business_id")
    public static final SqlColumn<Long> businessId = acceptCustomerSetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.company_id")
    public static final SqlColumn<Long> companyId = acceptCustomerSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = acceptCustomerSetting.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.accepted_customer_type")
    public static final SqlColumn<Integer> acceptedCustomerType = acceptCustomerSetting.acceptedCustomerType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.created_at")
    public static final SqlColumn<Date> createdAt = acceptCustomerSetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.updated_at")
    public static final SqlColumn<Date> updatedAt = acceptCustomerSetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: accept_customer_setting.update_by")
    public static final SqlColumn<Long> updateBy = acceptCustomerSetting.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    public static final class AcceptCustomerSetting extends AliasableSqlTable<AcceptCustomerSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> acceptedCustomerType = column("accepted_customer_type", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateBy = column("update_by", JDBCType.BIGINT);

        public AcceptCustomerSetting() {
            super("accept_customer_setting", AcceptCustomerSetting::new);
        }
    }
}
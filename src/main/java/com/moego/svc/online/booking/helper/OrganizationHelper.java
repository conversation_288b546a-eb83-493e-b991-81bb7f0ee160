package com.moego.svc.online.booking.helper;

import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OrganizationHelper {

    private final StaffServiceGrpc.StaffServiceBlockingStub staffStub;

    /**
     * return staffs for business.
     */
    public List<StaffBasicView> listStaffForBusiness(long companyId, long businessId, Boolean isShowOnCalendar) {
        var staffs = staffStub
                .getStaffsByWorkingLocation(GetStaffsByWorkingLocationRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setWorkingLocationId(businessId)
                        .build())
                .getStaffsList();
        if (isShowOnCalendar == null) {
            return staffs;
        }

        return staffs.stream()
                .filter(k -> Objects.equals(k.getIsShowOnCalendar(), isShowOnCalendar))
                .toList();
    }
}

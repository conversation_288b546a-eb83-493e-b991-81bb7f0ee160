package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DaycareServiceDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    public static final DaycareServiceDetail daycareServiceDetail = new DaycareServiceDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.id")
    public static final SqlColumn<Long> id = daycareServiceDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = daycareServiceDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.pet_id")
    public static final SqlColumn<Long> petId = daycareServiceDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_id")
    public static final SqlColumn<Long> serviceId = daycareServiceDetail.serviceId;

    /**
     * Database Column Remarks:
     *   The specific dates of the daycare service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.specific_dates")
    public static final SqlColumn<String> specificDates = daycareServiceDetail.specificDates;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = daycareServiceDetail.servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.tax_id")
    public static final SqlColumn<Long> taxId = daycareServiceDetail.taxId;

    /**
     * Database Column Remarks:
     *   The max duration of the daycare service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.max_duration")
    public static final SqlColumn<Integer> maxDuration = daycareServiceDetail.maxDuration;

    /**
     * Database Column Remarks:
     *   The arrival time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.start_time")
    public static final SqlColumn<Integer> startTime = daycareServiceDetail.startTime;

    /**
     * Database Column Remarks:
     *   The pickup time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.end_time")
    public static final SqlColumn<Integer> endTime = daycareServiceDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.created_at")
    public static final SqlColumn<Date> createdAt = daycareServiceDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = daycareServiceDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = daycareServiceDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    public static final class DaycareServiceDetail extends AliasableSqlTable<DaycareServiceDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<String> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> maxDuration = column("max_duration", JDBCType.INTEGER);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public DaycareServiceDetail() {
            super("daycare_service_detail", DaycareServiceDetail::new);
        }
    }
}
package com.moego.svc.online.booking.typehandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.type.Date;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.postgresql.util.PGobject;

/**
 * 处理数据库 JSONB 类型与 List<CapacityDateRange> 之间的转换
 */
public class CapacityDateRangeListTypeHandler extends BaseTypeHandler<List<CapacityDateRange>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<CapacityDateRange> parameter, JdbcType jdbcType)
            throws SQLException {
        // Java 对象 -> JSONB
        PGobject jsonObject = new PGobject();
        jsonObject.setType("jsonb");

        try {
            // 构建 JSONB 格式的字符串
            ArrayNode jsonArray = objectMapper.createArrayNode();
            for (CapacityDateRange dateRange : parameter) {
                ObjectNode rangeObject = objectMapper.createObjectNode();

                // 构建 startDate
                ObjectNode startDateObj = objectMapper.createObjectNode();
                startDateObj.put("year", dateRange.getStartDate().getYear());
                startDateObj.put("month", dateRange.getStartDate().getMonth());
                startDateObj.put("day", dateRange.getStartDate().getDay());
                rangeObject.set("startDate", startDateObj);

                // 构建 endDate
                ObjectNode endDateObj = objectMapper.createObjectNode();
                endDateObj.put("year", dateRange.getEndDate().getYear());
                endDateObj.put("month", dateRange.getEndDate().getMonth());
                endDateObj.put("day", dateRange.getEndDate().getDay());
                rangeObject.set("endDate", endDateObj);

                jsonArray.add(rangeObject);
            }

            jsonObject.setValue(objectMapper.writeValueAsString(jsonArray));
            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            throw new SQLException("Error converting List<CapacityDateRange> to JSON", e);
        }
    }

    @Override
    public List<CapacityDateRange> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        // JSONB -> Java 对象
        String jsonStr = rs.getString(columnName);
        return parseJsonToCapacityDateRanges(jsonStr);
    }

    @Override
    public List<CapacityDateRange> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonStr = rs.getString(columnIndex);
        return parseJsonToCapacityDateRanges(jsonStr);
    }

    @Override
    public List<CapacityDateRange> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonStr = cs.getString(columnIndex);
        return parseJsonToCapacityDateRanges(jsonStr);
    }

    private List<CapacityDateRange> parseJsonToCapacityDateRanges(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            List<CapacityDateRange> result = new ArrayList<>();
            ArrayNode jsonArray = (ArrayNode) objectMapper.readTree(jsonStr);

            for (int i = 0; i < jsonArray.size(); i++) {
                ObjectNode json = (ObjectNode) jsonArray.get(i);
                result.add(CapacityDateRange.newBuilder()
                        .setStartDate(toGoogleDate(json.get("startDate")))
                        .setEndDate(toGoogleDate(json.get("endDate")))
                        .build());
            }

            return result;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON to CapacityDateRange list", e);
        }
    }

    private Date toGoogleDate(com.fasterxml.jackson.databind.JsonNode json) {
        return Date.newBuilder()
                .setYear(json.get("year").asInt())
                .setMonth(json.get("month").asInt())
                .setDay(json.get("day").asInt())
                .build();
    }
}

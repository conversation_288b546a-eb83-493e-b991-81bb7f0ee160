package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class GroomingAutoAssignDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    public static final GroomingAutoAssign groomingAutoAssign = new GroomingAutoAssign();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.id")
    public static final SqlColumn<Long> id = groomingAutoAssign.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = groomingAutoAssign.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of staff, auto assign staff id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.staff_id")
    public static final SqlColumn<Long> staffId = groomingAutoAssign.staffId;

    /**
     * Database Column Remarks:
     *   auto assign appointment time, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.start_time")
    public static final SqlColumn<Integer> startTime = groomingAutoAssign.startTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.created_at")
    public static final SqlColumn<Date> createdAt = groomingAutoAssign.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.updated_at")
    public static final SqlColumn<Date> updatedAt = groomingAutoAssign.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.deleted_at")
    public static final SqlColumn<Date> deletedAt = groomingAutoAssign.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_auto_assign.grooming_service_detail_id")
    public static final SqlColumn<Long> groomingServiceDetailId = groomingAutoAssign.groomingServiceDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    public static final class GroomingAutoAssign extends AliasableSqlTable<GroomingAutoAssign> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> groomingServiceDetailId = column("grooming_service_detail_id", JDBCType.BIGINT);

        public GroomingAutoAssign() {
            super("grooming_auto_assign", GroomingAutoAssign::new);
        }
    }
}
package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 * Database Table Remarks:
 *   Appointment BookingRequest mapping
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_request_appointment_mapping
 */
public class BookingRequestAppointmentMapping {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   booking request id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.appointment_id")
    private Long appointmentId;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.created_at")
    private Date createdAt;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.appointment_id")
    public Long getAppointmentId() {
        return appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.appointment_id")
    public void setAppointmentId(Long appointmentId) {
        this.appointmentId = appointmentId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", appointmentId=").append(appointmentId);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingRequestAppointmentMapping other = (BookingRequestAppointmentMapping) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getAppointmentId() == null ? other.getAppointmentId() == null : this.getAppointmentId().equals(other.getAppointmentId()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getAppointmentId() == null) ? 0 : getAppointmentId().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.svc.online.booking.entity.DogWalkingServiceDetail;
import com.moego.svc.online.booking.mapper.DogWalkingServiceDetailMapper;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;

@ExtendWith(MockitoExtension.class)
class DogWalkingServiceDetailServiceTest {

    @Mock
    private DogWalkingServiceDetailMapper dogWalkingServiceDetailMapper;

    @InjectMocks
    private DogWalkingServiceDetailService dogWalkingServiceDetailService;

    @Test
    void get() {
        // Arrange
        long id = 1L;
        var detail = new DogWalkingServiceDetail();
        detail.setId(id);
        detail.setDeletedAt(null);

        when(dogWalkingServiceDetailMapper.selectByPrimaryKey(id)).thenReturn(Optional.of(detail));

        // Act
        var result = dogWalkingServiceDetailService.get(id);

        // Assert
        var expected = detail;
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void get_NotFound() {
        // Arrange
        var id = 2L;
        when(dogWalkingServiceDetailMapper.selectByPrimaryKey(id)).thenReturn(Optional.empty());

        // Act
        var result = dogWalkingServiceDetailService.get(id);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void listByBookingRequestId() {
        // Arrange
        var bookingRequestId = 1L;
        var detail = new DogWalkingServiceDetail();
        detail.setBookingRequestId(bookingRequestId);
        detail.setDeletedAt(null);

        when(dogWalkingServiceDetailMapper.select(any(SelectDSLCompleter.class)))
                .thenReturn(List.of(detail));

        // Act
        var result = dogWalkingServiceDetailService.listByBookingRequestId(bookingRequestId);

        // Assert
        var expected = List.of(detail);
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testListByBookingRequestId() {
        // Arrange
        var bookingRequestIds = List.of(1L, 2L);
        var detail1 = new DogWalkingServiceDetail();
        detail1.setBookingRequestId(1L);
        detail1.setDeletedAt(null);
        var detail2 = new DogWalkingServiceDetail();
        detail2.setBookingRequestId(2L);
        detail2.setDeletedAt(null);

        when(dogWalkingServiceDetailMapper.select(any(SelectDSLCompleter.class)))
                .thenReturn(List.of(detail1, detail2));

        // Act
        var result = dogWalkingServiceDetailService.listByBookingRequestId(bookingRequestIds);

        // Assert
        var expected = List.of(detail1, detail2);
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void testListByBookingRequestId_Empty() {
        // Arrange
        List<Long> bookingRequestIds = List.of();

        // Act
        var result = dogWalkingServiceDetailService.listByBookingRequestId(bookingRequestIds);

        // Assert
        var expected = List.of();
        assertThat(result).isEqualTo(expected);
    }

    @Test
    void delete_Empty() {
        // Arrange
        List<Long> ids = List.of();

        // Act
        var result = dogWalkingServiceDetailService.delete(ids);

        // Assert
        var expected = 0;
        assertThat(result).isEqualTo(expected);
    }
}

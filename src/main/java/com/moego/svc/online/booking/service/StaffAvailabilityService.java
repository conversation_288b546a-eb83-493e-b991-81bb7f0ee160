package com.moego.svc.online.booking.service;

import static com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR;
import static com.moego.svc.online.booking.mapper.StaffAvailabilityDynamicSqlSupport.staffAvailability;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;

import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.StaffAvailability;
import com.moego.svc.online.booking.mapper.StaffAvailabilityMapper;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StaffAvailabilityService {

    private final StaffAvailabilityMapper staffAvailabilityMapper;

    public void insert(StaffAvailability staffAvailability) {
        staffAvailabilityMapper.insertSelective(staffAvailability);
    }

    public void batchUpdate(List<StaffAvailability> staffAvailabilities) {
        staffAvailabilities.forEach(staffAvailabilityEntity -> {
            staffAvailabilityMapper.update(c -> c.set(staffAvailability.isAvailable)
                    .equalToWhenPresent(staffAvailabilityEntity::getIsAvailable)
                    .set(staffAvailability.updatedAt)
                    .equalTo(new Date())
                    .where(staffAvailability.companyId, isEqualTo(staffAvailabilityEntity::getCompanyId))
                    .and(staffAvailability.businessId, isEqualTo(staffAvailabilityEntity::getBusinessId))
                    .and(staffAvailability.staffId, isEqualTo(staffAvailabilityEntity::getStaffId)));
        });
    }

    /*
     * Get staff availability by business id
     * @param businessId not null
     * @param staffIds nullable
     * @return List<StaffAvailability>
     */
    public List<StaffAvailability> getStaffAvailabilities(Long businessId, List<Long> staffIds) {
        if (businessId == null) {
            throw ExceptionUtil.bizException(CODE_PARAMS_ERROR, "businessId is invalid");
        }
        return staffAvailabilityMapper.select(c -> c.where(staffAvailability.businessId, isEqualTo(businessId))
                .and(staffAvailability.staffId, isInWhenPresent(staffIds)));
    }

    public StaffAvailability getStaffAvailability(Long businessId, Long staffId) {
        return staffAvailabilityMapper
                .selectOne(c -> c.where(staffAvailability.businessId, isEqualTo(businessId))
                        .and(staffAvailability.staffId, isEqualTo(staffId)))
                .orElse(null);
    }
}

package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Database Table Remarks:
 *   The daycare add-on detail
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table daycare_add_on_detail
 */
public class DaycareAddOnDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of daycare service detail, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_detail_id")
    private Long serviceDetailId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   The id of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.add_on_id")
    private Long addOnId;

    /**
     * Database Column Remarks:
     *   The specific dates of add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.specific_dates")
    private String specificDates;

    /**
     * Database Column Remarks:
     *   The flag to indicate if the add-on service is everyday
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.is_everyday")
    private Boolean isEveryday;

    /**
     * Database Column Remarks:
     *   The price of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_price")
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   The id of tax, associated with the current add-on
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.tax_id")
    private Long taxId;

    /**
     * Database Column Remarks:
     *   The duration of current add-on service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.duration")
    private Integer duration;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.deleted_at")
    private Date deletedAt;

    /**
     * Database Column Remarks:
     *   Number of times per day this add-on is performed
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.quantity_per_day")
    private Integer quantityPerDay;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_detail_id")
    public Long getServiceDetailId() {
        return serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_detail_id")
    public void setServiceDetailId(Long serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.add_on_id")
    public Long getAddOnId() {
        return addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.add_on_id")
    public void setAddOnId(Long addOnId) {
        this.addOnId = addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.specific_dates")
    public String getSpecificDates() {
        return specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.specific_dates")
    public void setSpecificDates(String specificDates) {
        this.specificDates = specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.is_everyday")
    public Boolean getIsEveryday() {
        return isEveryday;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.is_everyday")
    public void setIsEveryday(Boolean isEveryday) {
        this.isEveryday = isEveryday;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.tax_id")
    public Long getTaxId() {
        return taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.tax_id")
    public void setTaxId(Long taxId) {
        this.taxId = taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.duration")
    public Integer getDuration() {
        return duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.duration")
    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.quantity_per_day")
    public Integer getQuantityPerDay() {
        return quantityPerDay;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_add_on_detail.quantity_per_day")
    public void setQuantityPerDay(Integer quantityPerDay) {
        this.quantityPerDay = quantityPerDay;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", serviceDetailId=").append(serviceDetailId);
        sb.append(", petId=").append(petId);
        sb.append(", addOnId=").append(addOnId);
        sb.append(", specificDates=").append(specificDates);
        sb.append(", isEveryday=").append(isEveryday);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", taxId=").append(taxId);
        sb.append(", duration=").append(duration);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", quantityPerDay=").append(quantityPerDay);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DaycareAddOnDetail other = (DaycareAddOnDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getServiceDetailId() == null ? other.getServiceDetailId() == null : this.getServiceDetailId().equals(other.getServiceDetailId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getAddOnId() == null ? other.getAddOnId() == null : this.getAddOnId().equals(other.getAddOnId()))
            && (this.getSpecificDates() == null ? other.getSpecificDates() == null : this.getSpecificDates().equals(other.getSpecificDates()))
            && (this.getIsEveryday() == null ? other.getIsEveryday() == null : this.getIsEveryday().equals(other.getIsEveryday()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getTaxId() == null ? other.getTaxId() == null : this.getTaxId().equals(other.getTaxId()))
            && (this.getDuration() == null ? other.getDuration() == null : this.getDuration().equals(other.getDuration()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getQuantityPerDay() == null ? other.getQuantityPerDay() == null : this.getQuantityPerDay().equals(other.getQuantityPerDay()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_add_on_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getServiceDetailId() == null) ? 0 : getServiceDetailId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getAddOnId() == null) ? 0 : getAddOnId().hashCode());
        result = prime * result + ((getSpecificDates() == null) ? 0 : getSpecificDates().hashCode());
        result = prime * result + ((getIsEveryday() == null) ? 0 : getIsEveryday().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getTaxId() == null) ? 0 : getTaxId().hashCode());
        result = prime * result + ((getDuration() == null) ? 0 : getDuration().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getQuantityPerDay() == null) ? 0 : getQuantityPerDay().hashCode());
        return result;
    }
}
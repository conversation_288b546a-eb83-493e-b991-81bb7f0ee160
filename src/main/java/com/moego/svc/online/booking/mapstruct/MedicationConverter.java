package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.online_booking.v1.CreateMedicationRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.Medication;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {EnumConverter.class})
public interface MedicationConverter {
    MedicationConverter INSTANCE = Mappers.getMapper(MedicationConverter.class);

    @Mapping(target = "time", ignore = true)
    @Mapping(target = "amountStr", expression = "java(toAmountStr(entity))")
    @Mapping(target = "selectedDate.dateType", source = "dateType")
    @Mapping(target = "selectedDate.specificDates", source = "specificDates")
    MedicationModel entityToModel(Medication entity);

    default List<MedicationModel> entityToModel(List<Medication> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return List.of();
        }
        return entities.stream().map(this::entityToModel).toList();
    }

    default String toAmountStr(Medication entity) {
        if (StringUtils.hasText(entity.getAmountStr())) {
            return entity.getAmountStr();
        }
        return entity.getAmount().toString();
    }

    default Medication createRequestToEntity(
            CreateMedicationRequest createRequest,
            Long bookingRequestId,
            Long serviceDetailId,
            ServiceItemType serviceItemType) {
        Medication medication = new Medication();

        if (createRequest.hasAmount()) {
            medication.setAmount(BigDecimal.valueOf(createRequest.getAmount()));
        }
        if (createRequest.hasUnit()) {
            medication.setUnit(createRequest.getUnit());
        }
        if (createRequest.hasMedicationName()) {
            medication.setMedicationName(createRequest.getMedicationName());
        }
        if (createRequest.hasNotes()) {
            medication.setNotes(createRequest.getNotes());
        }
        if (createRequest.hasCreatedAt()) {
            medication.setCreatedAt(pbTimestampToDate(createRequest.getCreatedAt()));
        }
        if (createRequest.hasUpdatedAt()) {
            medication.setUpdatedAt(pbTimestampToDate(createRequest.getUpdatedAt()));
        }
        if (createRequest.hasAmountStr()) {
            medication.setAmountStr(createRequest.getAmountStr());
        }
        if (createRequest.hasSelectedDate()) {
            var selectedDate = createRequest.getSelectedDate();
            medication.setDateType(selectedDate.getDateType());
            if (selectedDate.getDateType().equals(FeedingMedicationScheduleDateType.SPECIFIC_DATE)) {
                medication.setSpecificDates(selectedDate.getSpecificDatesList());
            } else {
                medication.setSpecificDates(List.of());
            }
        }

        medication.setTime(JsonUtil.toJson(createRequest.getTimeList()));
        medication.setBookingRequestId(bookingRequestId);
        medication.setServiceDetailId(serviceDetailId);
        medication.setServiceDetailType(serviceItemType.getNumber());

        return medication;
    }

    default List<Medication> createRequestToEntity(
            List<CreateMedicationRequest> creates,
            Long bookingRequestId,
            Long serviceDetailId,
            ServiceItemType serviceItemType) {
        if (CollectionUtils.isEmpty(creates)) {
            return List.of();
        }
        return creates.stream()
                .map(e -> createRequestToEntity(e, bookingRequestId, serviceDetailId, serviceItemType))
                .toList();
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    @AfterMapping
    default void setTime(Medication source, @MappingTarget MedicationModel.Builder target) {
        Optional.ofNullable(source.getTime())
                .filter(StringUtils::hasText)
                .map(e -> JsonUtil.toList(e, Schedule.class).stream()
                        .map(it -> {
                            MedicationModel.MedicationSchedule.Builder b =
                                    MedicationModel.MedicationSchedule.newBuilder();
                            Optional.ofNullable(it.label()).ifPresent(b::setLabel);
                            Optional.ofNullable(it.time()).ifPresent(b::setTime);
                            return b.build();
                        })
                        .toList())
                .ifPresent(target::addAllTime);
    }
}

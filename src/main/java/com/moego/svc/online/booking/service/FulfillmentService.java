package com.moego.svc.online.booking.service;

import com.google.protobuf.Timestamp;
import com.moego.idl.models.fulfillment.v1.FulfillmentCreateDef;
import com.moego.idl.models.fulfillment.v1.GroupClassCreateDef;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailCreateDef;
import com.moego.idl.models.fulfillment.v1.Source;
import com.moego.idl.models.fulfillment.v1.Status;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.service.fulfillment.v1.CreateFulfillmentRequest;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.svc.online.booking.client.OrganizationClient;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Service
@RequiredArgsConstructor
public class FulfillmentService {

    private final OrganizationClient organizationClient;
    private final FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentStub;

    public List<Long> createFulfillment(BookingRequestModel bookingRequest) {
        var timeZoneName = organizationClient.getCompanyTimeZoneName(bookingRequest.getCompanyId());

        var response = fulfillmentStub.createFulfillment(CreateFulfillmentRequest.newBuilder()
                .setFulfillment(buildFulfillment(bookingRequest, timeZoneName))
                .addAllGroupClasses(buildGroupClasses(bookingRequest.getServicesList()))
                .build());

        return List.of(response.getFulfillmentId());
    }

    static List<GroupClassCreateDef> buildGroupClasses(List<BookingRequestModel.Service> serviceDetails) {
        return serviceDetails.stream()
                .filter(BookingRequestModel.Service::hasGroupClass)
                .map(BookingRequestModel.Service::getGroupClass)
                .map(groupClass -> {
                    var groupClassService = groupClass.getService();
                    var groupClassDetail = GroupClassDetailCreateDef.newBuilder()
                            .setPetId(groupClassService.getPetId())
                            .setGroupClassInstanceId(groupClassService.getClassInstanceId())
                            .build();
                    return GroupClassCreateDef.newBuilder()
                            .setGroupClassDetail(groupClassDetail)
                            .build();
                })
                .toList();
    }

    static FulfillmentCreateDef buildFulfillment(BookingRequestModel bookingRequest, String timeZoneName) {
        return FulfillmentCreateDef.newBuilder()
                .setCompanyId(bookingRequest.getCompanyId())
                .setBusinessId(bookingRequest.getBusinessId())
                .setCustomerId(bookingRequest.getCustomerId())
                .setBookingRequestId(bookingRequest.getId())
                .setStartDateTime(
                        buildTimestamp(bookingRequest.getStartDate(), bookingRequest.getStartTime(), timeZoneName))
                .setEndDateTime(buildTimestamp(bookingRequest.getEndDate(), bookingRequest.getEndTime(), timeZoneName))
                .setStatus(Status.UNCONFIRMED)
                .setSource(Source.OB)
                .build();
    }

    static Timestamp buildTimestamp(String date, Integer minutesOfDay, String timeZoneName) {
        long seconds = LocalDate.parse(date)
                .atStartOfDay(ZoneId.of(timeZoneName))
                .plusMinutes(minutesOfDay)
                .toEpochSecond();
        return Timestamp.newBuilder().setSeconds(seconds).build();
    }
}

package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeResponse;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper()
public interface ArrivalPickUpTimeConverter {

    ArrivalPickUpTimeConverter INSTANCE = Mappers.getMapper(ArrivalPickUpTimeConverter.class);

    default List<GetAvailableDateTimeResponse.DateTimeRange> convertToDateTimeRange(
            Map<LocalDate, List<DayTimeRangeDef>> dateTimeMap) {
        if (CollectionUtils.isEmpty(dateTimeMap)) {
            return List.of();
        }
        List<GetAvailableDateTimeResponse.DateTimeRange> dateTimeRanges = new ArrayList<>();
        for (Map.Entry<LocalDate, List<DayTimeRangeDef>> dateListEntry : dateTimeMap.entrySet()) {
            dateTimeRanges.add(GetAvailableDateTimeResponse.DateTimeRange.newBuilder()
                    .setDate(ProtobufUtil.toProtobufDate(dateListEntry.getKey().toString()))
                    .addAllTimeRange(dateListEntry.getValue())
                    .build());
        }
        return dateTimeRanges;
    }

    default BookingTimeRangeOverride buildBookingTimeRangeOverride(
            Long settingId, BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef req) {
        var result = new BookingTimeRangeOverride();
        result.setSettingId(settingId);
        result.setStartDate(DateConverter.INSTANCE.toLocalDate(req.getStartDate()));
        result.setEndDate(DateConverter.INSTANCE.toLocalDate(req.getEndDate()));
        result.setIsAvailable(req.getIsAvailable());
        result.setDayTimeRange(TimeRangeConverter.INSTANCE.toString(req.getDayTimeRangesList()));
        result.setTimeRangeType(req.getType().getNumber());
        return result;
    }

    default List<BookingTimeRangeOverride> buildBookingTimeRangeOverride(
            Long settingId, List<BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef> batchReq) {
        if (CollectionUtils.isEmpty(batchReq)) {
            return List.of();
        }
        return batchReq.stream()
                .map(req -> buildBookingTimeRangeOverride(settingId, req))
                .toList();
    }

    default ArrivalPickUpTimeOverrideModel buildArrivalPickUpTimeOverrideModel(
            BookingTimeRangeOverride model, Map<Long, BookingTimeRangeSetting> settingMap, String businessCurrentDate) {
        var isActive = !model.getEndDate().isBefore(LocalDate.parse(businessCurrentDate));
        return ArrivalPickUpTimeOverrideModel.newBuilder()
                .setId(model.getId())
                .setServiceItemType(
                        settingMap.containsKey(model.getSettingId())
                                ? ServiceItemType.forNumber(
                                        settingMap.get(model.getSettingId()).getServiceItemType())
                                : ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED)
                .setType(TimeRangeType.forNumber(model.getTimeRangeType()))
                .setStartDate(DateConverter.INSTANCE.toGoogleDate(model.getStartDate()))
                .setEndDate(DateConverter.INSTANCE.toGoogleDate(model.getEndDate()))
                .setIsAvailable(model.getIsAvailable())
                .addAllDayTimeRanges(TimeRangeConverter.INSTANCE.stringToDayTimeRangeList(model.getDayTimeRange()))
                .setIsActive(isActive)
                .build();
    }

    default List<ArrivalPickUpTimeOverrideModel> buildArrivalPickUpTimeOverrideModel(
            List<BookingTimeRangeOverride> models,
            Map<Long, BookingTimeRangeSetting> settingMap,
            Map<Long, String> businessCurrentDateMap) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream()
                .map(k -> buildArrivalPickUpTimeOverrideModel(
                        k,
                        settingMap,
                        businessCurrentDateMap.getOrDefault(
                                k.getSettingId(), LocalDate.now().toString())))
                .toList();
    }

    default BookingTimeRangeOverride buildUpdateArrivalPickUpTimeOverrideRequest(
            BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef update) {
        BookingTimeRangeOverride model = new BookingTimeRangeOverride();
        model.setId(update.getId());
        if (update.hasStartDate()) {
            model.setStartDate(DateConverter.INSTANCE.toLocalDate(update.getStartDate()));
        }
        if (update.hasEndDate()) {
            model.setEndDate(DateConverter.INSTANCE.toLocalDate(update.getEndDate()));
        }
        if (update.hasIsAvailable()) {
            model.setIsAvailable(update.getIsAvailable());
        }
        if (update.hasDayTimeRanges()) {
            model.setDayTimeRange(TimeRangeConverter.INSTANCE.toString(
                    update.getDayTimeRanges().getValuesList()));
        }
        return model;
    }

    default List<BookingTimeRangeOverride> buildUpdateArrivalPickUpTimeOverrideRequest(
            List<BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef> updates) {
        if (CollectionUtils.isEmpty(updates)) {
            return List.of();
        }
        return updates.stream()
                .map(this::buildUpdateArrivalPickUpTimeOverrideRequest)
                .toList();
    }
}

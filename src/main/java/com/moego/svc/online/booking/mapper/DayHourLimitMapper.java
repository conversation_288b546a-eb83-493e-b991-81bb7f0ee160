package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.DayHourLimitDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.DayHourLimit;
import com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DayHourLimitMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DayHourLimitMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    BasicColumn[] selectList = BasicColumn.columnList(id, type, petSizeIds, petTypeId, isAllBreed, breedIds, serviceIds, isAllService, capacity, createdAt, updatedAt, groupId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DayHourLimit> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DayHourLimit> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DayHourLimitResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="type", property="type", jdbcType=JdbcType.INTEGER),
        @Result(column="pet_size_ids", property="petSizeIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="pet_type_id", property="petTypeId", jdbcType=JdbcType.BIGINT),
        @Result(column="is_all_breed", property="isAllBreed", jdbcType=JdbcType.BIT),
        @Result(column="breed_ids", property="breedIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="service_ids", property="serviceIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="is_all_service", property="isAllService", jdbcType=JdbcType.BIT),
        @Result(column="capacity", property="capacity", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="group_id", property="groupId", jdbcType=JdbcType.BIGINT)
    })
    List<DayHourLimit> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DayHourLimitResult")
    Optional<DayHourLimit> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int insertMultiple(Collection<DayHourLimit> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, dayHourLimit, c ->
            c.map(type).toProperty("type")
            .map(petSizeIds).toProperty("petSizeIds")
            .map(petTypeId).toProperty("petTypeId")
            .map(isAllBreed).toProperty("isAllBreed")
            .map(breedIds).toProperty("breedIds")
            .map(serviceIds).toProperty("serviceIds")
            .map(isAllService).toProperty("isAllService")
            .map(capacity).toProperty("capacity")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(groupId).toProperty("groupId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int insertSelective(DayHourLimit row) {
        return MyBatis3Utils.insert(this::insert, row, dayHourLimit, c ->
            c.map(type).toPropertyWhenPresent("type", row::getType)
            .map(petSizeIds).toPropertyWhenPresent("petSizeIds", row::getPetSizeIds)
            .map(petTypeId).toPropertyWhenPresent("petTypeId", row::getPetTypeId)
            .map(isAllBreed).toPropertyWhenPresent("isAllBreed", row::getIsAllBreed)
            .map(breedIds).toPropertyWhenPresent("breedIds", row::getBreedIds)
            .map(serviceIds).toPropertyWhenPresent("serviceIds", row::getServiceIds)
            .map(isAllService).toPropertyWhenPresent("isAllService", row::getIsAllService)
            .map(capacity).toPropertyWhenPresent("capacity", row::getCapacity)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(groupId).toPropertyWhenPresent("groupId", row::getGroupId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default Optional<DayHourLimit> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default List<DayHourLimit> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default List<DayHourLimit> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default Optional<DayHourLimit> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dayHourLimit, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    static UpdateDSL<UpdateModel> updateAllColumns(DayHourLimit row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(type).equalTo(row::getType)
                .set(petSizeIds).equalTo(row::getPetSizeIds)
                .set(petTypeId).equalTo(row::getPetTypeId)
                .set(isAllBreed).equalTo(row::getIsAllBreed)
                .set(breedIds).equalTo(row::getBreedIds)
                .set(serviceIds).equalTo(row::getServiceIds)
                .set(isAllService).equalTo(row::getIsAllService)
                .set(capacity).equalTo(row::getCapacity)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(groupId).equalTo(row::getGroupId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DayHourLimit row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(type).equalToWhenPresent(row::getType)
                .set(petSizeIds).equalToWhenPresent(row::getPetSizeIds)
                .set(petTypeId).equalToWhenPresent(row::getPetTypeId)
                .set(isAllBreed).equalToWhenPresent(row::getIsAllBreed)
                .set(breedIds).equalToWhenPresent(row::getBreedIds)
                .set(serviceIds).equalToWhenPresent(row::getServiceIds)
                .set(isAllService).equalToWhenPresent(row::getIsAllService)
                .set(capacity).equalToWhenPresent(row::getCapacity)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(groupId).equalToWhenPresent(row::getGroupId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit")
    default int updateByPrimaryKeySelective(DayHourLimit row) {
        return update(c ->
            c.set(type).equalToWhenPresent(row::getType)
            .set(petSizeIds).equalToWhenPresent(row::getPetSizeIds)
            .set(petTypeId).equalToWhenPresent(row::getPetTypeId)
            .set(isAllBreed).equalToWhenPresent(row::getIsAllBreed)
            .set(breedIds).equalToWhenPresent(row::getBreedIds)
            .set(serviceIds).equalToWhenPresent(row::getServiceIds)
            .set(isAllService).equalToWhenPresent(row::getIsAllService)
            .set(capacity).equalToWhenPresent(row::getCapacity)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(groupId).equalToWhenPresent(row::getGroupId)
            .where(id, isEqualTo(row::getId))
        );
    }
}
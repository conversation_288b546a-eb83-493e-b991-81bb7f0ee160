package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class LodgingCapacitySettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    public static final LodgingCapacitySetting lodgingCapacitySetting = new LodgingCapacitySetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.id")
    public static final SqlColumn<Long> id = lodgingCapacitySetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.company_id")
    public static final SqlColumn<Long> companyId = lodgingCapacitySetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.business_id")
    public static final SqlColumn<Long> businessId = lodgingCapacitySetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = lodgingCapacitySetting.serviceItemType;

    /**
     * Database Column Remarks:
     *   if limit requests based on  lodging/area capacity
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.is_capacity_limited")
    public static final SqlColumn<Boolean> isCapacityLimited = lodgingCapacitySetting.isCapacityLimited;

    /**
     * Database Column Remarks:
     *   limit requests based on service related lodging/area capacity
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.capacity_limit")
    public static final SqlColumn<Integer> capacityLimit = lodgingCapacitySetting.capacityLimit;

    /**
     * Database Column Remarks:
     *   当达到 capacity 限制时是否允许提交 waitlist
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_setting.allow_waitlist_signups")
    public static final SqlColumn<Boolean> allowWaitlistSignups = lodgingCapacitySetting.allowWaitlistSignups;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    public static final class LodgingCapacitySetting extends AliasableSqlTable<LodgingCapacitySetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isCapacityLimited = column("is_capacity_limited", JDBCType.BIT);

        public final SqlColumn<Integer> capacityLimit = column("capacity_limit", JDBCType.INTEGER);

        public final SqlColumn<Boolean> allowWaitlistSignups = column("allow_waitlist_signups", JDBCType.BIT);

        public LodgingCapacitySetting() {
            super("lodging_capacity_setting", LodgingCapacitySetting::new);
        }
    }
}
package com.moego.svc.online.booking.mapstruct;

import com.google.type.Date;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.service.online_booking.v1.CreateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingAddOnDetailRequest;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import java.time.LocalDate;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class BoardingAddOnDetailConverter {
    public static final BoardingAddOnDetailConverter INSTANCE = Mappers.getMapper(BoardingAddOnDetailConverter.class);

    public abstract BoardingAddOnDetailModel entityToModel(BoardingAddOnDetail entity);

    public abstract BoardingAddOnDetail createRequestToEntity(CreateBoardingAddOnDetailRequest createRequest);

    public BoardingAddOnDetail updateRequestToEntity(UpdateBoardingAddOnDetailRequest updateRequest) {
        var detail = new BoardingAddOnDetail();

        detail.setId(updateRequest.getId());
        if (updateRequest.hasDateType()) {
            detail.setDateType(updateRequest.getDateType());
            if (updateRequest.getDateType() == PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE) {
                detail.setSpecificDates(updateRequest.getSpecificDates().getValuesList().stream()
                        .map(LocalDate::parse)
                        .toList());
            } else {
                detail.setSpecificDates(List.of());
            }
        }
        if (updateRequest.hasQuantityPerDay()) {
            detail.setQuantityPerDay(updateRequest.getQuantityPerDay());
        }
        if (updateRequest.hasStartDate()) {
            detail.setStartDate(LocalDate.parse(updateRequest.getStartDate()));
        }

        return detail;
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    protected com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    protected java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    protected int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    protected LocalDate googleDateToLocalDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    protected Date localDateToGoogleDate(LocalDate date) {
        return Date.newBuilder()
                .setYear(date.getYear())
                .setMonth(date.getMonthValue())
                .setDay(date.getDayOfMonth())
                .build();
    }
}

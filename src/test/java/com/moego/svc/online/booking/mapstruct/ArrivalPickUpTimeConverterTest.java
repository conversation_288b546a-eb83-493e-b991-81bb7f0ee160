package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Date;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDefList;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class ArrivalPickUpTimeConverterTest {

    @Test
    void buildBookingTimeRangeOverride_withValidRequest_shouldReturnBookingTimeRangeOverride() {
        Long settingId = 1L;
        BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef req =
                BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.newBuilder()
                        .setType(TimeRangeType.ARRIVAL_TIME)
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(1)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(10)
                                .build())
                        .setIsAvailable(true)
                        .addAllDayTimeRanges(List.of())
                        .build();

        List<BookingTimeRangeOverride> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildBookingTimeRangeOverride(settingId, List.of(req));

        BookingTimeRangeOverride expect = new BookingTimeRangeOverride();
        expect.setSettingId(settingId);
        expect.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
        expect.setStartDate(LocalDate.of(2023, 1, 1));
        expect.setEndDate(LocalDate.of(2023, 1, 10));
        expect.setIsAvailable(true);
        expect.setDayTimeRange("[]");

        assertThat(result).usingRecursiveComparison().isEqualTo(List.of(expect));
    }

    @Test
    void buildBookingTimeRangeOverride_withEmptyModelList_shouldReturnEmptyList() {
        Long settingId = 1L;

        List<BookingTimeRangeOverride> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildBookingTimeRangeOverride(settingId, List.of());

        assertThat(result).isEmpty();
    }

    @Test
    void buildArrivalPickUpTimeOverrideModel_withValidModel_shouldReturnArrivalPickUpTimeOverrideModel() {
        Long settingId = 1L;
        Long businessId = 1L;

        BookingTimeRangeSetting settingPO = new BookingTimeRangeSetting();
        settingPO.setId(settingId);
        settingPO.setServiceItemType(ServiceItemType.BOARDING.getNumber());
        settingPO.setBusinessId(businessId);

        BookingTimeRangeOverride overridePO1 = new BookingTimeRangeOverride();
        overridePO1.setId(1L);
        overridePO1.setSettingId(settingId);
        overridePO1.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
        overridePO1.setStartDate(LocalDate.of(2023, 1, 1));
        overridePO1.setEndDate(LocalDate.of(2023, 1, 10));
        overridePO1.setIsAvailable(true);
        overridePO1.setDayTimeRange("[]");

        BookingTimeRangeOverride overridePO2 = new BookingTimeRangeOverride();
        overridePO2.setId(1L);
        overridePO2.setSettingId(settingId);
        overridePO2.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
        overridePO2.setStartDate(LocalDate.of(2023, 1, 1));
        overridePO2.setEndDate(LocalDate.of(2023, 1, 9));
        overridePO2.setIsAvailable(true);
        overridePO2.setDayTimeRange("[]");

        List<ArrivalPickUpTimeOverrideModel> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(
                        List.of(overridePO1, overridePO2),
                        Map.of(settingId, settingPO),
                        Map.of(businessId, "2023-01-10"));

        ArrivalPickUpTimeOverrideModel expect1 = ArrivalPickUpTimeOverrideModel.newBuilder()
                .setId(1L)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setType(TimeRangeType.ARRIVAL_TIME)
                .setStartDate(
                        Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                .setEndDate(
                        Date.newBuilder().setYear(2023).setMonth(1).setDay(10).build())
                .setIsAvailable(true)
                .addAllDayTimeRanges(List.of())
                .setIsActive(true)
                .build();

        ArrivalPickUpTimeOverrideModel expect2 = ArrivalPickUpTimeOverrideModel.newBuilder()
                .setId(1L)
                .setServiceItemType(ServiceItemType.BOARDING)
                .setType(TimeRangeType.ARRIVAL_TIME)
                .setStartDate(
                        Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build())
                .setEndDate(
                        Date.newBuilder().setYear(2023).setMonth(1).setDay(9).build())
                .setIsAvailable(true)
                .addAllDayTimeRanges(List.of())
                .setIsActive(false)
                .build();

        assertThat(result).isEqualTo(List.of(expect1, expect2));
    }

    @Test
    void buildArrivalPickUpTimeOverrideModel_withEmptyModelList_shouldReturnEmptyList() {

        List<ArrivalPickUpTimeOverrideModel> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(List.of(), Map.of(), Map.of());

        assertThat(result).isEmpty();
    }

    @Test
    void buildUpdateArrivalPickUpTimeOverrideRequest_withValidUpdate_shouldReturnBookingTimeRangeOverride() {
        BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef update =
                BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef.newBuilder()
                        .setId(1L)
                        .setStartDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(1)
                                .build())
                        .setEndDate(Date.newBuilder()
                                .setYear(2023)
                                .setMonth(1)
                                .setDay(10)
                                .build())
                        .setIsAvailable(true)
                        .setDayTimeRanges(DayTimeRangeDefList.getDefaultInstance())
                        .build();

        List<BookingTimeRangeOverride> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildUpdateArrivalPickUpTimeOverrideRequest(List.of(update));

        BookingTimeRangeOverride expect = new BookingTimeRangeOverride();
        expect.setId(1L);
        expect.setStartDate(LocalDate.of(2023, 1, 1));
        expect.setEndDate(LocalDate.of(2023, 1, 10));
        expect.setIsAvailable(true);
        expect.setDayTimeRange("[]");

        assertThat(result).usingRecursiveComparison().isEqualTo(List.of(expect));
    }

    @Test
    void buildUpdateArrivalPickUpTimeOverrideRequest_withEmptyModelList_shouldReturnEmptyList() {
        List<BookingTimeRangeOverride> result =
                ArrivalPickUpTimeConverter.INSTANCE.buildUpdateArrivalPickUpTimeOverrideRequest(List.of());

        assertThat(result).isEmpty();
    }
}

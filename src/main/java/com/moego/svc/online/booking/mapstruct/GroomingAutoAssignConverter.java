package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.GroomingAutoAssignModel;
import com.moego.idl.service.online_booking.v1.UpsertGroomingAutoAssignRequest;
import com.moego.svc.online.booking.entity.GroomingAutoAssign;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GroomingAutoAssignConverter {
    GroomingAutoAssignConverter INSTANCE = Mappers.getMapper(GroomingAutoAssignConverter.class);

    GroomingAutoAssignModel entityToModel(GroomingAutoAssign entity);

    GroomingAutoAssign upsertRequestToEntity(UpsertGroomingAutoAssignRequest request);

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }
}

package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BoardingServiceDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_detail")
    public static final BoardingServiceDetail boardingServiceDetail = new BoardingServiceDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.id")
    public static final SqlColumn<Long> id = boardingServiceDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = boardingServiceDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.pet_id")
    public static final SqlColumn<Long> petId = boardingServiceDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of lodging, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.lodging_id")
    public static final SqlColumn<Long> lodgingId = boardingServiceDetail.lodgingId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.service_id")
    public static final SqlColumn<Long> serviceId = boardingServiceDetail.serviceId;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = boardingServiceDetail.servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.tax_id")
    public static final SqlColumn<Long> taxId = boardingServiceDetail.taxId;

    /**
     * Database Column Remarks:
     *   The pet arrival date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.start_date")
    public static final SqlColumn<String> startDate = boardingServiceDetail.startDate;

    /**
     * Database Column Remarks:
     *   The pet arrival time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.start_time")
    public static final SqlColumn<Integer> startTime = boardingServiceDetail.startTime;

    /**
     * Database Column Remarks:
     *   The pet pickup date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.end_date")
    public static final SqlColumn<String> endDate = boardingServiceDetail.endDate;

    /**
     * Database Column Remarks:
     *   The pet pickup time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.end_time")
    public static final SqlColumn<Integer> endTime = boardingServiceDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.created_at")
    public static final SqlColumn<Date> createdAt = boardingServiceDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = boardingServiceDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = boardingServiceDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_detail")
    public static final class BoardingServiceDetail extends AliasableSqlTable<BoardingServiceDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> lodgingId = column("lodging_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<String> startDate = column("start_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<String> endDate = column("end_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public BoardingServiceDetail() {
            super("boarding_service_detail", BoardingServiceDetail::new);
        }
    }
}
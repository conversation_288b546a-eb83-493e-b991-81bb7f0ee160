package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceDetailRequest;
import com.moego.svc.online.booking.entity.BoardingServiceDetail;
import org.junit.jupiter.api.Test;

class BoardingServiceDetailConverterTest {

    @Test
    void updateRequestToEntity() {
        var updateRequest = UpdateBoardingServiceDetailRequest.newBuilder()
                .setId(1L)
                .setStartDate("2025-01-01")
                .setStartTime(600)
                .setEndDate("2025-01-02")
                .setEndTime(1080)
                .build();
        var result = BoardingServiceDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);
        var expect = new BoardingServiceDetail();
        expect.setId(1L);
        expect.setStartDate("2025-01-01");
        expect.setStartTime(600);
        expect.setEndDate("2025-01-02");
        expect.setEndTime(1080);
        assertThat(result).isEqualTo(expect);
    }
}

package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class AutomationSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    public static final AutomationSetting automationSetting = new AutomationSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.id")
    public static final SqlColumn<Long> id = automationSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.business_id")
    public static final SqlColumn<Long> businessId = automationSetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.company_id")
    public static final SqlColumn<Long> companyId = automationSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = automationSetting.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.enable_auto_accept")
    public static final SqlColumn<Boolean> enableAutoAccept = automationSetting.enableAutoAccept;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.auto_accept_condition")
    public static final SqlColumn<String> autoAcceptCondition = automationSetting.autoAcceptCondition;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.created_at")
    public static final SqlColumn<Date> createdAt = automationSetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.updated_at")
    public static final SqlColumn<Date> updatedAt = automationSetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: automation_setting.update_by")
    public static final SqlColumn<Long> updateBy = automationSetting.updateBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: automation_setting")
    public static final class AutomationSetting extends AliasableSqlTable<AutomationSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Boolean> enableAutoAccept = column("enable_auto_accept", JDBCType.BIT);

        public final SqlColumn<String> autoAcceptCondition = column("auto_accept_condition", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updateBy = column("update_by", JDBCType.BIGINT);

        public AutomationSetting() {
            super("automation_setting", AutomationSetting::new);
        }
    }
}
package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class FeedingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    public static final Feeding feeding = new Feeding();

    /**
     * Database Column Remarks:
     *   The primary key identifier for each feeding.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.id")
    public static final SqlColumn<Long> id = feeding.id;

    /**
     * Database Column Remarks:
     *   The booking request identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = feeding.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The service detail identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = feeding.serviceDetailId;

    /**
     * Database Column Remarks:
     *   service detail type, 1: boarding, 2: daycare
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_type")
    public static final SqlColumn<Integer> serviceDetailType = feeding.serviceDetailType;

    /**
     * Database Column Remarks:
     *   Feeding time.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.time")
    public static final SqlColumn<String> time = feeding.time;

    /**
     * Database Column Remarks:
     *   Feeding amount, must be greater than 0.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount")
    public static final SqlColumn<BigDecimal> amount = feeding.amount;

    /**
     * Database Column Remarks:
     *   Feeding unit.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.unit")
    public static final SqlColumn<String> unit = feeding.unit;

    /**
     * Database Column Remarks:
     *   Food type.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_type")
    public static final SqlColumn<String> foodType = feeding.foodType;

    /**
     * Database Column Remarks:
     *   Food source.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_source")
    public static final SqlColumn<String> foodSource = feeding.foodSource;

    /**
     * Database Column Remarks:
     *   Feeding instructions.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.instruction")
    public static final SqlColumn<String> instruction = feeding.instruction;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.created_at")
    public static final SqlColumn<Date> createdAt = feeding.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.updated_at")
    public static final SqlColumn<Date> updatedAt = feeding.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.deleted_at")
    public static final SqlColumn<Date> deletedAt = feeding.deletedAt;

    /**
     * Database Column Remarks:
     *   Additional notes about the feeding.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.note")
    public static final SqlColumn<String> note = feeding.note;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount_str")
    public static final SqlColumn<String> amountStr = feeding.amountStr;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    public static final class Feeding extends AliasableSqlTable<Feeding> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceDetailType = column("service_detail_type", JDBCType.INTEGER);

        public final SqlColumn<String> time = column("\"time\"", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<BigDecimal> amount = column("amount", JDBCType.NUMERIC);

        public final SqlColumn<String> unit = column("unit", JDBCType.VARCHAR);

        public final SqlColumn<String> foodType = column("food_type", JDBCType.VARCHAR);

        public final SqlColumn<String> foodSource = column("food_source", JDBCType.VARCHAR);

        public final SqlColumn<String> instruction = column("instruction", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> note = column("note", JDBCType.VARCHAR);

        public final SqlColumn<String> amountStr = column("amount_str", JDBCType.VARCHAR);

        public Feeding() {
            super("feeding", Feeding::new);
        }
    }
}
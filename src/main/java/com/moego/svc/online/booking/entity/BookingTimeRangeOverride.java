package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_time_range_override
 */
public class BookingTimeRangeOverride {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking time range setting
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.setting_id")
    private Long settingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.start_date")
    private LocalDate startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.end_date")
    private LocalDate endDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.is_available")
    private Boolean isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.day_time_range")
    private String dayTimeRange;

    /**
     * Database Column Remarks:
     *   1.arrival time 2.pick up time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.time_range_type")
    private Integer timeRangeType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.setting_id")
    public Long getSettingId() {
        return settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.setting_id")
    public void setSettingId(Long settingId) {
        this.settingId = settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.start_date")
    public LocalDate getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.start_date")
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.end_date")
    public LocalDate getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.end_date")
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.is_available")
    public Boolean getIsAvailable() {
        return isAvailable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.is_available")
    public void setIsAvailable(Boolean isAvailable) {
        this.isAvailable = isAvailable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.day_time_range")
    public String getDayTimeRange() {
        return dayTimeRange;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.day_time_range")
    public void setDayTimeRange(String dayTimeRange) {
        this.dayTimeRange = dayTimeRange;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.time_range_type")
    public Integer getTimeRangeType() {
        return timeRangeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.time_range_type")
    public void setTimeRangeType(Integer timeRangeType) {
        this.timeRangeType = timeRangeType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", settingId=").append(settingId);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", isAvailable=").append(isAvailable);
        sb.append(", dayTimeRange=").append(dayTimeRange);
        sb.append(", timeRangeType=").append(timeRangeType);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingTimeRangeOverride other = (BookingTimeRangeOverride) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSettingId() == null ? other.getSettingId() == null : this.getSettingId().equals(other.getSettingId()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getIsAvailable() == null ? other.getIsAvailable() == null : this.getIsAvailable().equals(other.getIsAvailable()))
            && (this.getDayTimeRange() == null ? other.getDayTimeRange() == null : this.getDayTimeRange().equals(other.getDayTimeRange()))
            && (this.getTimeRangeType() == null ? other.getTimeRangeType() == null : this.getTimeRangeType().equals(other.getTimeRangeType()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSettingId() == null) ? 0 : getSettingId().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getIsAvailable() == null) ? 0 : getIsAvailable().hashCode());
        result = prime * result + ((getDayTimeRange() == null) ? 0 : getDayTimeRange().hashCode());
        result = prime * result + ((getTimeRangeType() == null) ? 0 : getTimeRangeType().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
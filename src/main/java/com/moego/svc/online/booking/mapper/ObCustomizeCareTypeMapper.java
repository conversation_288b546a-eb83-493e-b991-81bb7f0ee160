package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.ObCustomizeCareTypeDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler;
import com.moego.svc.online.booking.typehandler.ServiceItemTypeHandler;
import com.moego.svc.online.booking.typehandler.ServiceTypeHandler;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface ObCustomizeCareTypeMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<ObCustomizeCareTypeMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    BasicColumn[] selectList = BasicColumn.columnList(id, name, companyId, businessId, description, icon, image, sort, serviceType, isAllServiceApplicable, selectedServices, serviceItemType, updatedBy, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<ObCustomizeCareType> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<ObCustomizeCareType> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="ObCustomizeCareTypeResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="description", property="description", jdbcType=JdbcType.VARCHAR),
        @Result(column="icon", property="icon", jdbcType=JdbcType.VARCHAR),
        @Result(column="image", property="image", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="sort", property="sort", jdbcType=JdbcType.INTEGER),
        @Result(column="service_type", property="serviceType", typeHandler=ServiceTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="is_all_service_applicable", property="isAllServiceApplicable", jdbcType=JdbcType.BIT),
        @Result(column="selected_services", property="selectedServices", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="service_item_type", property="serviceItemType", typeHandler=ServiceItemTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<ObCustomizeCareType> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("ObCustomizeCareTypeResult")
    Optional<ObCustomizeCareType> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int insertMultiple(Collection<ObCustomizeCareType> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, obCustomizeCareType, c ->
            c.map(name).toProperty("name")
            .map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(description).toProperty("description")
            .map(icon).toProperty("icon")
            .map(image).toProperty("image")
            .map(sort).toProperty("sort")
            .map(serviceType).toProperty("serviceType")
            .map(isAllServiceApplicable).toProperty("isAllServiceApplicable")
            .map(selectedServices).toProperty("selectedServices")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(updatedBy).toProperty("updatedBy")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int insertSelective(ObCustomizeCareType row) {
        return MyBatis3Utils.insert(this::insert, row, obCustomizeCareType, c ->
            c.map(name).toPropertyWhenPresent("name", row::getName)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(description).toPropertyWhenPresent("description", row::getDescription)
            .map(icon).toPropertyWhenPresent("icon", row::getIcon)
            .map(image).toPropertyWhenPresent("image", row::getImage)
            .map(sort).toPropertyWhenPresent("sort", row::getSort)
            .map(serviceType).toPropertyWhenPresent("serviceType", row::getServiceType)
            .map(isAllServiceApplicable).toPropertyWhenPresent("isAllServiceApplicable", row::getIsAllServiceApplicable)
            .map(selectedServices).toPropertyWhenPresent("selectedServices", row::getSelectedServices)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", row::getUpdatedBy)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default Optional<ObCustomizeCareType> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default List<ObCustomizeCareType> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default List<ObCustomizeCareType> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default Optional<ObCustomizeCareType> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, obCustomizeCareType, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    static UpdateDSL<UpdateModel> updateAllColumns(ObCustomizeCareType row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(name).equalTo(row::getName)
                .set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(description).equalTo(row::getDescription)
                .set(icon).equalTo(row::getIcon)
                .set(image).equalTo(row::getImage)
                .set(sort).equalTo(row::getSort)
                .set(serviceType).equalTo(row::getServiceType)
                .set(isAllServiceApplicable).equalTo(row::getIsAllServiceApplicable)
                .set(selectedServices).equalTo(row::getSelectedServices)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(updatedBy).equalTo(row::getUpdatedBy)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ObCustomizeCareType row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(name).equalToWhenPresent(row::getName)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(icon).equalToWhenPresent(row::getIcon)
                .set(image).equalToWhenPresent(row::getImage)
                .set(sort).equalToWhenPresent(row::getSort)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(isAllServiceApplicable).equalToWhenPresent(row::getIsAllServiceApplicable)
                .set(selectedServices).equalToWhenPresent(row::getSelectedServices)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    default int updateByPrimaryKeySelective(ObCustomizeCareType row) {
        return update(c ->
            c.set(name).equalToWhenPresent(row::getName)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(description).equalToWhenPresent(row::getDescription)
            .set(icon).equalToWhenPresent(row::getIcon)
            .set(image).equalToWhenPresent(row::getImage)
            .set(sort).equalToWhenPresent(row::getSort)
            .set(serviceType).equalToWhenPresent(row::getServiceType)
            .set(isAllServiceApplicable).equalToWhenPresent(row::getIsAllServiceApplicable)
            .set(selectedServices).equalToWhenPresent(row::getSelectedServices)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
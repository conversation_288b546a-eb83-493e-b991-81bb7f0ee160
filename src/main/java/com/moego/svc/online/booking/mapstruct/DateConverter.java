package com.moego.svc.online.booking.mapstruct;

import com.google.type.Date;
import java.time.LocalDate;
import java.util.Calendar;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DateConverter {

    DateConverter INSTANCE = Mappers.getMapper(DateConverter.class);

    default Date toGoogleDate(java.util.Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return Date.newBuilder()
                .setYear(calendar.get(Calendar.YEAR))
                .setMonth(calendar.get(Calendar.MONTH)
                        + 1) // Calendar.MONTH is 0-based (0 = January, 1 = February, ..., 11 = December)
                .setDay(calendar.get(Calendar.DAY_OF_MONTH))
                .build();
    }

    default Date toGoogleDate(LocalDate localDate) {
        return Date.newBuilder()
                .setYear(localDate.getYear())
                .setMonth(localDate.getMonthValue())
                .setDay(localDate.getDayOfMonth())
                .build();
    }

    default java.util.Date fromGoogleDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(date.getYear(), date.getMonth() - 1, date.getDay());
        return calendar.getTime();
    }

    default LocalDate toLocalDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }
}

package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Database Table Remarks:
 *   The grooming add-on detail
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table grooming_add_on_detail
 */
public class GroomingAddOnDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of service detail
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_detail_id")
    private Long serviceDetailId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   The id of staff, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.staff_id")
    private Long staffId;

    /**
     * Database Column Remarks:
     *   The id of add-on service, aka. grooming service id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.add_on_id")
    private Long addOnId;

    /**
     * Database Column Remarks:
     *   The time of current service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_time")
    private Integer serviceTime;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_price")
    private BigDecimal servicePrice;

    /**
     * Database Column Remarks:
     *   The start date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_date")
    private String startDate;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   The end date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_date")
    private String endDate;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_time")
    private Integer endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.deleted_at")
    private Date deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.tax_id")
    private Long taxId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_detail_id")
    public Long getServiceDetailId() {
        return serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_detail_id")
    public void setServiceDetailId(Long serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.add_on_id")
    public Long getAddOnId() {
        return addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.add_on_id")
    public void setAddOnId(Long addOnId) {
        this.addOnId = addOnId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_time")
    public Integer getServiceTime() {
        return serviceTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_time")
    public void setServiceTime(Integer serviceTime) {
        this.serviceTime = serviceTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_date")
    public String getStartDate() {
        return startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_date")
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_date")
    public String getEndDate() {
        return endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_date")
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.tax_id")
    public Long getTaxId() {
        return taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: grooming_add_on_detail.tax_id")
    public void setTaxId(Long taxId) {
        this.taxId = taxId;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", serviceDetailId=").append(serviceDetailId);
        sb.append(", petId=").append(petId);
        sb.append(", staffId=").append(staffId);
        sb.append(", addOnId=").append(addOnId);
        sb.append(", serviceTime=").append(serviceTime);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", startDate=").append(startDate);
        sb.append(", startTime=").append(startTime);
        sb.append(", endDate=").append(endDate);
        sb.append(", endTime=").append(endTime);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", taxId=").append(taxId);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GroomingAddOnDetail other = (GroomingAddOnDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getServiceDetailId() == null ? other.getServiceDetailId() == null : this.getServiceDetailId().equals(other.getServiceDetailId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getAddOnId() == null ? other.getAddOnId() == null : this.getAddOnId().equals(other.getAddOnId()))
            && (this.getServiceTime() == null ? other.getServiceTime() == null : this.getServiceTime().equals(other.getServiceTime()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getTaxId() == null ? other.getTaxId() == null : this.getTaxId().equals(other.getTaxId()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getServiceDetailId() == null) ? 0 : getServiceDetailId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getAddOnId() == null) ? 0 : getAddOnId().hashCode());
        result = prime * result + ((getServiceTime() == null) ? 0 : getServiceTime().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getTaxId() == null) ? 0 : getTaxId().hashCode());
        return result;
    }
}
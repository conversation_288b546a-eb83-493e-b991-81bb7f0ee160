package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingTimeRangeDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    public static final BookingTimeRangeDetail bookingTimeRangeDetail = new BookingTimeRangeDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.id")
    public static final SqlColumn<Long> id = bookingTimeRangeDetail.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.setting_id")
    public static final SqlColumn<Long> settingId = bookingTimeRangeDetail.settingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.time_range_type")
    public static final SqlColumn<Integer> timeRangeType = bookingTimeRangeDetail.timeRangeType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.first_week")
    public static final SqlColumn<String> firstWeek = bookingTimeRangeDetail.firstWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.second_week")
    public static final SqlColumn<String> secondWeek = bookingTimeRangeDetail.secondWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.third_week")
    public static final SqlColumn<String> thirdWeek = bookingTimeRangeDetail.thirdWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.forth_week")
    public static final SqlColumn<String> forthWeek = bookingTimeRangeDetail.forthWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.created_at")
    public static final SqlColumn<Date> createdAt = bookingTimeRangeDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = bookingTimeRangeDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    public static final class BookingTimeRangeDetail extends AliasableSqlTable<BookingTimeRangeDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> settingId = column("setting_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> timeRangeType = column("time_range_type", JDBCType.INTEGER);

        public final SqlColumn<String> firstWeek = column("first_week", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<String> secondWeek = column("second_week", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<String> thirdWeek = column("third_week", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<String> forthWeek = column("forth_week", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public BookingTimeRangeDetail() {
            super("booking_time_range_detail", BookingTimeRangeDetail::new);
        }
    }
}
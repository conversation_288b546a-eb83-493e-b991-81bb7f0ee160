package com.moego.svc.online.booking.utils;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.lib.common.proto.MoneyUtils.toGoogleMoney;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceOverrideType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.online_booking.v1.BoardingAddon;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetail;
import com.moego.idl.models.online_booking.v1.DaycareAddon;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetail;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetail;
import com.moego.idl.models.online_booking.v1.GroomingAddon;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetail;
import com.moego.idl.models.online_booking.v1.Pet;
import com.moego.idl.models.online_booking.v1.PetServiceDetails;
import com.moego.idl.models.online_booking.v1.ServiceDetail;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingResponse.LineItem;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import org.jetbrains.annotations.NotNull;

/**
 * <AUTHOR>
 * @since 2025/5/19
 */
public class PricingRuleUtils {

    @Builder(toBuilder = true)
    public record FulfillmentLineItem(
            CustomizedServiceView service,
            BigDecimal price,
            Long petId,
            @Nullable String date,
            @Nullable Long associatedServiceId) {}

    public static List<FulfillmentLineItem> convertServices(
            List<PetServiceDetails> petServiceDetails, List<ServiceWithCustomizedInfo> customizedServiceList) {
        return petServiceDetails.stream()
                .flatMap(petServiceDetail -> {
                    var pet = petServiceDetail.getPet();
                    return petServiceDetail.getServiceDetailsList().stream().flatMap(serviceDetail -> {
                        var serviceId = getServiceId(serviceDetail);
                        if (serviceId == null) {
                            return Stream.empty();
                        }

                        var petId = petServiceDetail.getIsNewPet() ? null : pet.getPetId();
                        var customizedService = findCustomizedService(customizedServiceList, serviceId, petId);
                        if (customizedService == null) {
                            return Stream.empty();
                        }

                        return switch (serviceDetail.getServiceCase()) {
                            case BOARDING -> generateBoardingServiceAmounts(
                                    serviceDetail.getBoarding(), customizedService, pet.getPetId());
                            case DAYCARE -> generateDaycareServiceAmounts(
                                    serviceDetail.getDaycare(), customizedService, pet.getPetId());
                            case GROOMING -> generateGroomingServiceAmounts(
                                    serviceDetail.getGrooming(), customizedService, pet.getPetId());
                            case DOG_WALKING -> generateDogWalkingServiceAmounts(
                                    serviceDetail.getDogWalking(), customizedService, pet.getPetId());
                            default -> Stream.empty();
                        };
                    });
                })
                .toList();
    }

    @Nullable
    private static CustomizedServiceView findCustomizedService(
            List<ServiceWithCustomizedInfo> customizedServiceList, Long serviceId, @Nullable Long petId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId());
                })
                .findFirst()
                .map(ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    private static Stream<FulfillmentLineItem> generateBoardingServiceAmounts(
            BoardingServiceDetail boarding, CustomizedServiceView customizedService, Long petId) {
        var start = boarding.getStartDate();
        var end = boarding.getEndDate();
        var endDate = isPerDayBoardingService(customizedService)
                ? LocalDate.parse(end).plusDays(1)
                : LocalDate.parse(end);

        return LocalDate.parse(start).datesUntil(endDate).map(date -> FulfillmentLineItem.builder()
                .service(customizedService)
                .price(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .date(date.toString())
                .build());
    }

    private static Stream<FulfillmentLineItem> generateDaycareServiceAmounts(
            DaycareServiceDetail daycare, CustomizedServiceView customizedService, Long petId) {
        return daycare.getDatesList().stream().map(date -> FulfillmentLineItem.builder()
                .service(customizedService)
                .price(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .date(date)
                .build());
    }

    private static Stream<FulfillmentLineItem> generateDogWalkingServiceAmounts(
            DogWalkingServiceDetail dogWalking, CustomizedServiceView customizedService, long petId) {
        var amount = FulfillmentLineItem.builder()
                .service(customizedService)
                .price(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .date(dogWalking.getDate())
                .build();

        return Stream.of(amount);
    }

    private static Stream<FulfillmentLineItem> generateGroomingServiceAmounts(
            GroomingServiceDetail grooming, CustomizedServiceView customizedService, long petId) {
        var amount = FulfillmentLineItem.builder()
                .service(customizedService)
                .price(BigDecimal.valueOf(customizedService.getPrice()))
                .petId(petId)
                .date(grooming.getStartDate())
                .build();

        return Stream.of(amount);
    }

    public static List<FulfillmentLineItem> convertAddOns(
            List<PetServiceDetails> petServiceDetails, List<ServiceWithCustomizedInfo> customizedServices) {
        return petServiceDetails.stream()
                .flatMap(petServiceDetail -> {
                    var pet = petServiceDetail.getPet();
                    return petServiceDetail.getServiceDetailsList().stream().flatMap(service -> {
                        var petId = petServiceDetail.getIsNewPet() ? null : pet.getPetId();
                        return switch (service.getServiceCase()) {
                            case BOARDING -> generateBoardingAddonAmounts(
                                    service.getBoarding(), customizedServices, pet, petId);
                            case DAYCARE -> generateDaycareAddonAmounts(
                                    service.getDaycare(), customizedServices, pet, petId);
                            case GROOMING -> generateGroomingAddonAmounts(
                                    service.getGrooming(), customizedServices, pet, petId);
                            case DOG_WALKING -> generateDogWalkingAddonAmounts();
                            default -> Stream.empty();
                        };
                    });
                })
                .toList();
    }

    private static Stream<FulfillmentLineItem> generateDogWalkingAddonAmounts() {
        // no addon for dog walking
        return Stream.of();
    }

    private static Stream<FulfillmentLineItem> generateGroomingAddonAmounts(
            GroomingServiceDetail grooming,
            List<ServiceWithCustomizedInfo> customizedServiceList,
            Pet pet,
            @Nullable Long petId) {
        return grooming.getAddonsList().stream()
                .map(e -> {
                    var customizedService = findCustomizedService(customizedServiceList, e.getId(), petId);
                    if (customizedService == null) {
                        return null;
                    }
                    return FulfillmentLineItem.builder()
                            .service(customizedService)
                            .price(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.getPetId())
                            .date(grooming.getStartDate())
                            .associatedServiceId(grooming.getServiceId())
                            .build();
                })
                .filter(Objects::nonNull);
    }

    private static Stream<FulfillmentLineItem> generateBoardingAddonAmounts(
            BoardingServiceDetail boarding,
            List<ServiceWithCustomizedInfo> customizedServiceList,
            Pet pet,
            Long petId) {
        return boarding.getAddonsList().stream().flatMap(addon -> {
            var customizedService = findCustomizedService(customizedServiceList, addon.getId(), petId);
            if (customizedService == null) {
                return Stream.empty();
            }
            return Stream.generate(() -> FulfillmentLineItem.builder()
                            .service(customizedService)
                            .price(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.getPetId())
                            .date(null)
                            .associatedServiceId(boarding.getServiceId())
                            .build())
                    .limit(calculateCount(boarding, addon));
        });
    }

    private static Stream<FulfillmentLineItem> generateDaycareAddonAmounts(
            DaycareServiceDetail daycare, List<ServiceWithCustomizedInfo> customizedServiceList, Pet pet, Long petId) {
        return daycare.getAddonsList().stream().flatMap(addon -> {
            var customizedService = findCustomizedService(customizedServiceList, addon.getId(), petId);
            if (customizedService == null) {
                return Stream.empty();
            }
            return Stream.generate(() -> FulfillmentLineItem.builder()
                            .service(customizedService)
                            .price(BigDecimal.valueOf(customizedService.getPrice()))
                            .petId(pet.getPetId())
                            .date(null)
                            .associatedServiceId(daycare.getServiceId())
                            .build())
                    .limit(calculateCount(daycare, addon));
        });
    }

    private static long calculateCount(BoardingServiceDetail service, BoardingAddon addon) {
        var start = service.getStartDate();
        var end = service.getEndDate();

        if (!addon.hasDateType()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "date type is required for boarding service add-on");
        }

        long days =
                switch (addon.getDateType()) {
                    case PET_DETAIL_DATE_EVERYDAY -> LocalDate.parse(start)
                            .datesUntil(LocalDate.parse(end))
                            .count();
                    case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> LocalDate.parse(start)
                            .datesUntil(LocalDate.parse(end).plusDays(1))
                            .count();
                    case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> LocalDate.parse(start)
                            .plusDays(1)
                            .datesUntil(LocalDate.parse(end))
                            .count();
                    case PET_DETAIL_DATE_SPECIFIC_DATE -> addon.getDatesCount();
                    case PET_DETAIL_DATE_DATE_POINT, PET_DETAIL_DATE_LAST_DAY, PET_DETAIL_DATE_FIRST_DAY -> 1;
                    default -> throw bizException(
                            Code.CODE_PARAMS_ERROR, "date type is invalid: " + addon.getDateType());
                };

        return days * getQuantityPerDay(addon);
    }

    private static int calculateCount(DaycareServiceDetail service, DaycareAddon addon) {
        if (addon.getIsEveryDay()) {
            return service.getDatesCount() * getQuantityPerDay(addon);
        }
        return addon.getDatesCount() * getQuantityPerDay(addon);
    }

    private static int getQuantityPerDay(BoardingAddon addon) {
        return addon.hasQuantityPerDay() ? addon.getQuantityPerDay() : 1;
    }

    private static int getQuantityPerDay(DaycareAddon addon) {
        return addon.hasQuantityPerDay() ? addon.getQuantityPerDay() : 1;
    }

    private static boolean isPerDayBoardingService(CustomizedServiceView customizedService) {
        return customizedService.getServiceItemType() == ServiceItemType.BOARDING
                && customizedService.getPriceUnit() == ServicePriceUnit.PER_DAY;
    }

    public static boolean isPricingRuleEligible(FulfillmentLineItem item) {
        return (item.service.getServiceItemType() == ServiceItemType.BOARDING
                        || item.service.getServiceItemType() == ServiceItemType.DAYCARE)
                && !Objects.equals(ServiceOverrideType.CLIENT, item.service.getPriceOverrideType());
    }

    public static List<FulfillmentLineItem> getUsingPricingRuleService(
            List<FulfillmentLineItem> items, List<PetDetailCalculateResultDef> petDetails) {
        return items.stream()
                .map(item -> {
                    if (item.service.getPriceOverrideType() == ServiceOverrideType.CLIENT) {
                        // override by client 优先级比 pricing rule 更高
                        // See
                        // https://moego.atlassian.net/wiki/spaces/~************************/pages/585826305/Service+by+Staff+-+PRD#Detail
                        return item;
                    }

                    var petDetail = findMatchingPetDetail(petDetails, item);
                    if (petDetail == null) {
                        return item;
                    }

                    return item.toBuilder()
                            .price(BigDecimal.valueOf(petDetail.getAdjustedPrice()))
                            .build();
                })
                .toList();
    }

    private static PetDetailCalculateResultDef findMatchingPetDetail(
            List<PetDetailCalculateResultDef> petDetails, FulfillmentLineItem item) {
        return petDetails.stream()
                .filter(detail -> Objects.equals(detail.getPetId(), item.petId())
                        && Objects.equals(detail.getServiceId(), item.service.getId())
                        && Objects.equals(detail.getServiceDate(), item.date()))
                .findFirst()
                .orElse(null);
    }

    @Nullable
    private static Long getServiceId(ServiceDetail service) {
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getServiceId();
            case DAYCARE -> service.getDaycare().getServiceId();
            case GROOMING -> service.getGrooming().getServiceId();
            case DOG_WALKING -> service.getDogWalking().getServiceId();
            default -> null;
        };
    }

    @NotNull
    private static List<Long> getAddOnIds(ServiceDetail service) {
        return switch (service.getServiceCase()) {
            case BOARDING -> service.getBoarding().getAddonsList().stream()
                    .map(BoardingAddon::getId)
                    .distinct()
                    .toList();
            case DAYCARE -> service.getDaycare().getAddonsList().stream()
                    .map(DaycareAddon::getId)
                    .distinct()
                    .toList();
            case GROOMING -> service.getGrooming().getAddonsList().stream()
                    .map(GroomingAddon::getId)
                    .distinct()
                    .toList();
            default -> List.of();
        };
    }

    public static Stream<CustomizedServiceQueryCondition> getQueryConditionStream(
            long businessId, ServiceDetail service, PetServiceDetails petServiceDetails) {
        var serviceId = getServiceId(service);
        if (serviceId == null) {
            return Stream.empty();
        }

        var serviceCondition = CustomizedServiceQueryCondition.newBuilder()
                .setServiceId(serviceId)
                .setBusinessId(businessId);

        var petId = petServiceDetails.getPet().getPetId();
        var isExistingPet = !petServiceDetails.getIsNewPet() && isNormal(petId);
        if (isExistingPet) {
            serviceCondition.setPetId(petId);
        }

        var addOnConditions = getAddOnIds(service).stream().map(addOnId -> {
            var builder = CustomizedServiceQueryCondition.newBuilder()
                    .setServiceId(addOnId)
                    .setBusinessId(businessId);
            if (isExistingPet) {
                builder.setPetId(petId);
            }
            return builder.build();
        });

        return Stream.concat(Stream.of(serviceCondition.build()), addOnConditions);
    }

    public static List<LineItem> processServiceLineItems(List<FulfillmentLineItem> items) {
        return items.stream()
                .collect(Collectors.groupingBy(item -> item.service.getServiceItemType()))
                .entrySet()
                .stream()
                .map(entry -> switch (entry.getKey()) {
                    case BOARDING -> processBoardingServices(entry.getValue());
                    case DAYCARE -> processSamePetServices(entry.getValue());
                    default -> processSinglePetService(entry.getValue()); // Grooming, DogWalking, Other
                })
                .flatMap(List::stream)
                .toList();
    }

    static List<LineItem> processBoardingServices(List<FulfillmentLineItem> boardingServices) {
        return boardingServices.stream()
                .collect(Collectors.groupingBy(item -> Map.entry(item.petId(), item.service.getId())))
                .entrySet()
                .stream()
                .map(entry -> {
                    var petId = entry.getKey().getKey();
                    var items = entry.getValue();

                    // 累加所有价格，既是单价也是总价
                    BigDecimal totalPrice =
                            items.stream().map(FulfillmentLineItem::price).reduce(BigDecimal.ZERO, BigDecimal::add);

                    return LineItem.newBuilder()
                            .setPetId(petId)
                            .setService(items.get(0).service)
                            .setUnitPrice(toGoogleMoney(totalPrice))
                            .setQuantity(1)
                            .setTotalPrice(toGoogleMoney(totalPrice))
                            .build();
                })
                .toList();
    }

    private record SamePetServicePriceKey(Long petId, Long serviceId, BigDecimal servicePrice) {}

    public static List<LineItem> processSamePetServices(List<FulfillmentLineItem> allItems) {
        return allItems.stream()
                .collect(Collectors.groupingBy(
                        item -> new SamePetServicePriceKey(item.petId(), item.service.getId(), item.price)))
                .entrySet()
                .stream()
                .map(entry -> {
                    var key = entry.getKey();
                    var items = entry.getValue();
                    int quantity = items.size();

                    var unitPrice = key.servicePrice();
                    var totalPrice = unitPrice.multiply(BigDecimal.valueOf(quantity));
                    var item = items.get(0);
                    var associatedServiceId = item.associatedServiceId != null ? item.associatedServiceId : 0L;

                    return LineItem.newBuilder()
                            .setPetId(key.petId())
                            .setService(item.service)
                            .setUnitPrice(toGoogleMoney(unitPrice))
                            .setQuantity(quantity)
                            .setTotalPrice(toGoogleMoney(totalPrice))
                            .setAssociatedServiceId(associatedServiceId)
                            .build();
                })
                .toList();
    }

    static List<LineItem> processSinglePetService(List<FulfillmentLineItem> items) {
        return items.stream()
                .map(item -> LineItem.newBuilder()
                        .setPetId(item.petId())
                        .setService(item.service)
                        .setUnitPrice(toGoogleMoney(item.price()))
                        .setQuantity(1)
                        .setTotalPrice(toGoogleMoney(item.price()))
                        .build())
                .toList();
    }
}

package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BookingRequestNoteDynamicSqlSupport.bookingRequestNote;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.svc.online.booking.entity.BookingRequestNote;
import com.moego.svc.online.booking.mapper.BookingRequestNoteMapper;
import com.moego.svc.online.booking.mapstruct.BookingRequestNoteConverter;
import jakarta.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class BookingRequestNoteService {
    private final BookingRequestNoteMapper bookingRequestNoteMapper;

    @Nullable
    public BookingRequestNote queryWaitingNotes(long companyId, long bookingRequestId) {
        List<BookingRequestNote> noteList =
                bookingRequestNoteMapper.select(c -> c.where(bookingRequestNote.companyId, isEqualTo(companyId))
                        .and(bookingRequestNote.bookingRequestId, SqlBuilder.isEqualTo(bookingRequestId))
                        .and(bookingRequestNote.deletedAt, isNull()));
        if (!CollectionUtils.isEmpty(noteList)) {
            return noteList.get(0);
        }
        return null;
    }

    public Map<Long, BookingRequestNote> queryMultiWaitingNotes(long companyId, List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        List<BookingRequestNote> notes =
                bookingRequestNoteMapper.select(c -> c.where(bookingRequestNote.companyId, isEqualTo(companyId))
                        .and(bookingRequestNote.bookingRequestId, SqlBuilder.isIn(bookingRequestIds))
                        .and(bookingRequestNote.deletedAt, isNull()));
        return notes.stream()
                .collect(Collectors.toMap(
                        BookingRequestNote::getBookingRequestId,
                        Function.identity(),
                        (existing, replacement) -> replacement));
    }

    public void saveBookingRequestNote(Long companyId, Long customerId, Long bookingRequestId, @Nullable String note) {
        if (note == null) {
            return;
        }
        var nowDate = LocalDateTime.now();
        var existingNote = queryWaitingNotes(companyId, bookingRequestId);

        if (existingNote != null) {
            // 更新现有笔记
            existingNote.setNote(note);
            existingNote.setUpdatedAt(nowDate);
            bookingRequestNoteMapper.updateByPrimaryKeySelective(existingNote);
        } else {
            // 插入新笔记
            var noteBean = new BookingRequestNote();
            noteBean.setCompanyId(companyId);
            noteBean.setCustomerId(customerId);
            noteBean.setBookingRequestId(bookingRequestId);
            noteBean.setNote(note);
            noteBean.setCreatedAt(nowDate);
            noteBean.setUpdatedAt(nowDate);
            bookingRequestNoteMapper.insertSelective(noteBean);
        }
    }

    public List<BookingRequestModel> fillWaitlistModelComments(List<BookingRequestModel> bookingRequestModelList) {
        if (CollectionUtils.isEmpty(bookingRequestModelList)) {
            return bookingRequestModelList;
        }

        var companyId = bookingRequestModelList.get(0).getCompanyId();
        List<Long> bookingRequestIds =
                bookingRequestModelList.stream().map(BookingRequestModel::getId).toList();

        var noteMap = queryMultiWaitingNotes(companyId, bookingRequestIds);

        return bookingRequestModelList.stream()
                .map(model -> {
                    var note = noteMap.get(model.getId());
                    if (note != null && StringUtils.hasText(note.getNote())) {
                        var comment = BookingRequestNoteConverter.INSTANCE.entityToModel(note);
                        return model.toBuilder().setComment(comment).build();
                    }
                    return model;
                })
                .toList();
    }

    // get booking request note by booking request
    @Nullable
    public BookingRequestNote getBookingRequestNoteByBookingRequestId(long companyId, Long bookingRequestId) {
        return queryMultiWaitingNotes(companyId, List.of(bookingRequestId)).get(bookingRequestId);
    }
}

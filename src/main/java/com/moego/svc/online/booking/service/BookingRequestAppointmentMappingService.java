package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.svc.online.booking.mapper.BookingRequestAppointmentMappingDynamicSqlSupport.bookingRequestAppointmentMapping;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.BookingRequestAppointmentMapping;
import com.moego.svc.online.booking.mapper.BookingRequestAppointmentMappingMapper;
import java.util.Collection;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Service
@RequiredArgsConstructor
public class BookingRequestAppointmentMappingService {

    private final BookingRequestAppointmentMappingMapper bookingRequestAppointmentMappingMapper;

    /**
     * Insert an BookingRequestAppointmentMapping.
     *
     * @param entity entity
     * @return insert id
     */
    public long insert(BookingRequestAppointmentMapping entity) {
        if (!isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is empty");
        }
        if (!isNormal(entity.getAppointmentId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "appointmentId is empty");
        }

        bookingRequestAppointmentMappingMapper.insertSelective(entity);

        return entity.getId();
    }

    /**
     * Delete by bookingRequestId.
     *
     * @param bookingRequestId bookingRequestId
     * @return affected rows
     */
    public int deleteByBookingRequestId(long bookingRequestId) {
        return bookingRequestAppointmentMappingMapper.delete(
                c -> c.where(bookingRequestAppointmentMapping.bookingRequestId, isEqualTo(bookingRequestId)));
    }

    /**
     * List by bookingRequestIds
     *
     * @param appointmentIds bookingRequestIds
     * @return list
     */
    public List<BookingRequestAppointmentMapping> listByAppointmentIds(Collection<Long> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return bookingRequestAppointmentMappingMapper.select(
                c -> c.where(bookingRequestAppointmentMapping.appointmentId, isIn(appointmentIds)));
    }
}

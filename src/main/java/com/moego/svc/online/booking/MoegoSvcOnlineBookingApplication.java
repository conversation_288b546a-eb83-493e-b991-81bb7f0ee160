package com.moego.svc.online.booking;

import static java.time.ZoneOffset.UTC;

import java.util.TimeZone;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@MapperScan("com.moego.svc.online.booking.mapper")
@EnableFeignClients({
    "com.moego.server.grooming.client",
    "com.moego.server.customer.client",
    "com.moego.server.business.client",
    "com.moego.server.payment.client",
    "com.moego.server.message.client",
})
public class MoegoSvcOnlineBookingApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(MoegoSvcOnlineBookingApplication.class, args);
    }
}

package com.moego.svc.online.booking.helper;

import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceVaccineRequirementModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetVaccineRequest;
import com.moego.idl.service.offering.v1.ListServiceVaccineRequirementsRequest;
import com.moego.idl.service.offering.v1.ServiceVaccineRequirementServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiPredicate;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class VaccineHelper {
    private final BusinessPetVaccineServiceGrpc.BusinessPetVaccineServiceBlockingStub petVaccineServiceBlockingStub;
    private final ServiceVaccineRequirementServiceGrpc.ServiceVaccineRequirementServiceBlockingStub
            serviceVaccineClient;

    private List<BusinessPetVaccineModel> getBusinessSettingVaccinesByServiceItemType(
            Long companyId, ServiceItemType selectedServiceItemType) {
        List<BusinessPetVaccineModel> vaccinesList = petVaccineServiceBlockingStub
                .listPetVaccine(ListPetVaccineRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getVaccinesList();
        if (CollectionUtils.isEmpty(vaccinesList)) {
            return List.of();
        }

        List<Long> vaccineIds =
                vaccinesList.stream().map(BusinessPetVaccineModel::getId).toList();
        ListServiceVaccineRequirementsRequest.Filters filters =
                ListServiceVaccineRequirementsRequest.Filters.newBuilder()
                        .addAllVaccineIds(vaccineIds)
                        .build();
        if (Objects.nonNull(selectedServiceItemType)) {
            filters = filters.toBuilder()
                    .addServiceItemTypes(selectedServiceItemType)
                    .build();
        }

        int pageNum = 1;
        final int pageSize = 1000;
        List<ServiceVaccineRequirementModel> vaccineRequirements = new ArrayList<>();
        while (true) {
            final var input = ListServiceVaccineRequirementsRequest.newBuilder()
                    .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(pageNum)
                            .setPageSize(pageSize)
                            .build())
                    .setFilter(filters)
                    .build();
            final var output = serviceVaccineClient.listServiceVaccineRequirements(input);
            vaccineRequirements.addAll(output.getServiceVaccineRequirementsList());

            if (output.getPagination().getTotal() <= pageNum * pageSize) {
                break;
            }
            pageNum++;
        }
        return vaccinesList.stream()
                .filter(vaccine -> vaccineRequirements.stream()
                        .anyMatch(requirement -> Objects.equals(requirement.getVaccineId(), vaccine.getId())))
                .toList();
    }

    public boolean vaccineNotMissing(
            List<CustomerProfileRequestDTO.PetProfileDTO> pets,
            Long companyId,
            ServiceItemType selectedServiceItemType) {
        List<BusinessPetVaccineModel> businessSettingVaccines =
                getBusinessSettingVaccinesByServiceItemType(companyId, selectedServiceItemType);

        Predicate<BusinessPetVaccineModel> vaccineAllowAllPetTypes = vaccine -> !vaccine.hasAvailability()
                || !vaccine.getAvailability().hasOnlyForSpecificPetType()
                || !vaccine.getAvailability().getOnlyForSpecificPetType();
        BiPredicate<BusinessPetVaccineModel, PetType> vaccineAllowThisPetType =
                (vaccine, petType) -> vaccine.hasAvailability()
                        && vaccine.getAvailability().hasOnlyForSpecificPetType()
                        && vaccine.getAvailability().getOnlyForSpecificPetType()
                        && vaccine.getAvailability().getAvailablePetTypesList().stream()
                                .anyMatch(petType::equals);
        BiPredicate<BusinessPetVaccineModel, Set<Integer>> hasVaccine =
                (vaccine, set) -> !CollectionUtils.isEmpty(set) && set.contains(Math.toIntExact(vaccine.getId()));

        Predicate<CustomerProfileRequestDTO.PetProfileDTO> petVaccineCheck = pet -> {
            Set<Integer> petVaccineIds = Optional.ofNullable(pet.getVaccineList()).orElse(List.of()).stream()
                    .map(VaccineBindingRecordDto::getVaccineId)
                    .collect(Collectors.toSet());
            return businessSettingVaccines.stream()
                    .filter(vaccine -> vaccineAllowAllPetTypes.test(vaccine)
                            || vaccineAllowThisPetType.test(vaccine, PetType.forNumber(pet.getPetTypeId())))
                    .allMatch(vaccine -> hasVaccine.test(vaccine, petVaccineIds));
        };

        return pets.stream().allMatch(petVaccineCheck);
    }

    public boolean vaccineNotExpired(
            List<CustomerProfileRequestDTO.PetProfileDTO> pets, String appointmentEndDateString) {
        return pets.stream()
                .map(CustomerProfileRequestDTO.PetProfileDTO::getVaccineList)
                .flatMap(List::stream)
                .filter(vaccine -> StringUtils.hasText(vaccine.getExpirationDate()))
                .allMatch(vaccine -> {
                    LocalDate expirationDate = LocalDate.parse(vaccine.getExpirationDate());
                    LocalDate appointmentEndDate = LocalDate.parse(appointmentEndDateString);
                    return expirationDate.isAfter(appointmentEndDate);
                });
    }
}

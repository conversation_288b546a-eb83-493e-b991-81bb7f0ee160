package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DogWalkingServiceDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    public static final DogWalkingServiceDetail dogWalkingServiceDetail = new DogWalkingServiceDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.id")
    public static final SqlColumn<Long> id = dogWalkingServiceDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = dogWalkingServiceDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.pet_id")
    public static final SqlColumn<Long> petId = dogWalkingServiceDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of staff, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.staff_id")
    public static final SqlColumn<Long> staffId = dogWalkingServiceDetail.staffId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.service_id")
    public static final SqlColumn<Long> serviceId = dogWalkingServiceDetail.serviceId;

    /**
     * Database Column Remarks:
     *   The time of current service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.service_time")
    public static final SqlColumn<Integer> serviceTime = dogWalkingServiceDetail.serviceTime;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = dogWalkingServiceDetail.servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.tax_id")
    public static final SqlColumn<Long> taxId = dogWalkingServiceDetail.taxId;

    /**
     * Database Column Remarks:
     *   The start date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.start_date")
    public static final SqlColumn<LocalDate> startDate = dogWalkingServiceDetail.startDate;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.start_time")
    public static final SqlColumn<Integer> startTime = dogWalkingServiceDetail.startTime;

    /**
     * Database Column Remarks:
     *   The end date of the service, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.end_date")
    public static final SqlColumn<LocalDate> endDate = dogWalkingServiceDetail.endDate;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.end_time")
    public static final SqlColumn<Integer> endTime = dogWalkingServiceDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = dogWalkingServiceDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = dogWalkingServiceDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: dog_walking_service_detail.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = dogWalkingServiceDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: dog_walking_service_detail")
    public static final class DogWalkingServiceDetail extends AliasableSqlTable<DogWalkingServiceDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceTime = column("service_time", JDBCType.INTEGER);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Long> taxId = column("tax_id", JDBCType.BIGINT);

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<LocalDate> endDate = column("end_date", JDBCType.DATE);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public DogWalkingServiceDetail() {
            super("dog_walking_service_detail", DogWalkingServiceDetail::new);
        }
    }
}
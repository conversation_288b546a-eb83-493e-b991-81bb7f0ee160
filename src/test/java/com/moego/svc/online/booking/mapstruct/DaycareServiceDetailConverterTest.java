package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceDetailRequest;
import com.moego.idl.utils.v1.StringListValue;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import java.util.List;
import org.junit.jupiter.api.Test;

class DaycareServiceDetailConverterTest {

    @Test
    void updateRequestToEntity() {
        var updateRequest = UpdateDaycareServiceDetailRequest.newBuilder()
                .setId(1L)
                .setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(List.of("2025-01-01", "2025-01-02"))
                        .build())
                .setStartTime(600)
                .setEndTime(1080)
                .build();
        var result = DaycareServiceDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);
        var expect = new DaycareServiceDetail();
        expect.setId(1L);
        expect.setSpecificDates(JsonUtil.toJson(List.of("2025-01-01", "2025-01-02")));
        expect.setStartTime(600);
        expect.setEndTime(1080);
        assertThat(result).isEqualTo(expect);
    }
}

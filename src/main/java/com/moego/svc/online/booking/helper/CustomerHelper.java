package com.moego.svc.online.booking.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/1/15
 */
@Component
@RequiredArgsConstructor
public class CustomerHelper {

    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerStub;

    /**
     * Get customer info by id, throw exception if not found.
     *
     * @param customerId customer id
     * @return customer info
     */
    public BusinessCustomerInfoModel mustGetCustomer(long customerId) {
        var resp = customerStub.getCustomerInfo(
                GetCustomerInfoRequest.newBuilder().setId(customerId).build());
        if (!resp.hasCustomer()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Customer not found: " + customerId);
        }
        return resp.getCustomer();
    }

    /**
     * List customer info by ids.
     *
     * @param customerIds customer ids
     * @return customer id -> customer info
     */
    public Map<Long, BusinessCustomerInfoModel> listCustomer(Collection<? extends Number> customerIds) {
        if (customerIds.isEmpty()) {
            return Map.of();
        }

        var ids = customerIds.stream()
                .map(Number::longValue)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        return customerStub
                .batchGetCustomerInfo(
                        BatchGetCustomerInfoRequest.newBuilder().addAllIds(ids).build())
                .getCustomersList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity(), (o, n) -> o));
    }
}

package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.service.online_booking.v1.CreateFeedingRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.Feeding;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {EnumConverter.class})
public interface FeedingConverter {
    FeedingConverter INSTANCE = Mappers.getMapper(FeedingConverter.class);

    @Mapping(target = "time", ignore = true)
    @Mapping(target = "amountStr", expression = "java(toAmountStr(entity))")
    FeedingModel entityToModel(Feeding entity);

    default List<FeedingModel> entityToModel(List<Feeding> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return List.of();
        }
        return entities.stream().map(this::entityToModel).toList();
    }

    default String toAmountStr(Feeding entity) {
        if (StringUtils.hasText(entity.getAmountStr())) {
            return entity.getAmountStr();
        }
        return entity.getAmount().toString();
    }

    default Feeding createRequestToEntity(
            CreateFeedingRequest createRequest,
            Long bookingRequestId,
            Long serviceDetailId,
            ServiceItemType serviceItemType) {
        Feeding feeding = new Feeding();

        if (createRequest.hasAmount()) {
            feeding.setAmount(BigDecimal.valueOf(createRequest.getAmount()));
        }
        if (createRequest.hasUnit()) {
            feeding.setUnit(createRequest.getUnit());
        }
        if (createRequest.hasFoodType()) {
            feeding.setFoodType(createRequest.getFoodType());
        }
        if (createRequest.hasFoodSource()) {
            feeding.setFoodSource(createRequest.getFoodSource());
        }
        if (createRequest.hasInstruction()) {
            feeding.setInstruction(createRequest.getInstruction());
        }
        if (createRequest.hasCreatedAt()) {
            feeding.setCreatedAt(pbTimestampToDate(createRequest.getCreatedAt()));
        }
        if (createRequest.hasUpdatedAt()) {
            feeding.setUpdatedAt(pbTimestampToDate(createRequest.getUpdatedAt()));
        }
        if (createRequest.hasNote()) {
            feeding.setNote(createRequest.getNote());
        }
        if (createRequest.hasAmountStr()) {
            feeding.setAmountStr(createRequest.getAmountStr());
        }

        feeding.setTime(JsonUtil.toJson(createRequest.getTimeList()));
        feeding.setBookingRequestId(bookingRequestId);
        feeding.setServiceDetailId(serviceDetailId);
        feeding.setServiceDetailType(serviceItemType.getNumber());

        return feeding;
    }

    default List<Feeding> createRequestToEntity(
            List<CreateFeedingRequest> creates,
            Long bookingRequestId,
            Long serviceDetailId,
            ServiceItemType serviceItemType) {
        if (CollectionUtils.isEmpty(creates)) {
            return List.of();
        }
        return creates.stream()
                .map(e -> createRequestToEntity(e, bookingRequestId, serviceDetailId, serviceItemType))
                .toList();
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    @AfterMapping
    default void stringToList(Feeding source, @MappingTarget FeedingModel.Builder target) {
        Optional.ofNullable(source.getTime())
                .filter(StringUtils::hasText)
                .map(e -> JsonUtil.toList(e, Schedule.class).stream()
                        .map(it -> {
                            FeedingModel.FeedingSchedule.Builder b = FeedingModel.FeedingSchedule.newBuilder();
                            Optional.ofNullable(it.label()).ifPresent(b::setLabel);
                            Optional.ofNullable(it.time()).ifPresent(b::setTime);
                            return b.build();
                        })
                        .toList())
                .ifPresent(target::addAllTime);
    }
}

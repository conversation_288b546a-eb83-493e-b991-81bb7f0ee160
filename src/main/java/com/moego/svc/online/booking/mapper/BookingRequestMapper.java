package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.typehandler.BookingRequestAttrTypeHandler;
import com.moego.svc.online.booking.typehandler.BookingRequestPaymentStatusTypeHandler;
import com.moego.svc.online.booking.typehandler.BookingRequestSourceTypeHandler;
import com.moego.svc.online.booking.typehandler.BookingRequestStatusTypeHandler;
import com.moego.svc.online.booking.typehandler.StringToDateTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingRequestMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingRequestMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, customerId, appointmentId, startDate, startTime, endDate, endTime, status, isPrepaid, additionalNote, sourcePlatform, serviceTypeInclude, createdAt, updatedAt, deletedAt, attr, paymentStatus, source, sourceId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingRequest> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingRequest> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingRequestResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.BIGINT),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_date", property="startDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_date", property="endDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="status", property="status", typeHandler=BookingRequestStatusTypeHandler.class, jdbcType=JdbcType.INTEGER),
        @Result(column="is_prepaid", property="isPrepaid", jdbcType=JdbcType.BIT),
        @Result(column="additional_note", property="additionalNote", jdbcType=JdbcType.VARCHAR),
        @Result(column="source_platform", property="sourcePlatform", jdbcType=JdbcType.VARCHAR),
        @Result(column="service_type_include", property="serviceTypeInclude", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="attr", property="attr", typeHandler=BookingRequestAttrTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="payment_status", property="paymentStatus", typeHandler=BookingRequestPaymentStatusTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="source", property="source", typeHandler=BookingRequestSourceTypeHandler.class, jdbcType=JdbcType.SMALLINT),
        @Result(column="source_id", property="sourceId", jdbcType=JdbcType.BIGINT)
    })
    List<BookingRequest> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingRequestResult")
    Optional<BookingRequest> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int insertMultiple(Collection<BookingRequest> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingRequest, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(customerId).toProperty("customerId")
            .map(appointmentId).toProperty("appointmentId")
            .map(startDate).toProperty("startDate")
            .map(startTime).toProperty("startTime")
            .map(endDate).toProperty("endDate")
            .map(endTime).toProperty("endTime")
            .map(status).toProperty("status")
            .map(isPrepaid).toProperty("isPrepaid")
            .map(additionalNote).toProperty("additionalNote")
            .map(sourcePlatform).toProperty("sourcePlatform")
            .map(serviceTypeInclude).toProperty("serviceTypeInclude")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(attr).toProperty("attr")
            .map(paymentStatus).toProperty("paymentStatus")
            .map(source).toProperty("source")
            .map(sourceId).toProperty("sourceId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int insertSelective(BookingRequest row) {
        return MyBatis3Utils.insert(this::insert, row, bookingRequest, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(customerId).toPropertyWhenPresent("customerId", row::getCustomerId)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
            .map(isPrepaid).toPropertyWhenPresent("isPrepaid", row::getIsPrepaid)
            .map(additionalNote).toPropertyWhenPresent("additionalNote", row::getAdditionalNote)
            .map(sourcePlatform).toPropertyWhenPresent("sourcePlatform", row::getSourcePlatform)
            .map(serviceTypeInclude).toPropertyWhenPresent("serviceTypeInclude", row::getServiceTypeInclude)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(attr).toPropertyWhenPresent("attr", row::getAttr)
            .map(paymentStatus).toPropertyWhenPresent("paymentStatus", row::getPaymentStatus)
            .map(source).toPropertyWhenPresent("source", row::getSource)
            .map(sourceId).toPropertyWhenPresent("sourceId", row::getSourceId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default Optional<BookingRequest> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default List<BookingRequest> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default List<BookingRequest> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default Optional<BookingRequest> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingRequest, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingRequest row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(customerId).equalTo(row::getCustomerId)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(startDate).equalTo(row::getStartDate)
                .set(startTime).equalTo(row::getStartTime)
                .set(endDate).equalTo(row::getEndDate)
                .set(endTime).equalTo(row::getEndTime)
                .set(status).equalTo(row::getStatus)
                .set(isPrepaid).equalTo(row::getIsPrepaid)
                .set(additionalNote).equalTo(row::getAdditionalNote)
                .set(sourcePlatform).equalTo(row::getSourcePlatform)
                .set(serviceTypeInclude).equalTo(row::getServiceTypeInclude)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(attr).equalTo(row::getAttr)
                .set(paymentStatus).equalTo(row::getPaymentStatus)
                .set(source).equalTo(row::getSource)
                .set(sourceId).equalTo(row::getSourceId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingRequest row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(customerId).equalToWhenPresent(row::getCustomerId)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(isPrepaid).equalToWhenPresent(row::getIsPrepaid)
                .set(additionalNote).equalToWhenPresent(row::getAdditionalNote)
                .set(sourcePlatform).equalToWhenPresent(row::getSourcePlatform)
                .set(serviceTypeInclude).equalToWhenPresent(row::getServiceTypeInclude)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(attr).equalToWhenPresent(row::getAttr)
                .set(paymentStatus).equalToWhenPresent(row::getPaymentStatus)
                .set(source).equalToWhenPresent(row::getSource)
                .set(sourceId).equalToWhenPresent(row::getSourceId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request")
    default int updateByPrimaryKeySelective(BookingRequest row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(customerId).equalToWhenPresent(row::getCustomerId)
            .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(status).equalToWhenPresent(row::getStatus)
            .set(isPrepaid).equalToWhenPresent(row::getIsPrepaid)
            .set(additionalNote).equalToWhenPresent(row::getAdditionalNote)
            .set(sourcePlatform).equalToWhenPresent(row::getSourcePlatform)
            .set(serviceTypeInclude).equalToWhenPresent(row::getServiceTypeInclude)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(attr).equalToWhenPresent(row::getAttr)
            .set(paymentStatus).equalToWhenPresent(row::getPaymentStatus)
            .set(source).equalToWhenPresent(row::getSource)
            .set(sourceId).equalToWhenPresent(row::getSourceId)
            .where(id, isEqualTo(row::getId))
        );
    }
}
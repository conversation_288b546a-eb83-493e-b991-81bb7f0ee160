package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.DayHourLimitDynamicSqlSupport.dayHourLimit;
import static com.moego.svc.online.booking.mapper.DayHourLimitGroupDynamicSqlSupport.dayHourLimitGroup;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.LimitationGroupDef;
import com.moego.svc.online.booking.entity.DayHourLimit;
import com.moego.svc.online.booking.entity.DayHourLimitGroup;
import com.moego.svc.online.booking.mapper.DayHourLimitGroupMapper;
import com.moego.svc.online.booking.mapper.DayHourLimitMapper;
import com.moego.svc.online.booking.utils.OBAvailabilityDayHourUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class BookingLimitationService {
    private final DayHourLimitMapper dayHourLimitMapper;
    private final DayHourLimitGroupMapper dayHourLimitGroupMapper;

    public Map<Long, DayHourLimit> getDayHourLimitMapByLimitIds(List<Long> limitIds) {
        if (limitIds.isEmpty()) {
            return Map.of();
        }
        return dayHourLimitMapper.select(c -> c.where(dayHourLimit.id, isIn(limitIds))).stream()
                .collect(Collectors.toMap(DayHourLimit::getId, Function.identity()));
    }

    public void deleteByIds(List<Long> ids) {
        if (ids.isEmpty()) {
            return;
        }

        var groupIds = dayHourLimitMapper.select(c -> c.where(dayHourLimit.id, isIn(ids))).stream()
                .map(DayHourLimit::getGroupId)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        if (!CollectionUtils.isEmpty(groupIds)) {
            deleteGroupByIds(groupIds);
        }

        dayHourLimitMapper.delete(c -> c.where(dayHourLimit.id, isIn(ids)));
    }

    public void batchCreate(List<DayHourLimit> dayHourLimits) {
        if (dayHourLimits.isEmpty()) {
            return;
        }
        dayHourLimits.forEach(dayHourLimitMapper::insertSelective);
    }

    public Map<Long, DayHourLimitGroup> getDayHourLimitGroupMap(List<Long> groupIds) {
        if (groupIds.isEmpty()) {
            return Map.of();
        }
        return dayHourLimitGroupMapper.select(c -> c.where(dayHourLimitGroup.id, isIn(groupIds))).stream()
                .collect(Collectors.toMap(DayHourLimitGroup::getId, Function.identity()));
    }

    public void deleteGroupByIds(List<Long> groupIds) {
        if (groupIds.isEmpty()) {
            return;
        }
        dayHourLimitGroupMapper.delete(c -> c.where(dayHourLimitGroup.id, isIn(groupIds)));
    }

    public List<Long> batchCreateByLimitationGroup(List<LimitationGroupDef> limitGroupDefs) {
        if (CollectionUtils.isEmpty(limitGroupDefs)) {
            return List.of();
        }
        List<Long> limitIds = new ArrayList<>();
        limitGroupDefs.forEach(limitationGroup -> {
            var dayHourLimitGroup = new DayHourLimitGroup();
            dayHourLimitGroup.setOnlyAcceptSelected(limitationGroup.getOnlyAcceptSelected());
            dayHourLimitGroupMapper.insertSelective(dayHourLimitGroup);

            var dayHourLimits =
                    OBAvailabilityDayHourUtils.buildDayHourLimits(limitationGroup, dayHourLimitGroup.getId());
            batchCreate(dayHourLimits);
            dayHourLimits.stream().map(DayHourLimit::getId).forEach(limitIds::add);
        });
        return limitIds;
    }
}

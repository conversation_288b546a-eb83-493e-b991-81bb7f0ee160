package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.StaffAvailabilityDayHourDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.StaffAvailabilityDayHour;
import com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface StaffAvailabilityDayHourMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<StaffAvailabilityDayHourMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    BasicColumn[] selectList = BasicColumn.columnList(id, dayType, dayId, startTime, endTime, capacity, limitIds, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<StaffAvailabilityDayHour> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<StaffAvailabilityDayHour> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="StaffAvailabilityDayHourResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="day_type", property="dayType", jdbcType=JdbcType.INTEGER),
        @Result(column="day_id", property="dayId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="capacity", property="capacity", jdbcType=JdbcType.INTEGER),
        @Result(column="limit_ids", property="limitIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<StaffAvailabilityDayHour> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("StaffAvailabilityDayHourResult")
    Optional<StaffAvailabilityDayHour> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int insertMultiple(Collection<StaffAvailabilityDayHour> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, staffAvailabilityDayHour, c ->
            c.map(dayType).toProperty("dayType")
            .map(dayId).toProperty("dayId")
            .map(startTime).toProperty("startTime")
            .map(endTime).toProperty("endTime")
            .map(capacity).toProperty("capacity")
            .map(limitIds).toProperty("limitIds")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int insertSelective(StaffAvailabilityDayHour row) {
        return MyBatis3Utils.insert(this::insert, row, staffAvailabilityDayHour, c ->
            c.map(dayType).toPropertyWhenPresent("dayType", row::getDayType)
            .map(dayId).toPropertyWhenPresent("dayId", row::getDayId)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(capacity).toPropertyWhenPresent("capacity", row::getCapacity)
            .map(limitIds).toPropertyWhenPresent("limitIds", row::getLimitIds)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default Optional<StaffAvailabilityDayHour> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default List<StaffAvailabilityDayHour> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default List<StaffAvailabilityDayHour> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default Optional<StaffAvailabilityDayHour> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, staffAvailabilityDayHour, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    static UpdateDSL<UpdateModel> updateAllColumns(StaffAvailabilityDayHour row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dayType).equalTo(row::getDayType)
                .set(dayId).equalTo(row::getDayId)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(capacity).equalTo(row::getCapacity)
                .set(limitIds).equalTo(row::getLimitIds)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(StaffAvailabilityDayHour row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(dayType).equalToWhenPresent(row::getDayType)
                .set(dayId).equalToWhenPresent(row::getDayId)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(capacity).equalToWhenPresent(row::getCapacity)
                .set(limitIds).equalToWhenPresent(row::getLimitIds)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    default int updateByPrimaryKeySelective(StaffAvailabilityDayHour row) {
        return update(c ->
            c.set(dayType).equalToWhenPresent(row::getDayType)
            .set(dayId).equalToWhenPresent(row::getDayId)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(capacity).equalToWhenPresent(row::getCapacity)
            .set(limitIds).equalToWhenPresent(row::getLimitIds)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
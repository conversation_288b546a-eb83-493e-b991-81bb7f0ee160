package com.moego.svc.online.booking.helper;

import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class OfferingHelper {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;

    public Map<Long, ServiceBriefView> getServiceMap(long companyId, List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        List<ServiceBriefView> serviceList = serviceManagementService
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllServiceIds(serviceIds.stream().distinct().toList())
                        .build())
                .getServicesList();
        return serviceList.stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
    }

    public List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            long companyId, List<CustomizedServiceQueryCondition> conditions) {
        if (CollectionUtils.isEmpty(conditions)) {
            return List.of();
        }

        return serviceManagementService
                .batchGetCustomizedService(BatchGetCustomizedServiceRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllQueryConditionList(conditions)
                        .build())
                .getCustomizedServiceListList();
    }
}

package com.moego.svc.online.booking.service;

import com.moego.idl.models.business_customer.v1.BusinessPetFeedingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.business_customer.v1.BatchCreateFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.BatchCreateMedicationScheduleRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetMedicationScheduleServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleResponse;
import com.moego.idl.service.business_customer.v1.ListPetMedicationScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetMedicationScheduleResponse;
import com.moego.svc.online.booking.mapstruct.PetFeedingConverter;
import com.moego.svc.online.booking.mapstruct.PetMedicationConverter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@Service
@RequiredArgsConstructor
public class FeedingMedicationService {

    private final BusinessPetMedicationScheduleServiceGrpc.BusinessPetMedicationScheduleServiceBlockingStub
            petMedicationScheduleService;
    private final BusinessPetFeedingScheduleServiceGrpc.BusinessPetFeedingScheduleServiceBlockingStub
            petFeedingScheduleService;

    public void syncPetDetailDef(Long companyId, BookingRequestModel bookingRequest) {
        Map<Long, List<MedicationModel>> newPetMedications = new HashMap<>();
        Map<Long, List<FeedingModel>> newPetFeedings = new HashMap<>();

        bookingRequest.getServicesList().forEach(service -> {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    BookingRequestModel.BoardingService boarding = service.getBoarding();
                    long petId = boarding.getService().getPetId();
                    var medications = boarding.getMedicationsList().stream()
                            .filter(k -> k.getTimeCount() > 0)
                            .toList();
                    if (!CollectionUtils.isEmpty(medications)) {
                        newPetMedications
                                .computeIfAbsent(petId, k -> new ArrayList<>())
                                .addAll(medications);
                    }

                    var feedings = boarding.getFeedingsList().stream()
                            .filter(k -> k.getTimeCount() > 0)
                            .toList();
                    if (!CollectionUtils.isEmpty(feedings)) {
                        newPetFeedings
                                .computeIfAbsent(petId, k -> new ArrayList<>())
                                .addAll(feedings);
                    }
                }
                case DAYCARE -> {
                    BookingRequestModel.DaycareService daycare = service.getDaycare();
                    long petId = daycare.getService().getPetId();
                    var medications = daycare.getMedicationsList().stream()
                            .filter(k -> k.getTimeCount() > 0)
                            .toList();
                    if (!CollectionUtils.isEmpty(medications)) {
                        newPetMedications
                                .computeIfAbsent(petId, k -> new ArrayList<>())
                                .addAll(medications);
                    }

                    var feedings = daycare.getFeedingsList().stream()
                            .filter(k -> k.getTimeCount() > 0)
                            .toList();
                    if (!CollectionUtils.isEmpty(feedings)) {
                        newPetFeedings
                                .computeIfAbsent(petId, k -> new ArrayList<>())
                                .addAll(feedings);
                    }
                }
                default -> {}
            }
        });

        syncPetProfileMedication(companyId, newPetMedications);
        syncPetProfileFeeding(companyId, newPetFeedings);
    }

    // sync for per pet if not exist
    private void syncPetProfileMedication(Long companyId, Map<Long, List<MedicationModel>> petMedications) {
        if (CollectionUtils.isEmpty(petMedications)) {
            return;
        }
        Set<Long> petIds = new HashSet<>(petMedications.keySet());
        Map<Long, ListPetMedicationScheduleResponse> existPetMedication =
                getPetMedicationSchedule(companyId, new ArrayList<>(petIds));

        List<BusinessPetMedicationScheduleDef> toCreate = new ArrayList<>();
        petMedications.forEach((petId, medications) -> {
            if (existPetMedication.containsKey(petId)) {
                return;
            }
            if (CollectionUtils.isEmpty(medications)) {
                return;
            }
            toCreate.addAll(medications.stream()
                    .map(k -> PetMedicationConverter.INSTANCE.toBusinessPetMedicationScheduleDef(petId, k))
                    .toList());
        });

        batchCreateMedicationSchedule(companyId, toCreate);
    }

    private void batchCreateMedicationSchedule(
            Long companyId, List<BusinessPetMedicationScheduleDef> petMedicationSchedules) {
        if (CollectionUtils.isEmpty(petMedicationSchedules)) {
            return;
        }
        petMedicationScheduleService.batchCreateMedicationSchedule(BatchCreateMedicationScheduleRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllMedicationSchedules(petMedicationSchedules)
                .build());
    }

    private ListPetMedicationScheduleResponse listMedicationSchedule(Long companyId, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return ListPetMedicationScheduleResponse.getDefaultInstance();
        }
        return petMedicationScheduleService.listPetMedicationSchedule(ListPetMedicationScheduleRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
    }

    private Map<Long, ListPetMedicationScheduleResponse> getPetMedicationSchedule(Long companyId, List<Long> petIds) {
        ListPetMedicationScheduleResponse response = listMedicationSchedule(companyId, petIds);
        Map<Long, List<BusinessPetMedicationModel>> petMedicationMap = response.getMedicationsList().stream()
                .collect(Collectors.groupingBy(BusinessPetMedicationModel::getPetId));
        Map<Long, List<BusinessPetScheduleSettingModel>> medicationScheduleMap = response.getSchedulesList().stream()
                .collect(Collectors.groupingBy(BusinessPetScheduleSettingModel::getScheduleId));

        Map<Long, ListPetMedicationScheduleResponse> result = new HashMap<>();

        petMedicationMap.forEach((petId, medications) -> result.put(
                petId,
                ListPetMedicationScheduleResponse.newBuilder()
                        .addAllMedications(medications)
                        .addAllSchedules(medications.stream()
                                .map(medication -> medicationScheduleMap.get(medication.getId()))
                                .flatMap(List::stream)
                                .toList())
                        .build()));
        return result;
    }

    // sync for per pet if not exist
    private void syncPetProfileFeeding(Long companyId, Map<Long, List<FeedingModel>> petFeedings) {
        if (CollectionUtils.isEmpty(petFeedings)) {
            return;
        }
        Set<Long> petIds = new HashSet<>(petFeedings.keySet());
        Map<Long, ListPetFeedingScheduleResponse> existPetFeeding =
                getPetFeedingSchedule(companyId, new ArrayList<>(petIds));

        List<BusinessPetFeedingScheduleDef> toCreate = new ArrayList<>();
        petFeedings.forEach((petId, feedings) -> {
            if (existPetFeeding.containsKey(petId)) {
                return;
            }
            if (CollectionUtils.isEmpty(feedings)) {
                return;
            }
            toCreate.addAll(feedings.stream()
                    .map(k -> PetFeedingConverter.INSTANCE.toBusinessPetFeedingScheduleDef(petId, k))
                    .toList());
        });

        batchCreateFeedingSchedule(companyId, toCreate);
    }

    private void batchCreateFeedingSchedule(Long companyId, List<BusinessPetFeedingScheduleDef> petFeedingSchedules) {
        if (CollectionUtils.isEmpty(petFeedingSchedules)) {
            return;
        }
        petFeedingScheduleService.batchCreateFeedingSchedule(BatchCreateFeedingScheduleRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllFeedingSchedules(petFeedingSchedules)
                .build());
    }

    private ListPetFeedingScheduleResponse listFeedingSchedule(Long companyId, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return ListPetFeedingScheduleResponse.getDefaultInstance();
        }
        return petFeedingScheduleService.listPetFeedingSchedule(ListPetFeedingScheduleRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
    }

    private Map<Long, ListPetFeedingScheduleResponse> getPetFeedingSchedule(Long companyId, List<Long> petIds) {
        ListPetFeedingScheduleResponse response = listFeedingSchedule(companyId, petIds);
        Map<Long, List<BusinessPetFeedingModel>> petFeedingMap =
                response.getFeedingsList().stream().collect(Collectors.groupingBy(BusinessPetFeedingModel::getPetId));
        Map<Long, List<BusinessPetScheduleSettingModel>> feedingScheduleMap = response.getSchedulesList().stream()
                .collect(Collectors.groupingBy(BusinessPetScheduleSettingModel::getScheduleId));

        Map<Long, ListPetFeedingScheduleResponse> result = new HashMap<>();

        petFeedingMap.forEach((petId, feeding) -> result.put(
                petId,
                ListPetFeedingScheduleResponse.newBuilder()
                        .addAllFeedings(feeding)
                        .addAllSchedules(feeding.stream()
                                .map(medication -> feedingScheduleMap.get(medication.getId()))
                                .flatMap(List::stream)
                                .toList())
                        .build()));
        return result;
    }
}

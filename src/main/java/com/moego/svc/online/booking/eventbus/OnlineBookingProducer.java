package com.moego.svc.online.booking.eventbus;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.event_bus.v1.EventData;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.event_bus.v1.OnlineBookingAcceptedEvent;
import com.moego.idl.models.event_bus.v1.OnlineBookingSubmittedEvent;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.event_bus.event.EventRecord;
import com.moego.lib.event_bus.producer.Producer;
import com.moego.svc.online.booking.client.OrganizationClient;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.service.BookingRequestService;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class OnlineBookingProducer {
    private final BookingRequestService bookingRequestService;
    private final Producer producer;
    private final OrganizationClient organizationClient;
    private static final String ONLINE_BOOKING_EVENT_TOPIC = "moego.erp.online_booking";

    public void pushOnlineBookingSubmittedEvent(Long id) {
        ThreadPool.execute(() -> {
            var entity = bookingRequestService.mustGet(id);
            var timeZoneName = organizationClient.getCompanyTimeZoneName(entity.getCompanyId());
            producer.send(ONLINE_BOOKING_EVENT_TOPIC, buildOnlineBookingSubmittedEvent(entity, timeZoneName));
        });
    }

    public void pushOnlineBookingAcceptedEvent(BookingRequestModel bookingRequestModel) {
        ThreadPool.execute(() -> {
            var timeZoneName = organizationClient.getCompanyTimeZoneName(bookingRequestModel.getCompanyId());
            producer.send(
                    ONLINE_BOOKING_EVENT_TOPIC, buildOnlineBookingAcceptedEvent(bookingRequestModel, timeZoneName));
        });
    }

    private static EventRecord buildOnlineBookingSubmittedEvent(BookingRequest entity, String timeZoneName) {
        return EventRecord.builder()
                .id(entity.getId().toString())
                .time(entity.getCreatedAt().toInstant())
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(entity.getCompanyId())
                                .setBusinessId(entity.getBusinessId())
                                .build())
                        .setOnlineBookingSubmittedEvent(OnlineBookingSubmittedEvent.newBuilder()
                                .setId(entity.getId())
                                .setCustomerId(entity.getCustomerId())
                                .setStartTime(getTimestamp(timeZoneName, entity.getStartDate(), entity.getStartTime()))
                                .addAllServiceItemTypes(
                                        ServiceItemEnum.convertBitValueList(entity.getServiceTypeInclude()).stream()
                                                .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                                                .toList())
                                .build())
                        .build())
                .type(EventType.ONLINE_BOOKING_SUBMITTED)
                .build();
    }

    private static EventRecord buildOnlineBookingAcceptedEvent(BookingRequestModel entity, String timeZoneName) {
        return EventRecord.builder()
                .id(String.valueOf(entity.getId()))
                .time(Instant.ofEpochMilli(Timestamps.toMillis(entity.getCreatedAt())))
                .detail(EventData.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(entity.getCompanyId())
                                .setBusinessId(entity.getBusinessId())
                                .build())
                        .setOnlineBookingAcceptedEvent(OnlineBookingAcceptedEvent.newBuilder()
                                .setId(entity.getId())
                                .setCustomerId(entity.getCustomerId())
                                .setStartTime(getTimestamp(timeZoneName, entity.getStartDate(), entity.getStartTime()))
                                .addAllServiceItemTypes(convertServiceItemTypes(entity.getServiceTypeInclude()))
                                .build())
                        .build())
                .type(EventType.ONLINE_BOOKING_ACCEPTED)
                .build();
    }

    private static Timestamp getTimestamp(String timeZoneName, String date, Integer minutes) {
        long seconds = LocalDate.parse(date)
                .atStartOfDay(ZoneId.of(timeZoneName))
                .plusMinutes(minutes)
                .toEpochSecond();
        return Timestamp.newBuilder().setSeconds(seconds).build();
    }

    private static List<ServiceItemType> convertServiceItemTypes(Integer serviceTypeInclude) {
        return ServiceItemEnum.convertBitValueList(serviceTypeInclude).stream()
                .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                .toList();
    }
}

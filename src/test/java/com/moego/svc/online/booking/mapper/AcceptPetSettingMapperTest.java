package com.moego.svc.online.booking.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.svc.online.booking.entity.AcceptPetSetting;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@Disabled("For local test")
class AcceptPetSettingMapperTest {

    @Autowired
    private AcceptPetSettingMapper mapper;

    @Test
    @Transactional // Rollback after test
    void testArrayColumn() {
        long id = 3;

        AcceptPetSetting setting = mustGet(id);

        setting.setAcceptedPetTypes(new Integer[0]);
        mapper.updateByPrimaryKeySelective(setting);

        assertThat(mustGet(id).getAcceptedPetTypes()).isEmpty();
    }

    private AcceptPetSetting mustGet(long id) {
        return mapper.selectByPrimaryKey(id).orElseThrow(() -> new IllegalStateException("Not found"));
    }
}

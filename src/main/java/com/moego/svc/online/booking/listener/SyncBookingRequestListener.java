package com.moego.svc.online.booking.listener;

import com.moego.common.constant.ActiveMQConstant;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.params.event.CustomerEventParams;
import com.moego.server.customer.params.event.CustomerPetEventParams;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.ob.GroomingOnlyBookingRequestDTO;
import com.moego.server.grooming.dto.ob.OBRequestSyncDTO;
import com.moego.server.grooming.params.ob.BookingRequestEventParams;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.entity.GroomingAutoAssign;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.service.BookingRequestService;
import com.moego.svc.online.booking.service.GroomingAddOnDetailService;
import com.moego.svc.online.booking.service.GroomingAutoAssignService;
import com.moego.svc.online.booking.service.GroomingServiceDetailService;
import jakarta.jms.JMSException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.command.ActiveMQTextMessage;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/3/25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SyncBookingRequestListener {

    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final BookingRequestService bookingRequestService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;
    private final StringRedisTemplate stringRedisTemplate;
    private final GroomingServiceDetailService groomingServiceDetailService;
    private final GroomingAutoAssignService groomingAutoAssignService;
    private final GroomingAddOnDetailService groomingAddOnDetailService;

    private static final Set<Integer> BOOKING_REQUEST_END_STATUSES = Set.of(
            BookingRequestStatus.SCHEDULED_VALUE,
            BookingRequestStatus.DECLINED_VALUE,
            BookingRequestStatus.DELETED_VALUE);

    @JmsListener(destination = ActiveMQConstant.ONLINE_BOOKING_REQUEST_QUEUE, containerFactory = "queueListenerFactory")
    public void appointmentEvent(ActiveMQTextMessage message) throws JMSException {
        try {
            BookingRequestEventParams params = JsonUtil.toBean(message.getText(), BookingRequestEventParams.class);
            log.info("Received booking request event: {}", params);
            try {
                processEvent(params);
            } catch (Exception e) {
                log.error("Failed to process event", e);
                stringRedisTemplate.opsForSet().add(OBRequestSyncDTO.FAILED_KEY, JsonUtil.toJson(params));
            }
        } catch (Exception e) {
            log.error("Failed to get message text from ActiveMQTextMessage", e);
        } finally {
            message.acknowledge();
        }
    }

    @JmsListener(destination = ActiveMQConstant.CUSTOMER_CUSTOMER_QUEUE, containerFactory = "queueListenerFactory")
    public void customerEvent(ActiveMQTextMessage message) throws JMSException {
        try {
            CustomerEventParams params = JsonUtil.toBean(message.getText(), CustomerEventParams.class);
            log.info("Received customer event: {}", params);
            switch (params.getEvent()) {
                case DELETED -> deleteBookingRequestByCustomer(params);
                default -> {}
            }
        } catch (Exception e) {
            log.error("Failed to get message text from ActiveMQTextMessage", e);
        } finally {
            message.acknowledge();
        }
    }

    private void deleteBookingRequestByCustomer(CustomerEventParams params) {
        int i = bookingRequestService.deleteByCustomerId(params.getBusinessId(), params.getCustomerId());
        log.info("Deleted customer [{}] booking request count: [{}]", params.getCustomerId(), i);
    }

    @JmsListener(destination = ActiveMQConstant.CUSTOMER_PET_QUEUE, containerFactory = "queueListenerFactory")
    public void customerPetEvent(ActiveMQTextMessage message) throws JMSException {
        try {
            CustomerPetEventParams params = JsonUtil.toBean(message.getText(), CustomerPetEventParams.class);
            log.info("Received customer pet event: {}", params);
            switch (params.getEvent()) {
                case DELETED, PASS_AWAY -> deleteServicesByPet(params);
                default -> {}
            }
        } catch (Exception e) {
            log.error("Failed to get message text from ActiveMQTextMessage", e);
        } finally {
            message.acknowledge();
        }
    }

    private void deleteServicesByPet(CustomerPetEventParams params) {
        List<BookingRequest> bookingRequests =
                bookingRequestService.listByCustomerId(params.getBusinessId(), params.getCustomerId());
        bookingRequests.forEach(bookingRequest -> {
            List<GroomingServiceDetail> details =
                    groomingServiceDetailService.listByBookingRequestId(bookingRequest.getId());
            List<Long> ids = details.stream()
                    .filter(detail -> detail.getPetId().equals(params.getPetId().longValue()))
                    .map(GroomingServiceDetail::getId)
                    .toList();
            int affectedRows = groomingServiceDetailService.delete(ids);
            List<GroomingAddOnDetail> addOnDetails =
                    groomingAddOnDetailService.listByBookingRequestId(bookingRequest.getId());
            List<Long> addOnDetailIds = addOnDetails.stream()
                    .filter(detail -> detail.getPetId().equals(params.getPetId().longValue()))
                    .map(GroomingAddOnDetail::getId)
                    .toList();
            groomingAddOnDetailService.delete(addOnDetailIds);
            // pet details 删完后，删除 booking request
            if (affectedRows == details.size()) {
                bookingRequestService.delete(bookingRequest.getId());
            }
        });
    }

    public void processEvent(BookingRequestEventParams params) {
        switch (params.getEvent()) {
            case SUBMITTED -> createGroomingOnlyBookingRequest(params.getAppointmentId());
            case AUTO_ASSIGN -> createGroomingAutoAssign(params.getAppointmentId());
            case SCHEDULED -> scheduleGroomingOnlyBookingRequest(params.getBusinessId(), params.getAppointmentId());
            case MOVED_TO_WAIT_LIST -> moveGroomingOnlyBookingRequestToWaitList(
                    params.getBusinessId(), params.getAppointmentId());
            case DECLINED -> declineGroomingOnlyBookingRequest(params.getBusinessId(), params.getAppointmentId());
            case DELETED -> deleteGroomingOnlyBookingRequest(params.getBusinessId(), params.getAppointmentId());
            case VALIDATE -> validateBookingRequest(params.getBusinessId(), params.getAppointmentId());
            case PAYMENT_FAILED -> updateBookingRequestStatus(
                    params.getBusinessId(), params.getAppointmentId(), BookingRequestStatus.PAYMENT_FAILED);
            default -> {}
        }
    }

    private void setAdd(Integer businessId, Integer appointmentId) {
        stringRedisTemplate
                .opsForSet()
                .add(
                        OBRequestSyncDTO.FAILED_KEY,
                        JsonUtil.toJson(new BookingRequestEventParams()
                                .setBusinessId(businessId)
                                .setAppointmentId(appointmentId)
                                .setEvent(BookingRequestEventParams.BookingRequestEvent.SUBMITTED)));
    }

    private void validateBookingRequest(Integer businessId, Integer appointmentId) {
        GroomingOnlyBookingRequestDTO dto = onlineBookingClient.getBookingRequest(appointmentId);
        if (dto == null) {
            log.error("Failed to get source booking request by appointmentId: {}", appointmentId);
            setAdd(businessId, appointmentId);
            return;
        }
        // 没有 grooming service 直接跳过
        if (CollectionUtils.isEmpty(dto.getGroomingServiceDetails())) {
            return;
        }
        BookingRequest targetBookingRequest = bookingRequestService.getByAppointmentId(businessId, appointmentId);
        if (targetBookingRequest == null) {
            log.error("Failed to get target booking request, appointment: {}", JsonUtil.toJson(dto));
            setAdd(businessId, appointmentId);
            return;
        }
        // 到达终态直接跳过
        if (BOOKING_REQUEST_END_STATUSES.contains(
                targetBookingRequest.getStatus().getNumber())) {
            return;
        }
        List<ServiceBriefView> services = getServices(dto);
        if (CollectionUtils.isEmpty(services)) {
            log.error("Failed to get source service details, appointment: {}", JsonUtil.toJson(dto));
            setAdd(businessId, appointmentId);
            return;
        }
        // 分别校验 booking request / service details / auto assign
        if (validateBookingRequest(dto, targetBookingRequest, services)) {
            setAdd(businessId, appointmentId);
        } else if (validateGroomingServiceDetails(dto, targetBookingRequest, services)) {
            setAdd(businessId, appointmentId);
        } else if (validateGroomingAutoAssign(dto, targetBookingRequest)) {
            setAdd(businessId, appointmentId);
        }
    }

    private boolean validateBookingRequest(
            GroomingOnlyBookingRequestDTO dto, BookingRequest target, List<ServiceBriefView> services) {
        Integer serviceTypeInclude = ServiceItemEnum.convertBitValueList(
                services.stream().map(ServiceBriefView::getServiceItemTypeValue).toList());
        BookingRequest source = buildBookingRequest(dto, serviceTypeInclude);
        boolean result = !Objects.equals(source.getCompanyId(), target.getCompanyId())
                || !Objects.equals(source.getBusinessId(), target.getBusinessId())
                || !Objects.equals(source.getCustomerId(), target.getCustomerId())
                || !Objects.equals(source.getAppointmentId(), target.getAppointmentId())
                || !Objects.equals(source.getStartDate(), target.getStartDate())
                || !Objects.equals(source.getStartTime(), target.getStartTime())
                || !Objects.equals(source.getEndDate(), target.getEndDate())
                || !Objects.equals(source.getEndTime(), target.getEndTime())
                || !Objects.equals(source.getStatus(), target.getStatus())
                || !Objects.equals(source.getIsPrepaid(), target.getIsPrepaid())
                || !Objects.equals(source.getAdditionalNote(), target.getAdditionalNote())
                || !Objects.equals(source.getSourcePlatform(), target.getSourcePlatform())
                || !Objects.equals(source.getServiceTypeInclude(), target.getServiceTypeInclude());
        if (result) {
            log.error(
                    "Failed to validate booking request, appointment: {}, source: {}, target: {}",
                    JsonUtil.toJson(dto),
                    JsonUtil.toJson(source),
                    JsonUtil.toJson(target));
        }
        return result;
    }

    private boolean validateGroomingServiceDetails(
            GroomingOnlyBookingRequestDTO dto, BookingRequest bookingRequest, List<ServiceBriefView> services) {
        Map<Long, ServiceBriefView> serviceMap =
                services.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));

        Map<Integer, GroomingServiceDetail> petServiceMap =
                filterGroomingServiceDetailMap(dto.getGroomingServiceDetails(), serviceMap);
        Map<Integer, List<GroomingAddOnDetail>> petAddOnsMap =
                filterGroomingAddOnDetailMap(dto.getGroomingServiceDetails(), serviceMap);

        List<GroomingServiceDetail> serviceDetails =
                groomingServiceDetailService.listByBookingRequestId(bookingRequest.getId());
        for (GroomingServiceDetail target : serviceDetails) {
            GroomingServiceDetail source = petServiceMap.get(target.getPetId().intValue());
            if (source == null) {
                log.error("Failed to get source service detail, appointment: {}", JsonUtil.toJson(dto));
                return true;
            }
            if (!Objects.equals(source.getStaffId(), target.getStaffId())
                    || !Objects.equals(source.getServiceId(), target.getServiceId())
                    || !Objects.equals(source.getServiceTime(), target.getServiceTime())
                    || !Objects.equals(source.getServicePrice(), target.getServicePrice())
                    || !Objects.equals(source.getStartDate(), target.getStartDate())
                    || !Objects.equals(source.getStartTime(), target.getStartTime())
                    || !Objects.equals(source.getEndDate(), target.getEndDate())
                    || !Objects.equals(source.getEndTime(), target.getEndTime())) {
                log.error(
                        "Failed to validate service detail, appointment: {}, source: {}, target: {}",
                        JsonUtil.toJson(dto),
                        JsonUtil.toJson(source),
                        JsonUtil.toJson(target));
                return true;
            }
        }
        List<GroomingAddOnDetail> addOnDetails =
                groomingAddOnDetailService.listByBookingRequestId(bookingRequest.getId());
        for (GroomingAddOnDetail target : addOnDetails) {
            List<GroomingAddOnDetail> sources =
                    petAddOnsMap.get(target.getPetId().intValue());
            if (CollectionUtils.isEmpty(sources)) {
                log.error("Failed to get source list add-on detail appointment: {}", JsonUtil.toJson(dto));
                return true;
            }
            Map<Long, GroomingAddOnDetail> sourceMap = sources.stream()
                    .collect(Collectors.toMap(GroomingAddOnDetail::getAddOnId, Function.identity(), (a, b) -> a));
            GroomingAddOnDetail source = sourceMap.get(target.getAddOnId());
            if (source == null) {
                log.error("Failed to get source add-on detail appointment: {}", JsonUtil.toJson(dto));
                return true;
            }
            if (!Objects.equals(source.getStaffId(), target.getStaffId())
                    || !Objects.equals(source.getServiceTime(), target.getServiceTime())
                    || !Objects.equals(source.getServicePrice(), target.getServicePrice())
                    || !Objects.equals(source.getStartDate(), target.getStartDate())
                    || !Objects.equals(source.getStartTime(), target.getStartTime())
                    || !Objects.equals(source.getEndDate(), target.getEndDate())
                    || !Objects.equals(source.getEndTime(), target.getEndTime())) {
                log.error(
                        "Failed to validate add-on detail, appointment: {}, source: {}, target: {}",
                        JsonUtil.toJson(dto),
                        JsonUtil.toJson(source),
                        JsonUtil.toJson(target));
                return true;
            }
        }
        return false;
    }

    private boolean validateGroomingAutoAssign(GroomingOnlyBookingRequestDTO dto, BookingRequest bookingRequest) {
        GroomingAutoAssign source = buildGroomingAutoAssign(dto.getAutoAssign());
        GroomingAutoAssign target = groomingAutoAssignService.getByBookingRequestId(bookingRequest.getId());
        if (source == null && target == null) {
            return false;
        }
        if (source == null || target == null) {
            log.error("Failed to get auto assign, appointment: {}", JsonUtil.toJson(dto));
            return true;
        }
        boolean result = !Objects.equals(source.getStaffId(), target.getStaffId())
                || !Objects.equals(source.getStartTime(), target.getStartTime());
        if (result) {
            log.error(
                    "Failed to validate auto assign, appointment: {}, source: {}, target: {}",
                    JsonUtil.toJson(dto),
                    JsonUtil.toJson(source),
                    JsonUtil.toJson(target));
        }
        return result;
    }

    private Map<Integer, GroomingServiceDetail> filterGroomingServiceDetailMap(
            List<GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO> groomingServiceDetails,
            Map<Long, ServiceBriefView> serviceMap) {
        return groomingServiceDetails.stream()
                .filter(detail -> serviceMap.containsKey(detail.getServiceId().longValue()))
                .filter(detail -> Objects.equals(
                        serviceMap.get(detail.getServiceId().longValue()).getType(), ServiceType.SERVICE))
                .collect(Collectors.toMap(
                        GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO::getPetId,
                        this::buildGroomingServiceDetail,
                        (a, b) -> a));
    }

    private Map<Integer, List<GroomingAddOnDetail>> filterGroomingAddOnDetailMap(
            List<GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO> groomingServiceDetails,
            Map<Long, ServiceBriefView> serviceMap) {
        return groomingServiceDetails.stream()
                .filter(detail -> serviceMap.containsKey(detail.getServiceId().longValue()))
                .filter(detail -> Objects.equals(
                        serviceMap.get(detail.getServiceId().longValue()).getType(), ServiceType.ADDON))
                .collect(Collectors.groupingBy(
                        GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO::getPetId,
                        Collectors.mapping(this::buildGroomingAddOnDetail, Collectors.toList())));
    }

    private void createGroomingAutoAssign(Integer appointmentId) {
        GroomingOnlyBookingRequestDTO dto = onlineBookingClient.getBookingRequest(appointmentId);
        if (dto == null) {
            log.error("Failed to get booing request by appointmentId: {}", appointmentId);
            return;
        }
        BookingRequest bookingRequest = bookingRequestService.getByAppointmentId(dto.getBusinessId(), appointmentId);
        if (bookingRequest == null) {
            return;
        }
        Long bookingRequestId = bookingRequest.getId();
        GroomingAutoAssign exists = groomingAutoAssignService.getByBookingRequestId(bookingRequestId);
        if (exists != null) {
            return;
        }
        // 更新 appointment 的 staff / time 信息
        GroomingOnlyBookingRequestDTO.GroomingAutoAssignDTO autoAssign = dto.getAutoAssign();

        Optional.ofNullable(autoAssign.getStartTime())
                .ifPresent(time -> updateTimeForBookingRequest(bookingRequestId, time));

        Optional.ofNullable(autoAssign.getStaffId())
                .ifPresent(staffId -> updateStaffForBookingRequest(bookingRequestId, staffId.longValue()));
        // 插入 auto assign 记录
        groomingAutoAssignService.insert(buildGroomingAutoAssign(autoAssign));
    }

    private void updateTimeForBookingRequest(Long bookingRequestId, Integer time) {
        List<GroomingServiceDetail> details = groomingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (ObjectUtils.isEmpty(details)) {
            return;
        }

        // 将第一个 pet detail start time 设置为 auto assign 的时间
        // 根据 service duration 依次往后推
        Integer startTime = time;
        for (GroomingServiceDetail detail : details) {
            GroomingServiceDetail update = new GroomingServiceDetail();
            update.setId(detail.getId());
            update.setStartTime(startTime);
            update.setEndTime(startTime + detail.getServiceTime());
            groomingServiceDetailService.update(update);
            startTime = update.getEndTime();
        }

        List<GroomingAddOnDetail> addOnDetails = groomingAddOnDetailService.listByBookingRequestId(bookingRequestId);
        for (GroomingAddOnDetail addOnDetail : addOnDetails) {
            GroomingAddOnDetail update = new GroomingAddOnDetail();
            update.setId(addOnDetail.getId());
            update.setStartTime(startTime);
            update.setEndTime(startTime + addOnDetail.getServiceTime());
            groomingAddOnDetailService.update(update);
            startTime = update.getEndTime();
        }

        Integer end = startTime;

        BookingRequest updateBean = new BookingRequest();
        updateBean.setId(bookingRequestId);
        updateBean.setStartTime(time);
        updateBean.setEndTime(end);
        bookingRequestService.update(updateBean);
    }

    private void updateStaffForBookingRequest(Long bookingRequestId, Long staffId) {
        List<GroomingServiceDetail> serviceDetails =
                groomingServiceDetailService.listByBookingRequestId(bookingRequestId);
        if (ObjectUtils.isEmpty(serviceDetails)) {
            return;
        }
        for (GroomingServiceDetail serviceDetail : serviceDetails) {
            GroomingServiceDetail update = new GroomingServiceDetail();
            update.setId(serviceDetail.getId());
            update.setStaffId(staffId);
            groomingServiceDetailService.update(update);
        }
        List<GroomingAddOnDetail> addOnDetails = groomingAddOnDetailService.listByBookingRequestId(bookingRequestId);
        for (GroomingAddOnDetail addOnDetail : addOnDetails) {
            GroomingAddOnDetail update = new GroomingAddOnDetail();
            update.setId(addOnDetail.getId());
            update.setStaffId(staffId);
            groomingAddOnDetailService.update(update);
        }
    }

    private BookingRequest buildGroomingOnlyBookingRequest(Integer businessId, Integer appointmentId) {
        BookingRequest bookingRequest = new BookingRequest();
        bookingRequest.setBusinessId(businessId.longValue());
        bookingRequest.setAppointmentId(appointmentId.longValue());
        bookingRequest.setUpdatedAt(new Date());
        return bookingRequest;
    }

    private void scheduleGroomingOnlyBookingRequest(Integer businessId, Integer appointmentId) {
        BookingRequest bookingRequest = buildGroomingOnlyBookingRequest(businessId, appointmentId);
        bookingRequest.setStatus(BookingRequestStatus.SCHEDULED);
        bookingRequestService.updateByAppointmentId(bookingRequest);
    }

    private void moveGroomingOnlyBookingRequestToWaitList(Integer businessId, Integer appointmentId) {
        BookingRequest bookingRequest = buildGroomingOnlyBookingRequest(businessId, appointmentId);
        bookingRequest.setStatus(BookingRequestStatus.WAIT_LIST);
        bookingRequestService.updateByAppointmentId(bookingRequest);
    }

    private void declineGroomingOnlyBookingRequest(Integer businessId, Integer appointmentId) {
        BookingRequest bookingRequest = buildGroomingOnlyBookingRequest(businessId, appointmentId);
        bookingRequest.setStatus(BookingRequestStatus.DECLINED);
        bookingRequestService.updateByAppointmentId(bookingRequest);
    }

    private void deleteGroomingOnlyBookingRequest(Integer businessId, Integer appointmentId) {
        BookingRequest bookingRequest = buildGroomingOnlyBookingRequest(businessId, appointmentId);
        bookingRequest.setStatus(BookingRequestStatus.DELETED);
        bookingRequest.setDeletedAt(new Date());
        bookingRequestService.updateByAppointmentId(bookingRequest);
    }

    private void updateBookingRequestStatus(Integer businessId, Integer appointmentId, BookingRequestStatus status) {
        BookingRequest bookingRequest = buildGroomingOnlyBookingRequest(businessId, appointmentId);
        bookingRequest.setStatus(status);
        bookingRequestService.updateByAppointmentId(bookingRequest);
    }

    private void createGroomingOnlyBookingRequest(Integer appointmentId) {
        GroomingOnlyBookingRequestDTO bookingRequest = onlineBookingClient.getBookingRequest(appointmentId);
        if (bookingRequest == null) {
            log.error("Failed to get booking request by appointmentId: {}", appointmentId);
            return;
        }
        // not grooming only booking request
        if (!Objects.equals(bookingRequest.getServiceTypeInclude(), ServiceItemType.GROOMING_VALUE)) {
            return;
        }
        if (CollectionUtils.isEmpty(bookingRequest.getGroomingServiceDetails())) {
            return;
        }
        List<ServiceBriefView> services = getServices(bookingRequest);
        if (CollectionUtils.isEmpty(services)) {
            log.error("Failed to get services by appointmentId: {}", appointmentId);
            return;
        }
        Map<Long, ServiceBriefView> serviceMap =
                services.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
        Integer serviceTypeInclude = ServiceItemEnum.convertBitValueList(
                services.stream().map(ServiceBriefView::getServiceItemTypeValue).toList());
        BookingRequest exists = bookingRequestService.getByAppointmentId(bookingRequest.getBusinessId(), appointmentId);
        if (exists == null) {
            Map<Integer, GroomingServiceDetail> petServiceMap =
                    filterGroomingServiceDetailMap(bookingRequest.getGroomingServiceDetails(), serviceMap);
            Map<Integer, List<GroomingAddOnDetail>> petAddOnsMap =
                    filterGroomingAddOnDetailMap(bookingRequest.getGroomingServiceDetails(), serviceMap);
            bookingRequestService.createGroomingOnlyBookingRequest(
                    buildBookingRequest(bookingRequest, serviceTypeInclude),
                    petServiceMap,
                    petAddOnsMap,
                    buildGroomingAutoAssign(bookingRequest.getAutoAssign()));
        } else {
            // 已经有数据继承 id 并更新
            BookingRequest update = buildBookingRequest(bookingRequest, serviceTypeInclude);
            update.setId(exists.getId());
            bookingRequestService.updateGroomingOnlyBookingRequest(
                    update,
                    filterGroomingServiceDetailMap(bookingRequest.getGroomingServiceDetails(), serviceMap),
                    filterGroomingAddOnDetailMap(bookingRequest.getGroomingServiceDetails(), serviceMap),
                    buildGroomingAutoAssign(bookingRequest.getAutoAssign()));
        }
    }

    private List<ServiceBriefView> getServices(GroomingOnlyBookingRequestDTO dto) {
        List<GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO> details = dto.getGroomingServiceDetails();
        if (CollectionUtils.isEmpty(details)) {
            return List.of();
        }
        List<Long> serviceIds = details.stream()
                .map(GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO::getServiceId)
                .map(Integer::longValue)
                .distinct()
                .toList();
        GetServiceListByIdsResponse response =
                serviceBlockingStub.getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(serviceIds)
                        .build());
        return response.getServicesList();
    }

    private static BookingRequest buildBookingRequest(GroomingOnlyBookingRequestDTO dto, Integer serviceTypeInclude) {
        BookingRequest bookingRequest = new BookingRequest();
        bookingRequest.setCompanyId(dto.getCompanyId());
        bookingRequest.setBusinessId(dto.getBusinessId().longValue());
        bookingRequest.setCustomerId(dto.getCustomerId().longValue());
        bookingRequest.setAppointmentId(dto.getAppointmentId().longValue());
        bookingRequest.setStartDate(StringUtils.hasText(dto.getStartDate()) ? dto.getStartDate() : null);
        bookingRequest.setEndDate(StringUtils.hasText(dto.getEndDate()) ? dto.getEndDate() : null);
        bookingRequest.setStartTime(dto.getStartTime());
        bookingRequest.setEndTime(dto.getEndTime());
        bookingRequest.setStatus(BookingRequestStatus.forNumber(dto.getStatus()));
        bookingRequest.setIsPrepaid(dto.getIsPrepaid());
        bookingRequest.setAdditionalNote(dto.getAdditionalNote());
        bookingRequest.setSourcePlatform(dto.getSourcePlatform());
        bookingRequest.setServiceTypeInclude(serviceTypeInclude);
        bookingRequest.setCreatedAt(new Date(dto.getCreatedAt() * 1000L));
        bookingRequest.setUpdatedAt(new Date(dto.getUpdatedAt() * 1000L));
        return bookingRequest;
    }

    private GroomingServiceDetail buildGroomingServiceDetail(
            GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO dto) {
        GroomingServiceDetail detail = new GroomingServiceDetail();
        detail.setPetId(dto.getPetId().longValue());
        detail.setStaffId(dto.getStaffId().longValue());
        detail.setServiceId(dto.getServiceId().longValue());
        detail.setServiceTime(dto.getServiceTime());
        detail.setServicePrice(dto.getServicePrice());
        detail.setStartDate(StringUtils.hasText(dto.getStartDate()) ? dto.getStartDate() : null);
        detail.setEndDate(StringUtils.hasText(dto.getEndDate()) ? dto.getEndDate() : null);
        detail.setStartTime(dto.getStartTime());
        detail.setEndTime(dto.getEndTime());
        detail.setCreatedAt(new Date(dto.getCreatedAt() * 1000L));
        detail.setUpdatedAt(new Date(dto.getUpdatedAt() * 1000L));
        return detail;
    }

    private GroomingAddOnDetail buildGroomingAddOnDetail(GroomingOnlyBookingRequestDTO.GroomingServiceDetailDTO dto) {
        GroomingAddOnDetail detail = new GroomingAddOnDetail();
        detail.setPetId(dto.getPetId().longValue());
        detail.setStaffId(dto.getStaffId().longValue());
        detail.setAddOnId(dto.getServiceId().longValue());
        detail.setServiceTime(dto.getServiceTime());
        detail.setServicePrice(dto.getServicePrice());
        detail.setStartDate(StringUtils.hasText(dto.getStartDate()) ? dto.getStartDate() : null);
        detail.setEndDate(StringUtils.hasText(dto.getEndDate()) ? dto.getEndDate() : null);
        detail.setStartTime(dto.getStartTime());
        detail.setEndTime(dto.getEndTime());
        detail.setCreatedAt(new Date(dto.getCreatedAt() * 1000L));
        detail.setUpdatedAt(new Date(dto.getUpdatedAt() * 1000L));
        return detail;
    }

    private GroomingAutoAssign buildGroomingAutoAssign(GroomingOnlyBookingRequestDTO.GroomingAutoAssignDTO dto) {
        if (dto == null || (dto.getStaffId() == null && dto.getStartTime() == null)) {
            return null;
        }
        GroomingAutoAssign autoAssign = new GroomingAutoAssign();
        if (dto.getStaffId() != null) {
            autoAssign.setStaffId(dto.getStaffId().longValue());
        }
        autoAssign.setStartTime(dto.getStartTime());
        autoAssign.setCreatedAt(dto.getCreatedAt());
        return autoAssign;
    }
}

package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.idl.models.organization.v1.BookingLimitationDef;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.SlotDailySettingDef;
import com.moego.idl.models.organization.v1.SlotHourSettingDef;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.models.organization.v1.TimeDailySettingDef;
import com.moego.idl.models.organization.v1.TimeHourSettingDef;
import com.moego.svc.online.booking.entity.DayHourLimit;
import com.moego.svc.online.booking.entity.StaffAvailabilityDayHour;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import com.moego.svc.online.booking.mapper.StaffAvailabilityDayHourMapper;
import com.moego.svc.online.booking.mapper.StaffAvailabilitySlotDayMapper;
import com.moego.svc.online.booking.mapper.StaffAvailabilityTimeDayMapper;
import com.moego.svc.online.booking.utils.OBAvailabilityDayHourUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;

@ExtendWith(MockitoExtension.class)
class StaffAvailabilityDayHourServiceTest {
    @Mock
    private StaffAvailabilitySlotDayMapper staffAvailabilitySlotDayMapper;

    @Mock
    private StaffAvailabilityTimeDayMapper staffAvailabilityTimeDayMapper;

    @Mock
    private StaffAvailabilityDayHourMapper staffAvailabilityDayHourMapper;

    @Mock
    private BookingLimitationService bookingLimitationService;

    @InjectMocks
    private StaffAvailabilityDayHourService service;

    @Captor
    private ArgumentCaptor<List<DayHourLimit>> limitCaptor;

    @Captor
    private ArgumentCaptor<List<Long>> deleteLimitIdsCaptor;

    @Captor
    private ArgumentCaptor<List<StaffAvailabilityDayHour>> dayHourCaptor;

    @Captor
    private ArgumentCaptor<StaffAvailabilityDayHour> singleDayHourCaptor;

    @Captor
    private ArgumentCaptor<StaffAvailabilitySlotDay> slotDayCaptor;

    @Test
    void updateStaffAvailabilitySlotDays_withMultipleDaysAndLimits() {
        // Arrange
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;

        // 创建三天的数据：周一(已存在)、周二(新建)、周三(新建)
        StaffAvailabilityDef staffAvailabilityDef = StaffAvailabilityDef.newBuilder()
                .setStaffId(staffId)
                .setIsAvailable(true)
                .addSlotAvailabilityDayList(createSlotAvailabilityDay(DayOfWeek.MONDAY, 600, 960, 10))
                .addSlotAvailabilityDayList(createSlotAvailabilityDay(DayOfWeek.TUESDAY, 1200, 1400, 5))
                .addSlotAvailabilityDayList(createSlotAvailabilityDay(DayOfWeek.WEDNESDAY, 840, 1020, 8))
                .build();

        // 只有周一的数据存在，并且包含已有的 limitIds
        Map<String, StaffAvailabilitySlotDay> existingMap = new HashMap<>();
        StaffAvailabilitySlotDay existingSlotDay = createExistingSlotDay(companyId, businessId, staffId, 1);
        existingSlotDay.setLimitIds(List.of(100L, 101L)); // 设置已存在的 limitIds
        existingMap.put(OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(businessId, staffId, 1), existingSlotDay);

        // Mock 所有需要的行为
        // doNothing().when(bookingLimitationService).batchCreate(limitCaptor.capture());
        doNothing().when(bookingLimitationService).deleteByIds(deleteLimitIdsCaptor.capture());
        when(staffAvailabilityDayHourMapper.insertSelective(singleDayHourCaptor.capture()))
                .thenReturn(1);
        when(staffAvailabilityDayHourMapper.select(any())).thenReturn(List.of());
        when(staffAvailabilityDayHourMapper.delete(any(DeleteDSLCompleter.class)))
                .thenReturn(1);
        when(staffAvailabilitySlotDayMapper.insertSelective(slotDayCaptor.capture()))
                .thenReturn(1);
        when(bookingLimitationService.batchCreateByLimitationGroup(any())).thenReturn(List.of(102L, 103L));

        // Act
        service.updateStaffAvailabilitySlotDays(companyId, businessId, staffAvailabilityDef, existingMap);

        // Assert
        // 验证创建的 DayHourLimit
        // List<List<DayHourLimit>> allCreatedLimits = limitCaptor.getAllValues();
        // List<DayHourLimit> createdLimits =
        //         allCreatedLimits.stream().flatMap(List::stream).collect(Collectors.toList());
        //
        // assertThat(createdLimits).hasSize(9); // 每天三个 limit (breed、size、service)

        // 验证删除的旧 limitIds
        List<Long> deletedLimitIds = deleteLimitIdsCaptor.getValue();
        assertThat(deletedLimitIds).containsExactly(100L, 101L);

        // 验证创建的 DayHour
        // List<List<StaffAvailabilityDayHour>> allCreatedDayHours = dayHourCaptor.getAllValues();
        // List<StaffAvailabilityDayHour> createdDayHours =
        //         allCreatedDayHours.stream().flatMap(List::stream).collect(Collectors.toList());
        List<StaffAvailabilityDayHour> createdDayHours = singleDayHourCaptor.getAllValues();

        assertThat(createdDayHours).hasSize(3); // 每天一个时间段
        assertThat(createdDayHours).extracting("startTime").containsExactly(600, 1200, 840);

        // 验证创建的 SlotDay
        List<StaffAvailabilitySlotDay> createdSlotDays = slotDayCaptor.getAllValues();
        assertThat(createdSlotDays).hasSize(2); // 只有周二和周三需要新建
        assertThat(createdSlotDays).extracting("dayOfWeek").containsExactly(2, 3);
    }

    @Test
    void updateStaffAvailabilityTimeDay_withMultipleDaysAndLimits() {
        // Arrange
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;

        // 创建三天的数据：周一(已存在)、周二(新建)、周三(新建)
        StaffAvailabilityDef staffAvailabilityDef = StaffAvailabilityDef.newBuilder()
                .setStaffId(staffId)
                .setIsAvailable(true)
                .addTimeAvailabilityDayList(createTimeAvailabilityDay(DayOfWeek.MONDAY, 600, 960))
                .addTimeAvailabilityDayList(createTimeAvailabilityDay(DayOfWeek.TUESDAY, 1200, 1400))
                .addTimeAvailabilityDayList(createTimeAvailabilityDay(DayOfWeek.WEDNESDAY, 840, 1020))
                .build();

        // 只有周一的数据存在，并且包含已有的 limitIds
        Map<String, StaffAvailabilityTimeDay> existingMap = new HashMap<>();
        StaffAvailabilityTimeDay existingTimeDay = createExistingTimeDay(companyId, businessId, staffId, 1);
        existingTimeDay.setLimitIds(List.of(100L, 101L)); // 设置已存在的 limitIds
        existingMap.put(OBAvailabilityDayHourUtils.getStaffAvailabilityDayKey(businessId, staffId, 1), existingTimeDay);

        // Mock 所有需要的行为
        // doNothing().when(bookingLimitationService).batchCreate(limitCaptor.capture());
        doNothing().when(bookingLimitationService).deleteByIds(deleteLimitIdsCaptor.capture());
        when(staffAvailabilityDayHourMapper.insertSelective(singleDayHourCaptor.capture()))
                .thenReturn(1);
        when(staffAvailabilityDayHourMapper.select(any())).thenReturn(List.of());
        when(staffAvailabilityDayHourMapper.delete(any(DeleteDSLCompleter.class)))
                .thenReturn(1);
        when(staffAvailabilityTimeDayMapper.insertSelective(any(StaffAvailabilityTimeDay.class)))
                .thenReturn(1);
        when(bookingLimitationService.batchCreateByLimitationGroup(any())).thenReturn(List.of(102L, 103L));

        // Act
        service.updateStaffAvailabilityTimeDays(companyId, businessId, staffAvailabilityDef, existingMap);

        // Assert
        // 验证创建的 DayHourLimit
        // List<List<DayHourLimit>> allCreatedLimits = limitCaptor.getAllValues();
        // List<DayHourLimit> createdLimits =
        //         allCreatedLimits.stream().flatMap(List::stream).collect(Collectors.toList());
        //
        // assertThat(createdLimits).hasSize(9); // 每天三个 limit (breed、size、service)

        // 验证删除的旧 limitIds
        List<Long> deletedLimitIds = deleteLimitIdsCaptor.getValue();
        assertThat(deletedLimitIds).containsExactly(100L, 101L);

        // 验证创建的 DayHour - 注意这里的 DayHour 没有 limit
        // List<List<StaffAvailabilityDayHour>> allCreatedDayHours = dayHourCaptor.getAllValues();
        // List<StaffAvailabilityDayHour> createdDayHours =
        //         allCreatedDayHours.stream().flatMap(List::stream).collect(Collectors.toList());
        List<StaffAvailabilityDayHour> createdDayHours = singleDayHourCaptor.getAllValues();

        assertThat(createdDayHours).hasSize(3); // 每天一个时间段
        assertThat(createdDayHours).extracting("startTime").containsExactly(600, 1200, 840);
        // 验证 DayHour 没有 limit
        assertThat(createdDayHours).extracting("limitIds").containsOnly(List.of());
    }

    private SlotAvailabilityDayDef createSlotAvailabilityDay(
            DayOfWeek dayOfWeek, int startTime, int endTime, int capacity) {
        return SlotAvailabilityDayDef.newBuilder()
                .setDayOfWeek(dayOfWeek)
                .setIsAvailable(true)
                .setSlotDailySetting(SlotDailySettingDef.newBuilder()
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .setCapacity(capacity)
                        .setLimit(createLimit())
                        .build())
                .addSlotHourSettingList(SlotHourSettingDef.newBuilder()
                        .setStartTime(startTime)
                        .setCapacity(capacity)
                        .build())
                .build();
    }

    private TimeAvailabilityDayDef createTimeAvailabilityDay(DayOfWeek dayOfWeek, int startTime, int endTime) {
        return TimeAvailabilityDayDef.newBuilder()
                .setDayOfWeek(dayOfWeek)
                .setIsAvailable(true)
                .setTimeDailySetting(
                        TimeDailySettingDef.newBuilder().setLimit(createLimit()).build())
                .addTimeHourSettingList(TimeHourSettingDef.newBuilder()
                        .setStartTime(startTime)
                        .setEndTime(endTime)
                        .build())
                .build();
    }

    private BookingLimitationDef createLimit() {
        return BookingLimitationDef.newBuilder()
                .addAllPetBreedLimits(List.of(BookingLimitationDef.PetBreedLimitation.newBuilder()
                        .addAllBreedIds(List.of(1L, 2L))
                        .setCapacity(5)
                        .build()))
                .addAllPetSizeLimits(List.of(
                        BookingLimitationDef.PetSizeLimitation.newBuilder().build()))
                .addAllServiceLimits(List.of(
                        BookingLimitationDef.ServiceLimitation.newBuilder().build()))
                .build();
    }

    private StaffAvailabilitySlotDay createExistingSlotDay(
            Long companyId, Long businessId, Long staffId, int dayOfWeek) {
        StaffAvailabilitySlotDay slotDay = new StaffAvailabilitySlotDay();
        slotDay.setId(1L);
        slotDay.setCompanyId(companyId);
        slotDay.setBusinessId(businessId);
        slotDay.setStaffId(staffId);
        slotDay.setDayOfWeek(dayOfWeek);
        slotDay.setIsAvailable(true);
        slotDay.setStartTime(600);
        slotDay.setEndTime(960);
        slotDay.setCapacity(10);
        return slotDay;
    }

    private StaffAvailabilityTimeDay createExistingTimeDay(
            Long companyId, Long businessId, Long staffId, int dayOfWeek) {
        StaffAvailabilityTimeDay timeDay = new StaffAvailabilityTimeDay();
        timeDay.setId(1L);
        timeDay.setCompanyId(companyId);
        timeDay.setBusinessId(businessId);
        timeDay.setStaffId(staffId);
        timeDay.setDayOfWeek(dayOfWeek);
        timeDay.setIsAvailable(true);
        return timeDay;
    }
}

package com.moego.svc.online.booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.ScheduleType;
import com.moego.idl.models.online_booking.v1.TimeRangeDef;
import com.moego.svc.online.booking.dto.UsedLocalTimeDTO;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class TimeRangeUtilsTest {

    @Test
    void getTimeRangeByDate_ValidDate_EveryWeekSchedule() {
        // Build a TimeRangeDef for testing: Sunday 9:00-16:00 in the first week
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllSunday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(960)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_WEEK;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // A Sunday
        LocalDate curDate = LocalDate.of(2023, 1, 8); // The next Sunday, falls into the first week slot

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);
        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(960).build());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getTimeRangeByDate_ValidDate_BiWeeklySchedule_FirstWeek() {
        // Build a TimeRangeDef for testing bi-weekly schedule
        // First week: Tuesday 10:00-12:00
        // Second week: Tuesday 13:00-15:00
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllTuesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(600)
                                .setEndTime(720)
                                .build()))
                        .build())
                .setSecondWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllTuesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(780)
                                .setEndTime(900)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_TWO_WEEKS; // 修正：使用正确的双周类型
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // A Sunday
        LocalDate curDate = LocalDate.of(2023, 1, 3); // A Tuesday in the first week (relative to scheduleStartDate)

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);
        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(600).setEndTime(720).build());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getTimeRangeByDate_ValidDate_BiWeeklySchedule_SecondWeek() {
        // 定义一个双周循环的 TimeRangeDef
        // 第一周周二：6:00 - 7:20 (360-440 分钟)
        // 第二周周二：13:00 - 15:00 (780-900 分钟)
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllTuesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(360)
                                .setEndTime(440)
                                .build()))
                        .build())
                .setSecondWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllTuesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(780)
                                .setEndTime(900)
                                .build()))
                        .build())
                .build();

        ScheduleType scheduleType = ScheduleType.EVERY_TWO_WEEKS; // 明确指定为双周循环
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // 2023年1月1日 是星期日

        // 计算一个在"第二周"的周二
        // 2023年1月1日 (周日) 是循环的第一天
        // 2023年1月8日 (周日) 是第二周的开始
        // 2023年1月10日 (周二) 属于循环的第二周
        LocalDate curDate = LocalDate.of(2023, 1, 10);

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);

        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(780).setEndTime(900).build());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getTimeRangeByDate_DateBeforeScheduleStartDate_ReturnsEmptyList() {
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllTuesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(960)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_WEEK;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1);
        LocalDate curDate = LocalDate.of(2022, 12, 31); // Date before scheduleStartDate

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);

        assertThat(result).isEmpty();
    }

    @Test
    void getTimeRangeByDate_EmptyTimeRangeDef_ReturnsEmptyList() {
        // An empty TimeRangeDef means no schedule is defined
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder().build();
        ScheduleType scheduleType = ScheduleType.EVERY_WEEK;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1);
        LocalDate curDate = LocalDate.of(2023, 1, 8);

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);

        assertThat(result).isEmpty();
    }

    @Test
    void getTimeRangeByDate_NoMatchingDayOfWeek_ReturnsEmptyList() {
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllMonday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(960)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_WEEK;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // A Sunday
        LocalDate curDate = LocalDate.of(2023, 1, 1); // Still Sunday, but schedule only has Monday

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);

        assertThat(result).isEmpty(); // Should be empty as Sunday schedule is not defined
    }

    @Test
    void getAvailableTimeRange_NoAppointmentsOrRequests() {
        LocalDate date = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = Map.of(
                date,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(9)
                        .setEndTime(10)
                        .setPetCapacity(5)
                        .build()));
        List<UsedLocalTimeDTO> existAppointments = List.of(); // No existing appointments
        List<UsedLocalTimeDTO> existBookingRequests = List.of(); // No existing booking requests
        LocalDate start = date;
        LocalDate end = date;

        Map<LocalDate, List<DayTimeRangeDef>> result = TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, existAppointments, existBookingRequests, start, end);

        // Expect the result to be the same as the initial time ranges, as nothing is occupied
        assertThat(result).containsExactlyInAnyOrderEntriesOf(timeRangeForEveryday);
    }

    @Test
    void getAvailableTimeRange_WithAppointmentsAndRequests_FullCapacity() {
        LocalDate date = LocalDate.of(2023, 1, 1);
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(600)
                .setPetCapacity(2)
                .build()); // Capacity of 2
        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = Map.of(date, timeRanges);

        // Two items occupy capacity, matching the total capacity
        List<UsedLocalTimeDTO> existAppointments = List.of(new UsedLocalTimeDTO(date, 540)); // 1 usage at 540 (9:00 AM)
        List<UsedLocalTimeDTO> existBookingRequests =
                List.of(new UsedLocalTimeDTO(date, 540)); // 1 usage at 540 (9:00 AM)

        LocalDate start = date;
        LocalDate end = date;

        Map<LocalDate, List<DayTimeRangeDef>> result = TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, existAppointments, existBookingRequests, start, end);

        assertThat(result.get(date)).isEmpty(); // All capacity should be used, so no available ranges
    }

    @Test
    void getAvailableTimeRange_WithAppointmentsAndRequests_PartialCapacityRemaining() {
        LocalDate date = LocalDate.of(2023, 1, 1);
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(600)
                .setPetCapacity(5)
                .build()); // Capacity of 5
        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = Map.of(date, timeRanges);

        // Two items occupy capacity, leaving 3 remaining
        List<UsedLocalTimeDTO> existAppointments = List.of(new UsedLocalTimeDTO(date, 540));
        List<UsedLocalTimeDTO> existBookingRequests = List.of(new UsedLocalTimeDTO(date, 540));

        LocalDate start = date;
        LocalDate end = date;

        Map<LocalDate, List<DayTimeRangeDef>> result = TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, existAppointments, existBookingRequests, start, end);

        assertThat(result.get(date))
                .hasSize(1)
                .first()
                .matches(def -> def.getPetCapacity() == 3 && def.getStartTime() == 540 && def.getEndTime() == 600);
    }

    @Test
    void getAvailableTimeRangeInDay_NoAppointmentsOrRequests() {
        List<UsedLocalTimeDTO> appointments = List.of();
        List<UsedLocalTimeDTO> bookingRequests = List.of();
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(9)
                .setEndTime(10)
                .setPetCapacity(5)
                .build());

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).containsExactlyInAnyOrderElementsOf(timeRanges);
    }

    @Test
    void getAvailableTimeRangeInDay_WithAppointments_FullCapacityUsed() {
        List<UsedLocalTimeDTO> appointments = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540)); // 1 usage
        List<UsedLocalTimeDTO> bookingRequests = List.of();
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(600)
                .setPetCapacity(1)
                .build()); // Capacity 1

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).isEmpty(); // Capacity fully used
    }

    @Test
    void getAvailableTimeRangeInDay_WithBookingRequests_FullCapacityUsed() {
        List<UsedLocalTimeDTO> appointments = List.of();
        List<UsedLocalTimeDTO> bookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540)); // 1 usage
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(600)
                .setPetCapacity(1)
                .build()); // Capacity 1

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).isEmpty(); // Capacity fully used
    }

    @Test
    void getAvailableTimeRangeInDay_WithPartialOverlap_PetCapacityReduced() {
        // Two usages, 540 and 600. The filter logic uses startTime only.
        // Assuming both appointments and booking requests start at 540, they will both count against the 540-660 range.
        List<UsedLocalTimeDTO> appointments = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540));
        List<UsedLocalTimeDTO> bookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540));
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(660)
                .setPetCapacity(3)
                .build()); // Initial capacity 3

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result)
                .hasSize(1)
                .first()
                .matches(def -> def.getPetCapacity() == 1
                        && def.getStartTime() == 540
                        && def.getEndTime() == 660); // 3 - 1 (appt) - 1 (req) = 1 remaining
    }

    @Test
    void getAvailableTimeRangeInDay_WithEmptyTimeRanges_ReturnsEmpty() {
        List<UsedLocalTimeDTO> appointments = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540));
        List<UsedLocalTimeDTO> bookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.now(), 600));
        List<DayTimeRangeDef> timeRanges = List.of(); // No time ranges defined

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).isEmpty();
    }

    @Test
    void getAvailableTimeRangeInDay_TimeRangeWithNoPetCapacityRestriction() {
        List<UsedLocalTimeDTO> appointments = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540));
        List<UsedLocalTimeDTO> bookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.now(), 540));
        List<DayTimeRangeDef> timeRanges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(660)
                .build()); // No pet capacity set, so it should not be filtered by capacity

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(timeRanges.get(0)); // Should return the original range unmodified
    }

    @Test
    void isStartTimeInRange_StartTimeEqualsRangeStart() {
        DayTimeRangeDef timeRangeDef =
                DayTimeRangeDef.newBuilder().setStartTime(100).setEndTime(200).build();
        int startTime = 100;
        assertThat(TimeRangeUtils.isStartTimeInRange(timeRangeDef, startTime)).isTrue();
    }

    @ParameterizedTest(name = "startTime={1} => expected={2}")
    @CsvSource({
        "100, 200, false", // 等于 end，exclusive
        "100, 150, true", // 在范围内
        "100, 50, false", // 小于 start
        "100, 250, false" // 大于 end
    })
    void isStartTimeInRange_Parameterized(int rangeStart, int startTime, boolean expected) {
        DayTimeRangeDef timeRangeDef = DayTimeRangeDef.newBuilder()
                .setStartTime(rangeStart)
                .setEndTime(200)
                .build();
        assertThat(TimeRangeUtils.isStartTimeInRange(timeRangeDef, startTime)).isEqualTo(expected);
    }

    // 新增测试用例：测试三周循环
    @Test
    void getTimeRangeByDate_ValidDate_EveryThreeWeeksSchedule() {
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllMonday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .build()))
                        .build())
                .setSecondWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllMonday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(600)
                                .setEndTime(660)
                                .build()))
                        .build())
                .setThirdWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllMonday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(660)
                                .setEndTime(720)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_THREE_WEEKS;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // Sunday
        LocalDate curDate = LocalDate.of(2023, 1, 16); // Monday in third week

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);
        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(660).setEndTime(720).build());

        assertThat(result).isEqualTo(expected);
    }

    // 新增测试用例：测试四周循环
    @Test
    void getTimeRangeByDate_ValidDate_EveryFourWeeksSchedule() {
        TimeRangeDef timeRangeDef = TimeRangeDef.newBuilder()
                .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllWednesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(600)
                                .build()))
                        .build())
                .setSecondWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllWednesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(600)
                                .setEndTime(660)
                                .build()))
                        .build())
                .setThirdWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllWednesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(660)
                                .setEndTime(720)
                                .build()))
                        .build())
                .setForthWeek(DayOfWeekTimeRangeDef.newBuilder()
                        .addAllWednesday(List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(720)
                                .setEndTime(780)
                                .build()))
                        .build())
                .build();
        ScheduleType scheduleType = ScheduleType.EVERY_FOUR_WEEKS;
        LocalDate scheduleStartDate = LocalDate.of(2023, 1, 1); // Sunday
        LocalDate curDate = LocalDate.of(2023, 1, 25); // Wednesday in fourth week

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getTimeRangeByDate(timeRangeDef, scheduleType, scheduleStartDate, curDate);
        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(720).setEndTime(780).build());

        assertThat(result).isEqualTo(expected);
    }

    // 新增测试用例：测试跨多天的可用时间范围
    @Test
    void getAvailableTimeRange_MultipleDays() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);

        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = Map.of(
                startDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(600)
                        .setPetCapacity(2)
                        .build()),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(660)
                        .setPetCapacity(1)
                        .build()),
                endDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(660)
                        .setEndTime(720)
                        .setPetCapacity(3)
                        .build()));

        List<UsedLocalTimeDTO> existAppointments =
                List.of(new UsedLocalTimeDTO(startDate, 540), new UsedLocalTimeDTO(endDate, 660));
        List<UsedLocalTimeDTO> existBookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.of(2023, 1, 2), 600));

        Map<LocalDate, List<DayTimeRangeDef>> result = TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, existAppointments, existBookingRequests, startDate, endDate);

        assertThat(result).hasSize(3);
        assertThat(result.get(startDate)).hasSize(1);
        assertThat(result.get(startDate).get(0).getPetCapacity()).isEqualTo(1);
        assertThat(result.get(LocalDate.of(2023, 1, 2))).isEmpty(); // 容量已满
        assertThat(result.get(endDate)).hasSize(1);
        assertThat(result.get(endDate).get(0).getPetCapacity()).isEqualTo(2);
    }

    // 新增测试用例：测试多个时间段的可用性
    @Test
    void getAvailableTimeRangeInDay_MultipleTimeRanges() {
        List<UsedLocalTimeDTO> appointments =
                List.of(new UsedLocalTimeDTO(LocalDate.now(), 540), new UsedLocalTimeDTO(LocalDate.now(), 660));
        List<UsedLocalTimeDTO> bookingRequests = List.of(new UsedLocalTimeDTO(LocalDate.now(), 600));
        List<DayTimeRangeDef> timeRanges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(600)
                        .setPetCapacity(2)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(660)
                        .setPetCapacity(1)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(660)
                        .setEndTime(720)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result =
                TimeRangeUtils.getAvailableTimeRangeInDay(appointments, bookingRequests, timeRanges);

        assertThat(result).hasSize(2); // 只有2个时间段有剩余容量
        assertThat(result.get(0).getPetCapacity()).isEqualTo(1); // 2 - 1 (appt) = 1
        assertThat(result.get(1).getPetCapacity()).isEqualTo(2); // 3 - 1 (appt) = 2
        // 第二个时间段容量为0，被过滤掉了
    }

    // 新增测试用例：测试边界情况 - startTime 为负数
    @Test
    void isStartTimeInRange_NegativeStartTime() {
        DayTimeRangeDef timeRangeDef =
                DayTimeRangeDef.newBuilder().setStartTime(100).setEndTime(200).build();
        int startTime = -50;
        assertThat(TimeRangeUtils.isStartTimeInRange(timeRangeDef, startTime)).isFalse();
    }

    // 新增测试用例：测试边界情况 - 时间范围为0
    @Test
    void isStartTimeInRange_ZeroTimeRange() {
        DayTimeRangeDef timeRangeDef =
                DayTimeRangeDef.newBuilder().setStartTime(100).setEndTime(100).build();
        int startTime = 100;
        assertThat(TimeRangeUtils.isStartTimeInRange(timeRangeDef, startTime)).isTrue();
    }

    // 新增测试用例：测试边界情况 - 开始和结束日期相同
    @Test
    void getAvailableTimeRange_SameStartAndEndDate() {
        LocalDate date = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = Map.of(
                date,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(600)
                        .setPetCapacity(2)
                        .build()));
        List<UsedLocalTimeDTO> existAppointments = List.of(new UsedLocalTimeDTO(date, 540));
        List<UsedLocalTimeDTO> existBookingRequests = List.of();

        Map<LocalDate, List<DayTimeRangeDef>> result = TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, existAppointments, existBookingRequests, date, date);

        assertThat(result).hasSize(1);
        assertThat(result.get(date)).hasSize(1);
        assertThat(result.get(date).get(0).getPetCapacity()).isEqualTo(1);
    }
}

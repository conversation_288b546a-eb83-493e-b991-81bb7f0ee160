package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class EvaluationTestDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    public static final EvaluationTestDetail evaluationTestDetail = new EvaluationTestDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.id")
    public static final SqlColumn<Long> id = evaluationTestDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = evaluationTestDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.pet_id")
    public static final SqlColumn<Long> petId = evaluationTestDetail.petId;

    /**
     * Database Column Remarks:
     *   The id of current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.evaluation_id")
    public static final SqlColumn<Long> evaluationId = evaluationTestDetail.evaluationId;

    /**
     * Database Column Remarks:
     *   The price of current evaluation test
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = evaluationTestDetail.servicePrice;

    /**
     * Database Column Remarks:
     *   The duration of current evaluation test, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.duration")
    public static final SqlColumn<Integer> duration = evaluationTestDetail.duration;

    /**
     * Database Column Remarks:
     *   The start date of the evaluation test, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_date")
    public static final SqlColumn<String> startDate = evaluationTestDetail.startDate;

    /**
     * Database Column Remarks:
     *   The start time of the evaluation test, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.start_time")
    public static final SqlColumn<Integer> startTime = evaluationTestDetail.startTime;

    /**
     * Database Column Remarks:
     *   The end date of the evaluation test, yyyy-MM-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_date")
    public static final SqlColumn<String> endDate = evaluationTestDetail.endDate;

    /**
     * Database Column Remarks:
     *   The end time of the evaluation test, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.end_time")
    public static final SqlColumn<Integer> endTime = evaluationTestDetail.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.created_at")
    public static final SqlColumn<Date> createdAt = evaluationTestDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.updated_at")
    public static final SqlColumn<Date> updatedAt = evaluationTestDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.deleted_at")
    public static final SqlColumn<Date> deletedAt = evaluationTestDetail.deletedAt;

    /**
     * Database Column Remarks:
     *   evaluation 绑定的 service id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: evaluation_test_detail.service_id")
    public static final SqlColumn<Long> serviceId = evaluationTestDetail.serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: evaluation_test_detail")
    public static final class EvaluationTestDetail extends AliasableSqlTable<EvaluationTestDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> evaluationId = column("evaluation_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<Integer> duration = column("duration", JDBCType.INTEGER);

        public final SqlColumn<String> startDate = column("start_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<String> endDate = column("end_date", JDBCType.DATE, "com.moego.svc.online.booking.typehandler.StringToDateTypeHandler");

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public EvaluationTestDetail() {
            super("evaluation_test_detail", EvaluationTestDetail::new);
        }
    }
}
package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.LodgingAvailabilityDef;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import org.junit.jupiter.api.Test;

class LodgingCapacityConverterTest {

    @Test
    void toLodgingAvailabilityDef_returnsDefaultInstance_whenEntityIsNull() {
        LodgingAvailabilityDef result = LodgingCapacityConverter.INSTANCE.toLodgingAvailabilityDef(null);

        assertThat(result).isEqualTo(LodgingAvailabilityDef.getDefaultInstance());
    }

    @Test
    void toLodgingAvailabilityDef_mapsFieldsCorrectly() {
        LodgingCapacitySetting entity = new LodgingCapacitySetting();
        entity.setIsCapacityLimited(true);
        entity.setCapacityLimit(50);
        entity.setAllowWaitlistSignups(true);

        LodgingAvailabilityDef result = LodgingCapacityConverter.INSTANCE.toLodgingAvailabilityDef(entity);

        LodgingAvailabilityDef expected = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(50)
                .setAllowWaitlistSignups(true)
                .build();

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void toLodgingCapacitySetting_mapsFieldsCorrectly() {
        long companyId = 1L;
        long businessId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.BOARDING;
        LodgingAvailabilityDef def = LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(true)
                .setCapacityLimit(50)
                .build();

        LodgingCapacitySetting result =
                LodgingCapacityConverter.INSTANCE.toLodgingCapacitySetting(companyId, businessId, serviceItemType, def);

        LodgingCapacitySetting expected = new LodgingCapacitySetting();
        expected.setCompanyId(companyId);
        expected.setBusinessId(businessId);
        expected.setServiceItemType(serviceItemType.getNumber());
        expected.setIsCapacityLimited(true);
        expected.setCapacityLimit(50);

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }
}

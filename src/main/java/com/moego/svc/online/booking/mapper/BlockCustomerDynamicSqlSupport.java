package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BlockCustomerDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    public static final BlockCustomer blockCustomer = new BlockCustomer();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.id")
    public static final SqlColumn<Long> id = blockCustomer.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.company_id")
    public static final SqlColumn<Long> companyId = blockCustomer.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = blockCustomer.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.customer_id")
    public static final SqlColumn<Long> customerId = blockCustomer.customerId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.is_active")
    public static final SqlColumn<Boolean> isActive = blockCustomer.isActive;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.updated_by")
    public static final SqlColumn<Long> updatedBy = blockCustomer.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.created_at")
    public static final SqlColumn<Date> createdAt = blockCustomer.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: block_customer.updated_at")
    public static final SqlColumn<Date> updatedAt = blockCustomer.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    public static final class BlockCustomer extends AliasableSqlTable<BlockCustomer> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Long> customerId = column("customer_id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> isActive = column("is_active", JDBCType.BIT);

        public final SqlColumn<Long> updatedBy = column("updated_by", JDBCType.BIGINT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public BlockCustomer() {
            super("block_customer", BlockCustomer::new);
        }
    }
}
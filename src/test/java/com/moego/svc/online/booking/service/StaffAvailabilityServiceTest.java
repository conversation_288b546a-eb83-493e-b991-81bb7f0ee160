package com.moego.svc.online.booking.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.svc.online.booking.entity.StaffAvailability;
import com.moego.svc.online.booking.mapper.StaffAvailabilityMapper;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;

@ExtendWith(MockitoExtension.class)
class StaffAvailabilityServiceTest {
    @Mock
    private StaffAvailabilityMapper staffAvailabilityMapper;

    @InjectMocks
    private StaffAvailabilityService service;

    @Test
    void batchUpdate_withValidInput_shouldSucceed() {

        List<StaffAvailability> staffAvailabilities =
                List.of(createStaffAvailability(1L, 1L, 1L, true), createStaffAvailability(1L, 1L, 2L, false));

        when(staffAvailabilityMapper.update(any(UpdateDSLCompleter.class))).thenReturn(0);

        service.batchUpdate(staffAvailabilities);
    }

    @Test
    void batchUpdate_withEmptyList_shouldSucceed() {

        List<StaffAvailability> staffAvailabilities = new ArrayList<>();

        service.batchUpdate(staffAvailabilities);
    }

    private StaffAvailability createStaffAvailability(
            Long companyId, Long businessId, Long staffId, boolean isAvailable) {
        StaffAvailability staffAvailability = new StaffAvailability();
        staffAvailability.setCompanyId(companyId);
        staffAvailability.setBusinessId(businessId);
        staffAvailability.setStaffId(staffId);
        staffAvailability.setIsAvailable(isAvailable);
        staffAvailability.setCreatedAt(new Date());
        staffAvailability.setUpdatedAt(new Date());
        return staffAvailability;
    }
}

package com.moego.svc.online.booking.mapper;

import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class MedicationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    public static final Medication medication = new Medication();

    /**
     * Database Column Remarks:
     *   The primary key identifier for each medication event.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.id")
    public static final SqlColumn<Long> id = medication.id;

    /**
     * Database Column Remarks:
     *   The booking request identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = medication.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The service detail identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = medication.serviceDetailId;

    /**
     * Database Column Remarks:
     *   service detail type, 1: boarding, 2: daycare
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.service_detail_type")
    public static final SqlColumn<Integer> serviceDetailType = medication.serviceDetailType;

    /**
     * Database Column Remarks:
     *   Medication time.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.time")
    public static final SqlColumn<String> time = medication.time;

    /**
     * Database Column Remarks:
     *   Medication amount, must be greater than 0.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount")
    public static final SqlColumn<BigDecimal> amount = medication.amount;

    /**
     * Database Column Remarks:
     *   Medication unit.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.unit")
    public static final SqlColumn<String> unit = medication.unit;

    /**
     * Database Column Remarks:
     *   Medication name.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.medication_name")
    public static final SqlColumn<String> medicationName = medication.medicationName;

    /**
     * Database Column Remarks:
     *   Additional notes about the medication.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.notes")
    public static final SqlColumn<String> notes = medication.notes;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.created_at")
    public static final SqlColumn<Date> createdAt = medication.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.updated_at")
    public static final SqlColumn<Date> updatedAt = medication.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.deleted_at")
    public static final SqlColumn<Date> deletedAt = medication.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.amount_str")
    public static final SqlColumn<String> amountStr = medication.amountStr;

    /**
     * Database Column Remarks:
     *   1-EVERYDAY_EXCEPT_CHECKOUT_DATE; 2-EVERYDAY_INCLUDE_CHECKOUT_DATE; 3-SPECIFIC_DATE
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.date_type")
    public static final SqlColumn<FeedingMedicationScheduleDateType> dateType = medication.dateType;

    /**
     * Database Column Remarks:
     *   specific_date list, yyyy-mm-dd
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: medication.specific_dates")
    public static final SqlColumn<List<String>> specificDates = medication.specificDates;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: medication")
    public static final class Medication extends AliasableSqlTable<Medication> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceDetailType = column("service_detail_type", JDBCType.INTEGER);

        public final SqlColumn<String> time = column("\"time\"", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<BigDecimal> amount = column("amount", JDBCType.NUMERIC);

        public final SqlColumn<String> unit = column("unit", JDBCType.VARCHAR);

        public final SqlColumn<String> medicationName = column("medication_name", JDBCType.VARCHAR);

        public final SqlColumn<String> notes = column("notes", JDBCType.VARCHAR);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public final SqlColumn<String> amountStr = column("amount_str", JDBCType.VARCHAR);

        public final SqlColumn<FeedingMedicationScheduleDateType> dateType = column("date_type", JDBCType.SMALLINT, "com.moego.svc.online.booking.typehandler.MedicationDateTypeTypeHandler");

        public final SqlColumn<List<String>> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.MedicationSpecificDatesTypeHandler");

        public Medication() {
            super("medication", Medication::new);
        }
    }
}
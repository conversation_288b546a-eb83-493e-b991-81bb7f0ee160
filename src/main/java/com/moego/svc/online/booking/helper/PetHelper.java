package com.moego.svc.online.booking.helper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/10
 */
@Component
@RequiredArgsConstructor
public class PetHelper {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub petStub;

    /**
     * Must get pet info by id, throw exception if not found.
     *
     * @param petId pet id
     * @return pet info
     */
    public BusinessCustomerPetInfoModel mustGetPet(long petId) {

        var resp =
                petStub.getPetInfo(GetPetInfoRequest.newBuilder().setId(petId).build());

        if (!resp.hasPet()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Pet not found: " + petId);
        }

        return resp.getPet();
    }

    /**
     * List pet info by ids.
     *
     * @param petIds pet ids
     * @return pet id -> pet info
     */
    public Map<Long, BusinessCustomerPetInfoModel> listPet(Collection<? extends Number> petIds) {
        if (petIds.isEmpty()) {
            return Map.of();
        }

        var ids = petIds.stream()
            .map(Number::longValue)
            .filter(CommonUtil::isNormal)
            .collect(Collectors.toSet());

        return petStub.batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                    .addAllIds(ids)
                    .build())
            .getPetsList()
            .stream()
            .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity(), (o, n) -> o));
    }
}

package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.DayHourLimitGroupDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.DayHourLimitGroup;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface DayHourLimitGroupMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<DayHourLimitGroupMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    BasicColumn[] selectList = BasicColumn.columnList(id, onlyAcceptSelected, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<DayHourLimitGroup> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<DayHourLimitGroup> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="DayHourLimitGroupResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="only_accept_selected", property="onlyAcceptSelected", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<DayHourLimitGroup> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("DayHourLimitGroupResult")
    Optional<DayHourLimitGroup> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int insertMultiple(Collection<DayHourLimitGroup> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, dayHourLimitGroup, c ->
            c.map(onlyAcceptSelected).toProperty("onlyAcceptSelected")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int insertSelective(DayHourLimitGroup row) {
        return MyBatis3Utils.insert(this::insert, row, dayHourLimitGroup, c ->
            c.map(onlyAcceptSelected).toPropertyWhenPresent("onlyAcceptSelected", row::getOnlyAcceptSelected)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default Optional<DayHourLimitGroup> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default List<DayHourLimitGroup> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default List<DayHourLimitGroup> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default Optional<DayHourLimitGroup> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, dayHourLimitGroup, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    static UpdateDSL<UpdateModel> updateAllColumns(DayHourLimitGroup row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(onlyAcceptSelected).equalTo(row::getOnlyAcceptSelected)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(DayHourLimitGroup row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(onlyAcceptSelected).equalToWhenPresent(row::getOnlyAcceptSelected)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    default int updateByPrimaryKeySelective(DayHourLimitGroup row) {
        return update(c ->
            c.set(onlyAcceptSelected).equalToWhenPresent(row::getOnlyAcceptSelected)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
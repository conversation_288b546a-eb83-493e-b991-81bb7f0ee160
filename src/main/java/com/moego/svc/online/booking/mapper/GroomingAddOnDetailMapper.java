package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.GroomingAddOnDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.typehandler.StringToDateTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface GroomingAddOnDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<GroomingAddOnDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, serviceDetailId, petId, staffId, addOnId, serviceTime, servicePrice, startDate, startTime, endDate, endTime, createdAt, updatedAt, deletedAt, taxId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<GroomingAddOnDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<GroomingAddOnDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="GroomingAddOnDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_id", property="serviceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="add_on_id", property="addOnId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_time", property="serviceTime", jdbcType=JdbcType.INTEGER),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="start_date", property="startDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_date", property="endDate", typeHandler=StringToDateTypeHandler.class, jdbcType=JdbcType.DATE),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.BIGINT)
    })
    List<GroomingAddOnDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("GroomingAddOnDetailResult")
    Optional<GroomingAddOnDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int insertMultiple(Collection<GroomingAddOnDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, groomingAddOnDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(serviceDetailId).toProperty("serviceDetailId")
            .map(petId).toProperty("petId")
            .map(staffId).toProperty("staffId")
            .map(addOnId).toProperty("addOnId")
            .map(serviceTime).toProperty("serviceTime")
            .map(servicePrice).toProperty("servicePrice")
            .map(startDate).toProperty("startDate")
            .map(startTime).toProperty("startTime")
            .map(endDate).toProperty("endDate")
            .map(endTime).toProperty("endTime")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(taxId).toProperty("taxId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int insertSelective(GroomingAddOnDetail row) {
        return MyBatis3Utils.insert(this::insert, row, groomingAddOnDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(serviceDetailId).toPropertyWhenPresent("serviceDetailId", row::getServiceDetailId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(addOnId).toPropertyWhenPresent("addOnId", row::getAddOnId)
            .map(serviceTime).toPropertyWhenPresent("serviceTime", row::getServiceTime)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default Optional<GroomingAddOnDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default List<GroomingAddOnDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default List<GroomingAddOnDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default Optional<GroomingAddOnDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, groomingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(GroomingAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(serviceDetailId).equalTo(row::getServiceDetailId)
                .set(petId).equalTo(row::getPetId)
                .set(staffId).equalTo(row::getStaffId)
                .set(addOnId).equalTo(row::getAddOnId)
                .set(serviceTime).equalTo(row::getServiceTime)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(startDate).equalTo(row::getStartDate)
                .set(startTime).equalTo(row::getStartTime)
                .set(endDate).equalTo(row::getEndDate)
                .set(endTime).equalTo(row::getEndTime)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(taxId).equalTo(row::getTaxId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(GroomingAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(addOnId).equalToWhenPresent(row::getAddOnId)
                .set(serviceTime).equalToWhenPresent(row::getServiceTime)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(taxId).equalToWhenPresent(row::getTaxId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_add_on_detail")
    default int updateByPrimaryKeySelective(GroomingAddOnDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(addOnId).equalToWhenPresent(row::getAddOnId)
            .set(serviceTime).equalToWhenPresent(row::getServiceTime)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .where(id, isEqualTo(row::getId))
        );
    }
}
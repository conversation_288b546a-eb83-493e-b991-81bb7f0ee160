package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.svc.online.booking.mapper.GroomingAddOnDetailDynamicSqlSupport.groomingAddOnDetail;
import static com.moego.svc.online.booking.mapper.GroomingAddOnDetailMapper.updateSelectiveColumns;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.GroomingAddOnDetailMapper;
import com.moego.svc.online.booking.mapper.GroomingServiceDetailMapper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class GroomingAddOnDetailService {

    private final GroomingAddOnDetailMapper groomingAddOnDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final ServiceHelper serviceHelper;
    private final GroomingServiceDetailMapper groomingServiceDetailMapper;

    public List<GroomingAddOnDetail> listByBookingRequestId(long bookingRequestId) {
        return groomingAddOnDetailMapper.select(
                c -> c.where(groomingAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(groomingAddOnDetail.deletedAt, isNull()));
    }

    public int update(GroomingAddOnDetail detail) {
        detail.setUpdatedAt(new Date());
        return groomingAddOnDetailMapper.update(c -> updateSelectiveColumns(detail, c)
                .where(groomingAddOnDetail.id, isEqualTo(detail.getId()))
                .and(groomingAddOnDetail.deletedAt, isNull()));
    }
    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public GroomingAddOnDetail get(long id) {
        return groomingAddOnDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        GroomingAddOnDetail update = new GroomingAddOnDetail();
        update.setDeletedAt(new Date());
        return groomingAddOnDetailMapper.update(c -> updateSelectiveColumns(update, c)
                .where(groomingAddOnDetail.id, isIn(ids))
                .and(groomingAddOnDetail.deletedAt, isNull()));
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(GroomingAddOnDetail entity) {

        populate(entity);

        groomingAddOnDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private void populate(GroomingAddOnDetail entity) {

        check(entity);

        // 设置 serviceTime 和 servicePrice
        if (entity.getServiceTime() == null || entity.getServicePrice() == null) {

            var bookingRequest = bookingRequestMapper
                    .selectByPrimaryKey(entity.getBookingRequestId())
                    .orElseThrow(() -> ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + entity.getBookingRequestId()));

            var builder = MustGetCustomizedServiceParam.builder();
            builder.serviceId(entity.getAddOnId());
            builder.companyId(bookingRequest.getCompanyId());
            builder.businessId(bookingRequest.getBusinessId());
            builder.petId(entity.getPetId());
            if (isNormal(entity.getStaffId())) {
                builder.staffId(entity.getStaffId());
            }

            var service = serviceHelper.mustGetCustomizedService(builder.build());

            entity.setServiceTime(service.getDuration());
            entity.setServicePrice(BigDecimal.valueOf(service.getPrice()));
            entity.setTaxId(service.getTaxId());
        }

        // 设置 startDate，默认使用 grooming_service_detail 的 startDate
        if (entity.getStartDate() == null) {
            // 如果 grooming addon 没有 startDate，默认使用 grooming service 的 startDate
            var serviceDetail = groomingServiceDetailMapper
                    .selectByPrimaryKey(entity.getServiceDetailId())
                    .orElseThrow(() -> ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR, "GroomingServiceDetail not found: " + entity.getServiceDetailId()));
            entity.setStartDate(serviceDetail.getStartDate());
        }

        // 设置 endDate 和 endTime，注意跨天的 case
        int dayCount = 0;

        if (entity.getStartTime() != null && entity.getServiceTime() != null) {
            dayCount = (entity.getStartTime() + entity.getServiceTime()) / 1440;
            var endTime = (entity.getStartTime() + entity.getServiceTime()) % 1440;
            entity.setEndTime(endTime);
        }

        if (entity.getStartDate() != null) {
            entity.setEndDate(
                    LocalDate.parse(entity.getStartDate()).plusDays(dayCount).toString());
        }
    }

    private static void check(GroomingAddOnDetail entity) {
        if (!isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is invalid");
        }
        if (!isNormal(entity.getServiceDetailId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is invalid");
        }
        if (!isNormal(entity.getAddOnId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "addOnId is invalid");
        }
        if (!isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is invalid");
        }
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return groomingAddOnDetailMapper.update(c -> c.set(groomingAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(groomingAddOnDetail.id, isEqualTo(id))
                .and(groomingAddOnDetail.deletedAt, isNull()));
    }

    /**
     * List grooming add-on detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of booking request id
     * @return bookingRequestId -> groomingServiceDetailId -> list of grooming add-on detail
     */
    public Map<Long, Map<Long, List<GroomingAddOnDetail>>> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return groomingAddOnDetailMapper
                .select(c -> c.where(groomingAddOnDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(groomingAddOnDetail.deletedAt, isNull()))
                .stream()
                .collect(Collectors.groupingBy(
                        GroomingAddOnDetail::getBookingRequestId,
                        Collectors.groupingBy(GroomingAddOnDetail::getServiceDetailId)));
    }
}

package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DayHourLimitGroupDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    public static final DayHourLimitGroup dayHourLimitGroup = new DayHourLimitGroup();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.id")
    public static final SqlColumn<Long> id = dayHourLimitGroup.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.only_accept_selected")
    public static final SqlColumn<Boolean> onlyAcceptSelected = dayHourLimitGroup.onlyAcceptSelected;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.created_at")
    public static final SqlColumn<Date> createdAt = dayHourLimitGroup.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: day_hour_limit_group.updated_at")
    public static final SqlColumn<Date> updatedAt = dayHourLimitGroup.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: day_hour_limit_group")
    public static final class DayHourLimitGroup extends AliasableSqlTable<DayHourLimitGroup> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> onlyAcceptSelected = column("only_accept_selected", JDBCType.BIT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public DayHourLimitGroup() {
            super("day_hour_limit_group", DayHourLimitGroup::new);
        }
    }
}
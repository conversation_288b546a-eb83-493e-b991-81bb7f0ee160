package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.service.online_booking.v1.CreateBoardingAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingAddOnDetailRequest;
import com.moego.idl.utils.v1.StringListValue;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;

class BoardingAddOnDetailConverterTest {

    @Test
    void testUpdateRequestToEntity_withSpecificDate() {
        // Arrange
        var updateRequest = UpdateBoardingAddOnDetailRequest.newBuilder()
                .setId(123L)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(List.of("2025-01-15", "2025-01-20"))
                        .build())
                .setQuantityPerDay(2)
                .build();

        // Act
        var result = BoardingAddOnDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);

        var expect = new BoardingAddOnDetail();
        expect.setId(123L);
        expect.setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE);
        expect.setSpecificDates(List.of(LocalDate.parse("2025-01-15"), LocalDate.parse("2025-01-20")));
        expect.setQuantityPerDay(2);

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testUpdateRequestToEntity_withEveryDay() {
        // Arrange
        var updateRequest = UpdateBoardingAddOnDetailRequest.newBuilder()
                .setId(123L)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                .build();

        // Act
        var result = BoardingAddOnDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);

        var expect = new BoardingAddOnDetail();
        expect.setId(123L);
        expect.setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY);
        expect.setSpecificDates(List.of());

        assertThat(result).isEqualTo(expect);
    }

    /**
     * {@link BoardingAddOnDetailConverter#entityToModel(BoardingAddOnDetail)}
     */
    @Test
    void testEntityToModel() {
        // Arrange
        var entity = new BoardingAddOnDetail();
        entity.setSpecificDates(List.of(LocalDate.parse("2025-01-15")));

        // Act
        var actual = BoardingAddOnDetailConverter.INSTANCE.entityToModel(entity);

        // Assert
        var expect = BoardingAddOnDetailModel.newBuilder()
                .addAllSpecificDates(List.of("2025-01-15"))
                .build();
        assertThat(actual).isEqualTo(expect);
    }

    /**
     * {@link BoardingAddOnDetailConverter#createRequestToEntity(CreateBoardingAddOnDetailRequest)}
     */
    @Test
    void testCreateRequestToEntity() {
        // Arrange
        var createRequest = CreateBoardingAddOnDetailRequest.newBuilder()
                .setPetId(1L)
                .setAddOnId(1L)
                .addAllSpecificDates(List.of("2025-01-15"))
                .build();

        // Act
        var actual = BoardingAddOnDetailConverter.INSTANCE.createRequestToEntity(createRequest);

        // Assert
        var expect = new BoardingAddOnDetail();
        expect.setPetId(1L);
        expect.setAddOnId(1L);
        expect.setSpecificDates(List.of(LocalDate.parse("2025-01-15")));
        assertThat(actual).isEqualTo(expect);
    }
}

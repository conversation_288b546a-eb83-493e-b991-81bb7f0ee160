package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.GroupClassServiceDetailDynamicSqlSupport.groupClassServiceDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.svc.online.booking.entity.GroupClassServiceDetail;
import com.moego.svc.online.booking.mapper.GroupClassServiceDetailMapper;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class GroupClassServiceDetailService {

    private final GroupClassServiceDetailMapper serviceDetailMapper;

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(GroupClassServiceDetail entity) {
        serviceDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return serviceDetailMapper.update(c -> c.set(groupClassServiceDetail.deletedAt)
                .equalTo(LocalDateTime.now())
                .where(groupClassServiceDetail.id, isEqualTo(id))
                .and(groupClassServiceDetail.deletedAt, isNull()));
    }

    /**
     * List group class service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of group class service detail
     */
    public List<GroupClassServiceDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return serviceDetailMapper.select(
                c -> c.where(groupClassServiceDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(groupClassServiceDetail.deletedAt, isNull()));
    }
}

package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingTimeRangeOverrideDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingTimeRangeOverrideMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingTimeRangeOverrideMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    BasicColumn[] selectList = BasicColumn.columnList(id, settingId, startDate, endDate, createdAt, updatedAt, isAvailable, dayTimeRange, timeRangeType, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingTimeRangeOverride> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingTimeRangeOverride> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingTimeRangeOverrideResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="setting_id", property="settingId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="is_available", property="isAvailable", jdbcType=JdbcType.BIT),
        @Result(column="day_time_range", property="dayTimeRange", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="time_range_type", property="timeRangeType", jdbcType=JdbcType.INTEGER),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BookingTimeRangeOverride> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingTimeRangeOverrideResult")
    Optional<BookingTimeRangeOverride> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int insertMultiple(Collection<BookingTimeRangeOverride> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingTimeRangeOverride, c ->
            c.map(settingId).toProperty("settingId")
            .map(startDate).toProperty("startDate")
            .map(endDate).toProperty("endDate")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(isAvailable).toProperty("isAvailable")
            .map(dayTimeRange).toProperty("dayTimeRange")
            .map(timeRangeType).toProperty("timeRangeType")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int insertSelective(BookingTimeRangeOverride row) {
        return MyBatis3Utils.insert(this::insert, row, bookingTimeRangeOverride, c ->
            c.map(settingId).toPropertyWhenPresent("settingId", row::getSettingId)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(isAvailable).toPropertyWhenPresent("isAvailable", row::getIsAvailable)
            .map(dayTimeRange).toPropertyWhenPresent("dayTimeRange", row::getDayTimeRange)
            .map(timeRangeType).toPropertyWhenPresent("timeRangeType", row::getTimeRangeType)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default Optional<BookingTimeRangeOverride> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default List<BookingTimeRangeOverride> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default List<BookingTimeRangeOverride> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default Optional<BookingTimeRangeOverride> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingTimeRangeOverride, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingTimeRangeOverride row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalTo(row::getSettingId)
                .set(startDate).equalTo(row::getStartDate)
                .set(endDate).equalTo(row::getEndDate)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(isAvailable).equalTo(row::getIsAvailable)
                .set(dayTimeRange).equalTo(row::getDayTimeRange)
                .set(timeRangeType).equalTo(row::getTimeRangeType)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingTimeRangeOverride row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalToWhenPresent(row::getSettingId)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(isAvailable).equalToWhenPresent(row::getIsAvailable)
                .set(dayTimeRange).equalToWhenPresent(row::getDayTimeRange)
                .set(timeRangeType).equalToWhenPresent(row::getTimeRangeType)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    default int updateByPrimaryKeySelective(BookingTimeRangeOverride row) {
        return update(c ->
            c.set(settingId).equalToWhenPresent(row::getSettingId)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(isAvailable).equalToWhenPresent(row::getIsAvailable)
            .set(dayTimeRange).equalToWhenPresent(row::getDayTimeRange)
            .set(timeRangeType).equalToWhenPresent(row::getTimeRangeType)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
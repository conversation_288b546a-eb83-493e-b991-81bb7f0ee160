package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingTimeRangeSettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingTimeRangeSettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingTimeRangeSettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, serviceItemType, isCustomized, scheduleType, startDate, endDate, updatedBy, createdAt, updatedAt, capacitySettingId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingTimeRangeSetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingTimeRangeSetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingTimeRangeSettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_customized", property="isCustomized", jdbcType=JdbcType.BIT),
        @Result(column="schedule_type", property="scheduleType", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE),
        @Result(column="end_date", property="endDate", jdbcType=JdbcType.DATE),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="capacity_setting_id", property="capacitySettingId", jdbcType=JdbcType.BIGINT)
    })
    List<BookingTimeRangeSetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingTimeRangeSettingResult")
    Optional<BookingTimeRangeSetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int insertMultiple(Collection<BookingTimeRangeSetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingTimeRangeSetting, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(isCustomized).toProperty("isCustomized")
            .map(scheduleType).toProperty("scheduleType")
            .map(startDate).toProperty("startDate")
            .map(endDate).toProperty("endDate")
            .map(updatedBy).toProperty("updatedBy")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(capacitySettingId).toProperty("capacitySettingId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int insertSelective(BookingTimeRangeSetting row) {
        return MyBatis3Utils.insert(this::insert, row, bookingTimeRangeSetting, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(isCustomized).toPropertyWhenPresent("isCustomized", row::getIsCustomized)
            .map(scheduleType).toPropertyWhenPresent("scheduleType", row::getScheduleType)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
            .map(endDate).toPropertyWhenPresent("endDate", row::getEndDate)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", row::getUpdatedBy)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(capacitySettingId).toPropertyWhenPresent("capacitySettingId", row::getCapacitySettingId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default Optional<BookingTimeRangeSetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default List<BookingTimeRangeSetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default List<BookingTimeRangeSetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default Optional<BookingTimeRangeSetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingTimeRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingTimeRangeSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(isCustomized).equalTo(row::getIsCustomized)
                .set(scheduleType).equalTo(row::getScheduleType)
                .set(startDate).equalTo(row::getStartDate)
                .set(endDate).equalTo(row::getEndDate)
                .set(updatedBy).equalTo(row::getUpdatedBy)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(capacitySettingId).equalTo(row::getCapacitySettingId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingTimeRangeSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(isCustomized).equalToWhenPresent(row::getIsCustomized)
                .set(scheduleType).equalToWhenPresent(row::getScheduleType)
                .set(startDate).equalToWhenPresent(row::getStartDate)
                .set(endDate).equalToWhenPresent(row::getEndDate)
                .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(capacitySettingId).equalToWhenPresent(row::getCapacitySettingId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    default int updateByPrimaryKeySelective(BookingTimeRangeSetting row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(isCustomized).equalToWhenPresent(row::getIsCustomized)
            .set(scheduleType).equalToWhenPresent(row::getScheduleType)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .set(endDate).equalToWhenPresent(row::getEndDate)
            .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(capacitySettingId).equalToWhenPresent(row::getCapacitySettingId)
            .where(id, isEqualTo(row::getId))
        );
    }
}
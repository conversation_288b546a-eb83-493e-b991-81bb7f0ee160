package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table staff_availability_time_day
 */
public class StaffAvailabilityTimeDay {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.staff_id")
    private Long staffId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.day_of_week")
    private Integer dayOfWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.is_available")
    private Boolean isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.limit_ids")
    private List<Long> limitIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.staff_id")
    public Long getStaffId() {
        return staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.staff_id")
    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.day_of_week")
    public Integer getDayOfWeek() {
        return dayOfWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.day_of_week")
    public void setDayOfWeek(Integer dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.is_available")
    public Boolean getIsAvailable() {
        return isAvailable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.is_available")
    public void setIsAvailable(Boolean isAvailable) {
        this.isAvailable = isAvailable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.limit_ids")
    public List<Long> getLimitIds() {
        return limitIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.limit_ids")
    public void setLimitIds(List<Long> limitIds) {
        this.limitIds = limitIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_time_day.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_time_day")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", companyId=").append(companyId);
        sb.append(", businessId=").append(businessId);
        sb.append(", staffId=").append(staffId);
        sb.append(", dayOfWeek=").append(dayOfWeek);
        sb.append(", isAvailable=").append(isAvailable);
        sb.append(", limitIds=").append(limitIds);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_time_day")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StaffAvailabilityTimeDay other = (StaffAvailabilityTimeDay) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getStaffId() == null ? other.getStaffId() == null : this.getStaffId().equals(other.getStaffId()))
            && (this.getDayOfWeek() == null ? other.getDayOfWeek() == null : this.getDayOfWeek().equals(other.getDayOfWeek()))
            && (this.getIsAvailable() == null ? other.getIsAvailable() == null : this.getIsAvailable().equals(other.getIsAvailable()))
            && (this.getLimitIds() == null ? other.getLimitIds() == null : this.getLimitIds().equals(other.getLimitIds()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_time_day")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getStaffId() == null) ? 0 : getStaffId().hashCode());
        result = prime * result + ((getDayOfWeek() == null) ? 0 : getDayOfWeek().hashCode());
        result = prime * result + ((getIsAvailable() == null) ? 0 : getIsAvailable().hashCode());
        result = prime * result + ((getLimitIds() == null) ? 0 : getLimitIds().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}
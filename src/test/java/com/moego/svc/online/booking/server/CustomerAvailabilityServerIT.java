package com.moego.svc.online.booking.server;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.svc.online.booking.dto.CustomerBlockInfoDTO;
import com.moego.svc.online.booking.entity.BlockCustomer;
import com.moego.svc.online.booking.mapper.BlockCustomerMapper;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link CustomerAvailabilityServer}
 */
@SpringBootTest
@Disabled("local test only")
@ExtendWith(OutputCaptureExtension.class)
class CustomerAvailabilityServerIT {

    @Autowired
    CustomerAvailabilityServer customerAvailabilityServer;

    @Autowired
    BlockCustomerMapper blockCustomerMapper;

    /**
     * {@link CustomerAvailabilityServer#paginateCustomerBlockService(ListBlockedCustomerRequest)}
     */
    @Test
    @Transactional
    void paginateCustomerBlockService_Success(CapturedOutput output) {

        /*
        新增 3 条数据，page size 为 2，page num 为 2，预期返回肯定有值
         */

        var companyId = 100870L;

        for (int i = 0; i < 3; i++) {
            var bc = new BlockCustomer();
            bc.setCompanyId(companyId);
            bc.setServiceItemType(ServiceItemType.GROOMING_VALUE);
            bc.setCustomerId((long) i);
            bc.setIsActive(true);
            bc.setUpdatedBy(0L);
            blockCustomerMapper.insertSelective(bc);
        }

        var serviceItemTypes = List.of(ServiceItemType.GROOMING, ServiceItemType.BOARDING, ServiceItemType.DAYCARE);
        var customerIds = List.<Long>of();
        var pagination =
                PaginationRequest.newBuilder().setPageNum(2).setPageSize(2).build();
        var request = ListBlockedCustomerRequest.newBuilder()
                .addAllServiceItemTypes(serviceItemTypes)
                .addAllCustomerIds(customerIds)
                .setPagination(pagination)
                .setCompanyId(companyId)
                .build();

        var customerToBlockServices = customerAvailabilityServer.paginateCustomerBlockService(request);
        assertThat(customerToBlockServices.getFirst()).isNotEmpty();
        assertThat(customerToBlockServices.getFirst().get(0)).isInstanceOf(CustomerBlockInfoDTO.class);
        // see https://moegoworkspace.slack.com/archives/C01K60LEP7E/p1737881656023879
        assertThat(customerToBlockServices.getFirst().get(0)).isNotInstanceOf(Map.class);

        // Verify the SQL
        assertThat(output)
                .contains(
                        "select array(select jsonb_array_elements_text(jsonb_agg(service_item_type))::integer) as service_item_types, customer_id from block_customer where company_id = ? and service_item_type in (?,?,?) and is_active = ? group by customer_id LIMIT ? OFFSET ?");
    }
}

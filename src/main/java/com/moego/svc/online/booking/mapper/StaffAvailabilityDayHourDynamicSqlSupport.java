package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class StaffAvailabilityDayHourDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    public static final StaffAvailabilityDayHour staffAvailabilityDayHour = new StaffAvailabilityDayHour();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.id")
    public static final SqlColumn<Long> id = staffAvailabilityDayHour.id;

    /**
     * Database Column Remarks:
     *   0-time, 1-slot, 2-disable
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_type")
    public static final SqlColumn<Integer> dayType = staffAvailabilityDayHour.dayType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.day_id")
    public static final SqlColumn<Long> dayId = staffAvailabilityDayHour.dayId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.start_time")
    public static final SqlColumn<Integer> startTime = staffAvailabilityDayHour.startTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.end_time")
    public static final SqlColumn<Integer> endTime = staffAvailabilityDayHour.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.capacity")
    public static final SqlColumn<Integer> capacity = staffAvailabilityDayHour.capacity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.limit_ids")
    public static final SqlColumn<List<Long>> limitIds = staffAvailabilityDayHour.limitIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.created_at")
    public static final SqlColumn<Date> createdAt = staffAvailabilityDayHour.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_day_hour.updated_at")
    public static final SqlColumn<Date> updatedAt = staffAvailabilityDayHour.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_day_hour")
    public static final class StaffAvailabilityDayHour extends AliasableSqlTable<StaffAvailabilityDayHour> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> dayType = column("day_type", JDBCType.INTEGER);

        public final SqlColumn<Long> dayId = column("day_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> capacity = column("capacity", JDBCType.INTEGER);

        public final SqlColumn<List<Long>> limitIds = column("limit_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public StaffAvailabilityDayHour() {
            super("staff_availability_day_hour", StaffAvailabilityDayHour::new);
        }
    }
}
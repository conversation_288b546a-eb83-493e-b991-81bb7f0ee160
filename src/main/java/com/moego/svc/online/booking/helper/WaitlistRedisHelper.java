package com.moego.svc.online.booking.helper;

import com.moego.common.utils.RedisUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@RequiredArgsConstructor
@Slf4j
public class WaitlistRedisHelper {
    private static final String WAITLIST_NAMESPACE = "Waitlist";
    private static final String CHECK_SUBSPACE = "check";
    private static final String AVAILABLE_SUBSPACE = "available";
    private static final String CHECK_HASH_KEY = WAITLIST_NAMESPACE + RedisUtil.KEY_SEP + CHECK_SUBSPACE;
    private static final String AVAILABLE_KEY_PREFIX = WAITLIST_NAMESPACE + RedisUtil.KEY_SEP + AVAILABLE_SUBSPACE;
    private static final long WAITLIST_CHECK_EXPIRE_HOURS = 12; // 12 hours

    private final RedisUtil redisUtil;
    /**
     * == Waitlist 可用性通知 - Redis 设计思路 ==
     * 需求：
     * 1. 如果过去 5 分钟内，某商家的 bd 或 grooming 发生变动。
     * 2. 并且，这个变动恰好使得某个 waitlist 变为 available。
     * 3. 那么，在变动发生大约 5 分钟后，需要给用户发通知，waitlist slot available。
     * <p>
     * 关键点：不可能记录所有的预约变动，然后进行复杂计算，所以采用 waitlist available 自己的状态变更，去实现这个需求
     * 具体操作：
     * 1、设计了一个 hash key，去记录有哪些 business，过去 5 分钟发生了变动
     * 2、每个 waitlist，设计了一个 string key，去记录 waitlist is available
     * <p>
     * 每隔 5 分钟查一次，将所有的 waitlist available 状态维护到 redis string key 内
     * 1、 string key 变更有这几种情况
     *  a. 从 false -> true，需要发通知，且记录 true
     *  b. 从 false -> false，跳过
     *  c. 从 true -> true，跳过
     *  d. 从 true -> false，需要设置为 false
     * 2、特殊处理，false 时，不额外记录 key，而是直接删除，所以没有 redis string key，等于 false
     * 3、特殊处理，grooming 和 bd 的 waitlist，id 可能会重复，所以 key 要标识出 grooming 和 bd 两个 type
     *
     */

    /**
     * 获取需要检查的等待列表
     * @return 公司ID和业务ID的列表
     */
    public List<Pair<Long, Long>> getAllWaitlistToCheck() {
        List<Pair<Long, Long>> result = new ArrayList<>();
        Map<Object, Object> allEntries = redisUtil.hGetAll(CHECK_HASH_KEY);
        redisUtil.delete(CHECK_HASH_KEY);

        if (allEntries != null) {
            for (Map.Entry<Object, Object> entry : allEntries.entrySet()) {
                String hashKey = (String) entry.getKey();
                String value = (String) entry.getValue();

                if (Boolean.TRUE.toString().equals(value)) {
                    Pair<Long, Long> ids = WaitlistRedisHelper.parseHashSubKeyToIds(hashKey);
                    if (ids != null) {
                        result.add(ids);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 发送检查 hash sub key 生成
     */
    private static String getWaitlistHashSubKey(long companyId, long businessId) {
        return String.format("%d%s%d", companyId, RedisUtil.KEY_SEP, businessId);
    }

    /**
     * 发送检查 解析 hash sub key 为 company ID 和 business ID
     * @param hashKey
     * @return
     */
    @Nullable
    private static Pair<Long, Long> parseHashSubKeyToIds(String hashKey) {
        try {
            String[] parts = hashKey.split(Pattern.quote(RedisUtil.KEY_SEP));
            if (parts.length == 2) {
                long companyId = Long.parseLong(parts[0]);
                long businessId = Long.parseLong(parts[1]);
                return Pair.of(companyId, businessId);
            }
        } catch (NumberFormatException e) {
            log.error("Failed to parse hash key: {}", hashKey, e);
        }
        return null;
    }

    /**
     * 发送检查 hash sub key 设置
     * @param companyId 公司ID
     * @param businessId 业务ID
     */
    public void setWaitlistCheckFlag(long companyId, long businessId) {
        String hashSubKey = getWaitlistHashSubKey(companyId, businessId);
        redisUtil.hPut(CHECK_HASH_KEY, hashSubKey, Boolean.TRUE.toString());
        redisUtil.expire(CHECK_HASH_KEY, WAITLIST_CHECK_EXPIRE_HOURS, TimeUnit.HOURS);
    }

    /**
     * waitlistIsAvailable key 生成
     * grooming 和 bd 是两个类型的 key
     */
    private static String buildBdAvailableKey(Long bdWaitlistId) {
        return String.format(AVAILABLE_KEY_PREFIX + ":bd:%d", bdWaitlistId);
    }

    public static String buildGroomingAvailableKey(Long groomingId) {
        return String.format(AVAILABLE_KEY_PREFIX + ":g:%d", groomingId);
    }

    private boolean getBeforeValue(String redisKey) {
        var value = redisUtil.get(redisKey);
        return StringUtils.hasText(value) && Boolean.TRUE.toString().equals(value);
    }

    /**
     * waitlistIsAvailable key 设置 true
     * @param redisKey
     */
    private void setBeforeValue(String redisKey) {
        redisUtil.setEx(redisKey, Boolean.TRUE.toString(), 30 * 3, TimeUnit.DAYS);
    }

    /**
     * waitlistIsAvailable key 删除
     * @param redisKey
     */
    private void deleteBeforeKey(String redisKey) {
        redisUtil.delete(redisKey);
    }

    /**
     * waitlistIsAvailable 是否发送判断
     */
    public boolean sendCheckCompare(boolean newAvailable, Long bdId, Long groomingId) {
        String redisKey = null;
        if (bdId != null) {
            redisKey = buildBdAvailableKey(bdId);
        }
        if (groomingId != null) {
            redisKey = buildGroomingAvailableKey(groomingId);
        }
        if (!StringUtils.hasText(redisKey)) {
            return false;
        }
        boolean beforeAvailable = getBeforeValue(redisKey);
        // null -> true. 发送通知 && setKey
        if (!beforeAvailable && newAvailable) {
            setBeforeValue(redisKey);
            return true;
        }
        // true -> false 删除 key
        if (beforeAvailable && !newAvailable) {
            deleteBeforeKey(redisKey);
            return false;
        }
        // true > true 跳过
        if (beforeAvailable) {
            return false;
        }
        // false -> false 跳过
        return false;
    }
}

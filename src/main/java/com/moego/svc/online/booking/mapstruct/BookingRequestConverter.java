package com.moego.svc.online.booking.mapstruct;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestSourcePlatform;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.online_booking.v1.ListWaitlistsRequest;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingRequestRequest;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.entity.BookingRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mapstruct.AfterMapping;
import org.mapstruct.BeforeMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class BookingRequestConverter {
    public static final BookingRequestConverter INSTANCE = Mappers.getMapper(BookingRequestConverter.class);

    public abstract ListBookingRequestsRequest convertRequest(ListWaitlistsRequest request);

    @Mapping(target = "sourcePlatform", ignore = true)
    public abstract BookingRequestModel entityToModel(BookingRequest entity);

    public abstract BookingRequest createRequestToEntity(CreateBookingRequestRequest createRequest);

    public abstract BookingRequest updateRequestToEntity(UpdateBookingRequestRequest updateRequest);

    public abstract BookingRequest replaceRequestToEntity(ReplaceBookingRequestRequest updateRequest);

    public BookingRequestFilterDTO countRequestToFilterDTO(CountBookingRequestsRequest request) {
        BookingRequestFilterDTO dto = new BookingRequestFilterDTO();
        final var tenant = request.getTenant();
        dto.setCompanyId(tenant.getCompanyId());
        if (tenant.hasBusinessId()) {
            dto.setBusinessIds(List.of(tenant.getBusinessId()));
        }

        if (request.hasFilters()) {
            final var filters = request.getFilters();
            if (filters.hasCreatedTimeRange()) {
                dto.setCreatedAfter(
                        pbTimestampToDate(filters.getCreatedTimeRange().getStartTime()));
                dto.setCreatedBefore(
                        pbTimestampToDate(filters.getCreatedTimeRange().getEndTime()));
            }

            if (!CollectionUtils.isEmpty(filters.getStatusesList())) {
                dto.setStatuses(filters.getStatusesValueList());
            }
            if (!CollectionUtils.isEmpty(filters.getPaymentStatusesList())) {
                dto.setPaymentStatuses(filters.getPaymentStatusesList());
            }
        }

        return dto;
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    protected com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    protected java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    protected int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    protected BookingRequestStatus intToBookingRequestStatus(Integer value) {
        return BookingRequestStatus.forNumber(value);
    }

    @BeforeMapping
    protected void handleSourcePlatform(BookingRequest entity, @MappingTarget BookingRequestModel.Builder target) {
        if (entity.getSourcePlatform() == null || entity.getSourcePlatform().isEmpty()) {
            target.setSourcePlatform(BookingRequestSourcePlatform.BOOKING_REQUEST_SOURCE_PLATFORM_UNSPECIFIED);
        }
    }

    @AfterMapping
    protected void entityToModelAfterMapping(BookingRequest source, @MappingTarget BookingRequestModel.Builder target) {
        // 优先使用 payment_status 的值
        if (source.getPaymentStatus() != BookingRequestModel.PaymentStatus.PAYMENT_STATUS_UNSPECIFIED) {
            // see com.moego.server.grooming.service.ob.OBRequestService.buildPrepaid
            target.setIsPrepaid(target.getPaymentStatus() != BookingRequestModel.PaymentStatus.NO_PAYMENT);
        }
    }

    @AfterMapping
    protected void createRequestToEntityAfterMapping(
            CreateBookingRequestRequest source, @MappingTarget BookingRequest target) {
        if (source.getPaymentStatus() != BookingRequestModel.PaymentStatus.PAYMENT_STATUS_UNSPECIFIED) {
            target.setIsPrepaid(source.getPaymentStatus() != BookingRequestModel.PaymentStatus.NO_PAYMENT);
        }
        target.setServiceTypeInclude(getServiceTypeInclude(source.getServicesList()));
    }

    @AfterMapping
    protected void updateRequestToEntityAfterMapping(
            UpdateBookingRequestRequest source, @MappingTarget BookingRequest target) {
        if (source.getPaymentStatus() != BookingRequestModel.PaymentStatus.PAYMENT_STATUS_UNSPECIFIED) {
            target.setIsPrepaid(source.getPaymentStatus() != BookingRequestModel.PaymentStatus.NO_PAYMENT);
        }
    }

    protected static int getServiceTypeInclude(List<CreateBookingRequestRequest.Service> servicesList) {
        List<Integer> arr = new ArrayList<>();
        for (CreateBookingRequestRequest.Service service : servicesList) {
            switch (service.getServiceCase()) {
                case GROOMING -> arr.add(ServiceItemEnum.GROOMING.getServiceItem());
                case BOARDING -> arr.add(ServiceItemEnum.BOARDING.getServiceItem());
                case DAYCARE -> arr.add(ServiceItemEnum.DAYCARE.getServiceItem());
                case EVALUATION -> arr.add(ServiceItemEnum.EVALUATION.getServiceItem());
                case DOG_WALKING -> arr.add(ServiceItemEnum.DOG_WALKING.getServiceItem());
                case GROUP_CLASS -> arr.add(ServiceItemEnum.GROUP_CLASS.getServiceItem());
                default -> {
                    // no-op
                }
            }
        }
        return ServiceItemEnum.convertBitValueList(arr);
    }

    String LABEL = "label";

    protected List<AppointmentPetFeedingScheduleDef> feedingToAppointmentPetFeedingScheduleDef(
            List<FeedingModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream()
                .map(this::feedingToAppointmentPetFeedingScheduleDef)
                .filter(Objects::nonNull)
                .toList();
    }

    protected AppointmentPetFeedingScheduleDef feedingToAppointmentPetFeedingScheduleDef(FeedingModel model) {
        if (Objects.isNull(model) || CollectionUtils.isEmpty(model.getTimeList())) {
            return null;
        }
        return AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingAmount(getAmount(model))
                .setFeedingUnit(model.getUnit())
                .setFeedingType(model.getFoodType())
                .setFeedingSource(model.getFoodSource())
                .setFeedingInstruction(model.getInstruction())
                .setFeedingNote(model.getNote())
                .addAllFeedingTimes(model.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson(LABEL, time.getLabel())
                                .build())
                        .toList())
                .build();
    }

    protected String getAmount(FeedingModel model) {
        return model.hasAmountStr() ? model.getAmountStr() : String.valueOf(model.getAmount());
    }

    protected String getAmount(MedicationModel model) {
        return model.hasAmountStr() ? model.getAmountStr() : String.valueOf(model.getAmount());
    }

    protected Short getShort(BookingRequestModel.Source source) {
        return (short) source.getNumber();
    }

    protected BookingRequestModel.Source setShort(Short source) {
        return BookingRequestModel.Source.forNumber(source.intValue());
    }

    protected List<AppointmentPetMedicationScheduleDef> medicationToAppointmentPetMedicationScheduleDef(
            List<MedicationModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream()
                .map(this::medicationToAppointmentPetMedicationScheduleDef)
                .filter(Objects::nonNull)
                .toList();
    }

    protected AppointmentPetMedicationScheduleDef medicationToAppointmentPetMedicationScheduleDef(
            MedicationModel model) {
        if (Objects.isNull(model) || CollectionUtils.isEmpty(model.getTimeList())) {
            return null;
        }
        return AppointmentPetMedicationScheduleDef.newBuilder()
                .setMedicationAmount(getAmount(model))
                .setMedicationUnit(model.getUnit())
                .setMedicationName(model.getMedicationName())
                .setMedicationNote(model.getNotes())
                .addAllMedicationTimes(model.getTimeList().stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getTime())
                                .putExtraJson(LABEL, time.getLabel())
                                .build())
                        .toList())
                .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(model.getSelectedDate().getDateType())
                        .addAllSpecificDates(model.getSelectedDate().getSpecificDatesList())
                        .build())
                .build();
    }

    public SelectedServiceDef boardingToSelectedServiceDef(
            BoardingServiceDetailModel model,
            Long lodgingId,
            List<FeedingModel> feedings,
            List<MedicationModel> medications) {
        var selectedServiceDef = SelectedServiceDef.newBuilder()
                .setServiceId(model.getServiceId())
                .setStartDate(model.getStartDate())
                .setEndDate(model.getEndDate())
                .setStartTime(model.getStartTime())
                .setEndTime(model.getEndTime())
                .setServicePrice(model.getServicePrice())
                .addAllFeedings(feedingToAppointmentPetFeedingScheduleDef(feedings))
                .addAllMedications(medicationToAppointmentPetMedicationScheduleDef(medications))
                .setLodgingId(lodgingId);
        return selectedServiceDef.build();
    }

    public SelectedAddOnDef boardingAddOnToSelectedServiceAddOnDef(
            BoardingAddOnDetailModel model, String startDate, Integer startTime, Long associatedServiceId) {
        return SelectedAddOnDef.newBuilder()
                .setAddOnId(model.getAddOnId())
                .setStartDate(startDate)
                .setStartTime(startTime)
                .setAddonDateType(model.getDateType())
                .addAllSpecificDates(model.getSpecificDatesList())
                .setServicePrice(model.getServicePrice())
                .setAssociatedServiceId(associatedServiceId)
                .setQuantityPerDay(model.getQuantityPerDay())
                .build();
    }

    public SelectedServiceDef daycareToSelectedServiceDef(
            DaycareServiceDetailModel model,
            String startDate,
            String endDate,
            Long lodgingId,
            List<FeedingModel> feedings,
            List<MedicationModel> medications) {
        var selectedServiceDef = SelectedServiceDef.newBuilder()
                .setServiceId(model.getServiceId())
                .setStartTime(model.getStartTime())
                .setEndTime(model.getEndTime())
                .setServicePrice(model.getServicePrice())
                .addAllSpecificDates(model.getSpecificDatesList())
                .setServiceTime(model.getEndTime() - model.getStartTime())
                .addAllFeedings(feedingToAppointmentPetFeedingScheduleDef(feedings))
                .addAllMedications(medicationToAppointmentPetMedicationScheduleDef(medications))
                .setDateType(
                        PetDetailDateType
                                .PET_DETAIL_DATE_DATE_POINT); // date type 应该从数据库中读取，目前业务上暂时不支持其他类型，因此取默认值 Date Point
        if (startDate != null) {
            selectedServiceDef.setStartDate(startDate);
        }
        if (endDate != null) {
            selectedServiceDef.setEndDate(endDate);
        }
        if (lodgingId != null) {
            selectedServiceDef.setLodgingId(lodgingId);
        }
        return selectedServiceDef.build();
    }

    public SelectedAddOnDef daycareAddOnToSelectedServiceAddOnDef(
            DaycareAddOnDetailModel model,
            String startDate,
            Integer startTime,
            Long associatedServiceId,
            List<String> specificDates) {
        PetDetailDateType addonDateType = model.getIsEveryday()
                ? PetDetailDateType.PET_DETAIL_DATE_EVERYDAY
                : PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE;
        return SelectedAddOnDef.newBuilder()
                .setAddOnId(model.getAddOnId())
                .setStartDate(startDate)
                .setStartTime(startTime)
                .setAddonDateType(addonDateType)
                .addAllSpecificDates(
                        CollectionUtils.isEmpty(specificDates) ? model.getSpecificDatesList() : specificDates)
                .setServicePrice(model.getServicePrice())
                .setServiceTime(model.getDuration())
                .setAssociatedServiceId(associatedServiceId)
                .setQuantityPerDay(model.getQuantityPerDay())
                .build();
    }

    public abstract SelectedServiceDef groomingToSelectedServiceDef(GroomingServiceDetailModel model);

    public SelectedAddOnDef groomingAddOnToSelectedServiceAddOnDef(GroomingAddOnDetailModel model, String startDate) {
        return SelectedAddOnDef.newBuilder()
                .setAddOnId(model.getAddOnId())
                .setAddonDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .setStartDate(startDate)
                .addAllSpecificDates(List.of(startDate))
                .setStartTime(model.getStartTime())
                .setStaffId(model.getStaffId())
                .setServiceTime(model.getServiceTime())
                .setServicePrice(model.getServicePrice())
                .build();
    }

    @Mapping(target = "serviceId", source = "evaluationId")
    @Mapping(target = "serviceTime", source = "duration")
    @Mapping(target = "servicePrice", source = "servicePrice")
    public abstract SelectedEvaluationDef evaluationToSelectedServiceDef(EvaluationTestDetailModel model);

    public abstract SelectedServiceDef dogWalkingToSelectedServiceDef(DogWalkingServiceDetailModel model);
}

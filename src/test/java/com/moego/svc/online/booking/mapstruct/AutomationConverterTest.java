package com.moego.svc.online.booking.mapstruct;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;

import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.AutomationSettingModel;
import com.moego.idl.models.online_booking.v1.ProfileUpdateCondition;
import com.moego.idl.models.online_booking.v1.VaccineStatusCondition;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.AutomationSetting;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AutomationConverterTest {

    private AutomationConverter automationConverter;

    @BeforeEach
    void setUp() {
        automationConverter = Mappers.getMapper(AutomationConverter.class);
    }

    @Test
    void testToModel() {
        // Arrange
        AutomationConditionDef DEFAULT_AUTOMATION_CONDITION = AutomationConditionDef.newBuilder()
                .setAcceptClientType(AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH)
                .setProfileUpdateCondition(ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL)
                .setVaccineStatusCondition(VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL)
                .build();

        AutomationSetting entity = new AutomationSetting();
        entity.setId(1L);
        entity.setAutoAcceptCondition(JsonUtil.toJson(DEFAULT_AUTOMATION_CONDITION));
        entity.setServiceItemType(1);
        Date createdAt = new Date();
        Date updatedAt = new Date();
        entity.setCreatedAt(createdAt);
        entity.setUpdatedAt(updatedAt);

        try (MockedStatic<JsonUtil> jsonUtilMock = mockStatic(JsonUtil.class)) {
            AutomationConditionDef conditionDef = AutomationConditionDef.newBuilder()
                    .setAcceptClientType(AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH)
                    .build();
            jsonUtilMock
                    .when(() -> JsonUtil.toBean(anyString(), eq(AutomationConditionDef.class)))
                    .thenReturn(conditionDef);

            // Act
            AutomationSettingModel result = automationConverter.toModel(entity);

            // Assert
            assertNotNull(result);
            assertEquals(1L, result.getId());
            assertEquals(conditionDef, result.getAutoAcceptCondition());
            assertEquals(
                    AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH,
                    result.getAutoAcceptCondition().getAcceptClientType());
        }
    }

    @Test
    void testMapAutoAcceptCondition_EmptyString() {
        // Act
        AutomationConditionDef result = AutomationConverter.mapAutoAcceptCondition("");

        // Assert
        assertEquals(AutomationConditionDef.getDefaultInstance(), result);
    }

    @Test
    void testMapAutoAcceptCondition_Null() {
        // Act
        AutomationConditionDef result = AutomationConverter.mapAutoAcceptCondition(null);

        // Assert
        assertEquals(AutomationConditionDef.getDefaultInstance(), result);
    }
}

package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 * Database Table Remarks:
 *   boarding auto assign record
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table boarding_auto_assign
 */
public class BoardingAutoAssign {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.booking_request_id")
    private Long bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.boarding_service_detail_id")
    private Long boardingServiceDetailId;

    /**
     * Database Column Remarks:
     *   The id of lodging
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.lodging_id")
    private Long lodgingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.deleted_at")
    private Date deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.boarding_service_detail_id")
    public Long getBoardingServiceDetailId() {
        return boardingServiceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.boarding_service_detail_id")
    public void setBoardingServiceDetailId(Long boardingServiceDetailId) {
        this.boardingServiceDetailId = boardingServiceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.lodging_id")
    public Long getLodgingId() {
        return lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.lodging_id")
    public void setLodgingId(Long lodgingId) {
        this.lodgingId = lodgingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", boardingServiceDetailId=").append(boardingServiceDetailId);
        sb.append(", lodgingId=").append(lodgingId);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BoardingAutoAssign other = (BoardingAutoAssign) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getBoardingServiceDetailId() == null ? other.getBoardingServiceDetailId() == null : this.getBoardingServiceDetailId().equals(other.getBoardingServiceDetailId()))
            && (this.getLodgingId() == null ? other.getLodgingId() == null : this.getLodgingId().equals(other.getLodgingId()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getBoardingServiceDetailId() == null) ? 0 : getBoardingServiceDetailId().hashCode());
        result = prime * result + ((getLodgingId() == null) ? 0 : getLodgingId().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
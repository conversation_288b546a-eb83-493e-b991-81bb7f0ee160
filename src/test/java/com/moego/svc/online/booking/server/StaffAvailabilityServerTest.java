package com.moego.svc.online.booking.server;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import com.google.type.DayOfWeek;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityResponse;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import com.moego.svc.online.booking.service.StaffAvailabilityDayHourService;
import com.moego.svc.online.booking.service.StaffAvailabilityInitService;
import com.moego.svc.online.booking.service.StaffAvailabilityService;
import io.grpc.stub.StreamObserver;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StaffAvailabilityServerTest {
    @Mock
    private StaffAvailabilityService staffAvailabilityService;

    @Mock
    private StaffAvailabilityInitService staffAvailabilityInitService;

    @Mock
    private StaffAvailabilityDayHourService staffAvailabilityDayHourService;

    @Mock
    private StreamObserver<UpdateStaffAvailabilityResponse> responseObserver;

    @InjectMocks
    private StaffAvailabilityServer server;

    @Captor
    private ArgumentCaptor<UpdateStaffAvailabilityResponse> responseCaptor;

    @Test
    void updateStaffAvailability_whenEmptyRequest_shouldReturnDefaultResponse() {
        // Arrange
        UpdateStaffAvailabilityRequest request = UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(1L)
                .setBusinessId(2L)
                .build();

        // Act
        server.updateStaffAvailability(request, responseObserver);

        // Assert
        verify(responseObserver).onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        verify(responseObserver).onCompleted();
        verifyNoInteractions(staffAvailabilityService, staffAvailabilityDayHourService);
    }

    @Test
    void updateStaffAvailability_withSlotAndTimeAvailability_shouldUpdateBoth() {
        // Arrange
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;

        StaffAvailabilityDef staffAvailability = StaffAvailabilityDef.newBuilder()
                .setStaffId(staffId)
                .addSlotAvailabilityDayList(createSlotAvailabilityDay())
                .addTimeAvailabilityDayList(createTimeAvailabilityDay())
                .build();

        UpdateStaffAvailabilityRequest request = UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addStaffAvailabilityList(staffAvailability)
                .build();

        Map<String, StaffAvailabilitySlotDay> slotDayMap = new HashMap<>();
        Map<String, StaffAvailabilityTimeDay> timeDayMap = new HashMap<>();

        when(staffAvailabilityDayHourService.getAvailabilitySlotDayMapByStaffIds(businessId, List.of(staffId)))
                .thenReturn(slotDayMap);
        when(staffAvailabilityDayHourService.getAvailabilityTimeDayMapByStaffIds(businessId, List.of(staffId)))
                .thenReturn(timeDayMap);
        doNothing().when(staffAvailabilityInitService).checkStaffAvailabilityWithInit(any(), any(), any());

        // Act
        server.updateStaffAvailability(request, responseObserver);

        // Assert
        verify(staffAvailabilityService).batchUpdate(any());
        verify(staffAvailabilityDayHourService)
                .updateStaffAvailabilitySlotDays(companyId, businessId, staffAvailability, slotDayMap);
        verify(staffAvailabilityDayHourService)
                .updateStaffAvailabilityTimeDays(companyId, businessId, staffAvailability, timeDayMap);

        // 验证响应
        verify(responseObserver).onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        verify(responseObserver).onCompleted();
    }

    @Test
    void updateStaffAvailability_withOnlySlotAvailability_shouldOnlyUpdateSlot() {
        // Arrange
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;

        StaffAvailabilityDef staffAvailability = StaffAvailabilityDef.newBuilder()
                .setStaffId(staffId)
                .addSlotAvailabilityDayList(createSlotAvailabilityDay())
                .build();

        UpdateStaffAvailabilityRequest request = UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addStaffAvailabilityList(staffAvailability)
                .build();

        Map<String, StaffAvailabilitySlotDay> slotDayMap = new HashMap<>();
        when(staffAvailabilityDayHourService.getAvailabilitySlotDayMapByStaffIds(businessId, List.of(staffId)))
                .thenReturn(slotDayMap);
        doNothing().when(staffAvailabilityInitService).checkStaffAvailabilityWithInit(any(), any(), any());

        // Act
        server.updateStaffAvailability(request, responseObserver);

        // Assert
        verify(staffAvailabilityService).batchUpdate(any());
        verify(staffAvailabilityDayHourService)
                .updateStaffAvailabilitySlotDays(companyId, businessId, staffAvailability, slotDayMap);
        verify(responseObserver).onNext(UpdateStaffAvailabilityResponse.getDefaultInstance());
        verify(responseObserver).onCompleted();
    }

    // 辅助方法，创建测试数据
    private SlotAvailabilityDayDef createSlotAvailabilityDay() {
        return SlotAvailabilityDayDef.newBuilder()
                .setDayOfWeek(DayOfWeek.MONDAY)
                .setIsAvailable(true)
                .build();
    }

    private TimeAvailabilityDayDef createTimeAvailabilityDay() {
        return TimeAvailabilityDayDef.newBuilder()
                .setDayOfWeek(DayOfWeek.MONDAY)
                .setIsAvailable(true)
                .build();
    }
}

package com.moego.svc.online.booking.server;

import com.google.type.Date;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeDef;
import com.moego.idl.models.online_booking.v1.BookingRangeEndType;
import com.moego.idl.models.online_booking.v1.DateLimitType;
import com.moego.idl.models.online_booking.v1.DateRangeDef;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResponse;
import com.moego.idl.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResponse;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResponse;
import com.moego.idl.service.online_booking.v1.CreateCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.CreateCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.DeleteCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.DeleteCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.GetEvaluationServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetEvaluationServiceAvailabilityResponse;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityResponse;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingRequest;
import com.moego.idl.service.online_booking.v1.ListAcceptedCustomerSettingResponse;
import com.moego.idl.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest;
import com.moego.idl.service.online_booking.v1.ListArrivalPickUpTimeOverridesResponse;
import com.moego.idl.service.online_booking.v1.ListAvailableBookingTimeRangeRequest;
import com.moego.idl.service.online_booking.v1.ListAvailableBookingTimeRangeResponse;
import com.moego.idl.service.online_booking.v1.ListCapacityOverridesRequest;
import com.moego.idl.service.online_booking.v1.ListCapacityOverridesResponse;
import com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeRequest;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeResponse;
import com.moego.idl.service.online_booking.v1.QueryAvailablePetTypeRequest;
import com.moego.idl.service.online_booking.v1.QueryAvailablePetTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateAcceptedCustomerSettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateAcceptedCustomerSettingResponse;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.UpdateCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.UpdateCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingResponse;
import com.moego.idl.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.UpdateEvaluationServiceAvailabilityResponse;
import com.moego.idl.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingServiceAvailabilityResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IBookOnlineAcceptPetTypeService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.CompanyBusinessIdDTO;
import com.moego.svc.online.booking.entity.AcceptCustomerSetting;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.mapstruct.ArrivalPickUpTimeConverter;
import com.moego.svc.online.booking.mapstruct.LodgingCapacityConverter;
import com.moego.svc.online.booking.service.AvailabilitySettingService;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class ServiceAvailabilitySettingServer
        extends OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceImplBase {
    private final AvailabilitySettingService availabilitySettingService;
    // TODO: migrate grooming setting to new structure
    private final IBookOnlineAcceptPetTypeService bookOnlineAcceptPetTypeService;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final BusinessHelper businessHelper;

    @Override
    public void getBoardingServiceAvailabilitySetting(
            GetBoardingServiceAvailabilitySettingRequest request,
            StreamObserver<GetBoardingServiceAvailabilitySettingResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }
        var boardingServiceAvailabilityModel = availabilitySettingService.getBoardingServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = GetBoardingServiceAvailabilitySettingResponse.newBuilder()
                .setBoardingServiceAvailabilitySetting(boardingServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateBoardingServiceAvailabilitySetting(
            UpdateBoardingServiceAvailabilitySettingRequest request,
            StreamObserver<UpdateBoardingServiceAvailabilitySettingResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }

        availabilitySettingService.updateBoardingServiceAvailability(
                request.getTenant().getCompanyId(),
                request.getTenant().getBusinessId(),
                request.getStaffId(),
                request.getBoardingServiceAvailabilitySetting());

        var boardingServiceAvailabilityModel = availabilitySettingService.getBoardingServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = UpdateBoardingServiceAvailabilitySettingResponse.newBuilder()
                .setBoardingServiceAvailabilitySetting(boardingServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getDaycareServiceAvailabilitySetting(
            GetDaycareServiceAvailabilitySettingRequest request,
            StreamObserver<GetDaycareServiceAvailabilitySettingResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }
        var daycareServiceAvailabilityModel = availabilitySettingService.getDaycareServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = GetDaycareServiceAvailabilitySettingResponse.newBuilder()
                .setDaycareServiceAvailabilitySetting(daycareServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateDaycareServiceAvailabilitySetting(
            UpdateDaycareServiceAvailabilitySettingRequest request,
            StreamObserver<UpdateDaycareServiceAvailabilitySettingResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }

        availabilitySettingService.updateDaycareServiceAvailability(
                request.getTenant().getCompanyId(),
                request.getTenant().getBusinessId(),
                request.getStaffId(),
                request.getDaycareServiceAvailabilitySetting());

        var daycareServiceAvailabilityModel = availabilitySettingService.getDaycareServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = UpdateDaycareServiceAvailabilitySettingResponse.newBuilder()
                .setDaycareServiceAvailabilitySetting(daycareServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void queryAvailablePetType(
            QueryAvailablePetTypeRequest request, StreamObserver<QueryAvailablePetTypeResponse> responseObserver) {
        List<PetType> availablePetTypeList =
                switch (request.getServiceItemType()) {
                    case BOARDING -> availabilitySettingService
                            .getBoardingServiceAvailability(
                                    request.getTenant().getCompanyId(),
                                    request.getTenant().getBusinessId())
                            .getAcceptedPetTypesList();
                    case DAYCARE -> availabilitySettingService
                            .getDaycareServiceAvailability(
                                    request.getTenant().getCompanyId(),
                                    request.getTenant().getBusinessId())
                            .getAcceptedPetTypesList();
                    case GROOMING -> {
                        var petTypeAvailabilityMap =
                                bookOnlineAcceptPetTypeService.getAcceptPetType(new CompanyBusinessIdDTO(
                                        request.getTenant().getCompanyId(),
                                        Math.toIntExact(request.getTenant().getBusinessId())));
                        yield petTypeAvailabilityMap.petTypeAcceptMap().entrySet().stream()
                                .filter(Map.Entry::getValue)
                                .map(entry -> PetType.forNumber(entry.getKey()))
                                .toList();
                    }
                    case DOG_WALKING, GROUP_CLASS -> List.of(PetType.PET_TYPE_DOG); // only support dog
                    default -> List.of();
                };

        var response = QueryAvailablePetTypeResponse.newBuilder()
                .addAllAvailablePetType(availablePetTypeList)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void queryAvailableBookingDateRange(
            QueryAvailableBookingDateRangeRequest request,
            StreamObserver<QueryAvailableBookingDateRangeResponse> responseObserver) {

        var today = LocalDate.parse(
                businessHelper.getBusinessCurrentDate(request.getTenant().getBusinessId()));

        Date fromDate, endDate;
        switch (request.getServiceItemType()) {
            case BOARDING -> {
                var dateRange = availabilitySettingService
                        .getBoardingServiceAvailability(
                                request.getTenant().getCompanyId(),
                                request.getTenant().getBusinessId())
                        .getBookingDateRange();
                fromDate = getStartDate(dateRange, today);
                endDate = getEndDate(dateRange, today);
            }
            case DAYCARE -> {
                var dateRange = availabilitySettingService
                        .getDaycareServiceAvailability(
                                request.getTenant().getCompanyId(),
                                request.getTenant().getBusinessId())
                        .getBookingDateRange();
                fromDate = getStartDate(dateRange, today);
                endDate = getEndDate(dateRange, today);
            }
            case GROOMING -> {
                BookOnlineDTO obSetting = onlineBookingService.getOBSetting(
                        Math.toIntExact(request.getTenant().getBusinessId()));

                fromDate = ProtobufUtil.toProtobufDate(today.plusDays(obSetting.getBookingRangeStartOffset()));
                endDate =
                        switch (Objects.requireNonNull(BookingRangeEndType.forNumber(
                                obSetting.getBookingRangeEndType().intValue()))) {
                            case BOOKING_RANGE_END_TYPE_ABSOLUTE -> ProtobufUtil.toProtobufDate(
                                    obSetting.getBookingRangeEndDate());
                            case BOOKING_RANGE_END_TYPE_RELATIVE -> ProtobufUtil.toProtobufDate(
                                    today.plusDays(obSetting.getBookingRangeEndOffset()));
                            default -> throw ExceptionUtil.bizException(
                                    Code.CODE_PARAMS_ERROR, "Invalid booking range end type");
                        };
            }
            case EVALUATION -> {
                var dateRange = availabilitySettingService
                        .getEvaluationServiceAvailability(
                                request.getTenant().getCompanyId(),
                                request.getTenant().getBusinessId())
                        .getBookingDateRange();
                fromDate = getStartDate(dateRange, today);
                endDate = getEndDate(dateRange, today);
            }
            case DOG_WALKING -> {
                var dateRange = availabilitySettingService
                        .getDogWalkingServiceAvailability(
                                request.getTenant().getCompanyId(),
                                request.getTenant().getBusinessId())
                        .getBookingDateRange();
                fromDate = getStartDate(dateRange, today);
                endDate = getEndDate(dateRange, today);
            }
            default -> {
                fromDate = ProtobufUtil.toProtobufDate(today);
                endDate = ProtobufUtil.toProtobufDate(today);
            }
        }

        responseObserver.onNext(QueryAvailableBookingDateRangeResponse.newBuilder()
                .setFromDate(fromDate)
                .setToDate(endDate)
                .build());
        responseObserver.onCompleted();
    }

    private static Date getStartDate(DateRangeDef dateRange, LocalDate today) {
        if (dateRange.getStartDateType() == DateLimitType.DATE_TYPE_OFFSET) {
            return ProtobufUtil.toProtobufDate(today.plusDays(dateRange.getMaxStartDateOffset()));
        }
        return dateRange.getSpecificStartDate();
    }

    private static Date getEndDate(DateRangeDef dateRange, LocalDate today) {
        if (dateRange.getEndDateType() == DateLimitType.DATE_TYPE_OFFSET) {
            return ProtobufUtil.toProtobufDate(today.plusDays(dateRange.getMaxEndDateOffset()));
        }
        return dateRange.getSpecificEndDate();
    }

    @Override
    public void getEvaluationServiceAvailability(
            GetEvaluationServiceAvailabilityRequest request,
            StreamObserver<GetEvaluationServiceAvailabilityResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }
        var evaluationServiceAvailabilityModel = availabilitySettingService.getEvaluationServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = GetEvaluationServiceAvailabilityResponse.newBuilder()
                .setAvailability(evaluationServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateEvaluationServiceAvailability(
            UpdateEvaluationServiceAvailabilityRequest request,
            StreamObserver<UpdateEvaluationServiceAvailabilityResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }

        availabilitySettingService.updateEvaluationServiceAvailability(
                request.getTenant().getCompanyId(),
                request.getTenant().getBusinessId(),
                request.getStaffId(),
                request.getAvailability());

        var evaluationServiceAvailabilityModel = availabilitySettingService.getEvaluationServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var response = UpdateEvaluationServiceAvailabilityResponse.newBuilder()
                .setAvailability(evaluationServiceAvailabilityModel)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void getGroomingServiceAvailability(
            GetGroomingServiceAvailabilityRequest request,
            StreamObserver<GetGroomingServiceAvailabilityResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }
        var groomingServiceAvailabilityModel = availabilitySettingService.getGroomingServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var builder = GetGroomingServiceAvailabilityResponse.newBuilder();
        if (Objects.nonNull(groomingServiceAvailabilityModel)) {
            builder.setAvailability(groomingServiceAvailabilityModel);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateGroomingServiceAvailability(
            UpdateGroomingServiceAvailabilityRequest request,
            StreamObserver<UpdateGroomingServiceAvailabilityResponse> responseObserver) {
        if (!request.getTenant().hasBusinessId()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "businessId is required for online booking setting");
        }

        availabilitySettingService.updateGroomingServiceAvailability(
                request.getTenant().getCompanyId(),
                request.getTenant().getBusinessId(),
                request.getStaffId(),
                request.getAvailability());

        var groomingServiceAvailabilityModel = availabilitySettingService.getGroomingServiceAvailability(
                request.getTenant().getCompanyId(), request.getTenant().getBusinessId());
        var builder = UpdateGroomingServiceAvailabilityResponse.newBuilder();
        if (Objects.nonNull(groomingServiceAvailabilityModel)) {
            builder.setAvailability(groomingServiceAvailabilityModel);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * @deprecated 接口用来 client-api-v3 查询 ob time range setting，已经废弃
     * 相关逻辑已经转移到 OBAvailableServer.getAvailableDateTime
     * 2026-06-20 后，可以删除相关引用和逻辑，暂时为了上线兼容性，还需要保留并修复逻辑
     */
    @Deprecated(forRemoval = true)
    @Override
    public void listAvailableBookingTimeRange(
            ListAvailableBookingTimeRangeRequest request,
            StreamObserver<ListAvailableBookingTimeRangeResponse> responseObserver) {

        Map<ServiceItemType, ArrivalPickUpTimeDef> arrivalPickUpTimeRangeDefMap;
        if (CollectionUtils.isEmpty(request.getServiceItemTypeList())) {
            arrivalPickUpTimeRangeDefMap = Map.of();
        } else {
            arrivalPickUpTimeRangeDefMap = request.getServiceItemTypeList().stream()
                    .collect(Collectors.toMap(
                            Function.identity(),
                            serviceItemType -> availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(
                                    request.getCompanyId(), request.getBusinessId(), serviceItemType)));
        }

        var response = ListAvailableBookingTimeRangeResponse.newBuilder()
                .addAllTimeRanges(arrivalPickUpTimeRangeDefMap.entrySet().stream()
                        .map(entry -> ListAvailableBookingTimeRangeResponse.TimeRange.newBuilder()
                                .setServiceItemType(entry.getKey())
                                .setArrivalPickUpTimeRange(entry.getValue())
                                .build())
                        .toList())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void listAcceptedCustomerSetting(
            ListAcceptedCustomerSettingRequest request,
            StreamObserver<ListAcceptedCustomerSettingResponse> responseObserver) {
        var acceptCustomerSettingMap = new HashMap<>(availabilitySettingService.getAcceptCustomerSettingMap(
                request.getCompanyId(), request.getBusinessId(), request.getServiceItemTypesList()));
        // TODO 默认全开放，后续根据业务需求调整
        if (!acceptCustomerSettingMap.containsKey(ServiceItemType.GROUP_CLASS)) {
            var setting = new AcceptCustomerSetting();
            setting.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
            setting.setServiceItemType(ServiceItemType.GROUP_CLASS_VALUE);
            acceptCustomerSettingMap.put(ServiceItemType.GROUP_CLASS, setting);
        }

        var response = ListAcceptedCustomerSettingResponse.newBuilder()
                .addAllAcceptCustomerTypes(acceptCustomerSettingMap.entrySet().stream()
                        .map(entry -> ListAcceptedCustomerSettingResponse.AcceptCustomerType.newBuilder()
                                .setServiceItemType(entry.getKey())
                                .setAcceptCustomerType(AcceptCustomerType.forNumber(
                                        entry.getValue().getAcceptedCustomerType()))
                                .build())
                        .toList())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void updateAcceptedCustomerSetting(
            UpdateAcceptedCustomerSettingRequest request,
            StreamObserver<UpdateAcceptedCustomerSettingResponse> responseObserver) {
        availabilitySettingService.batchSaveAcceptCustomerSetting(
                request.getTenant().getCompanyId(),
                request.getTenant().getBusinessId(),
                request.getAcceptCustomerType(),
                request.getServiceItemTypesList(),
                request.getStaffId());

        responseObserver.onNext(UpdateAcceptedCustomerSettingResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void listArrivalPickUpTimeOverrides(
            ListArrivalPickUpTimeOverridesRequest request,
            StreamObserver<ListArrivalPickUpTimeOverridesResponse> responseObserver) {
        var allTimeRangeSetting = availabilitySettingService.getCompanyBookingTimeRangeSettings(
                request.getCompanyId(), request.getBusinessIdsList(), request.getServiceItemTypesList());
        // map serviceItemType by setting 每个 serviceItemType，只能取一个 settingId
        Map<Integer, List<BookingTimeRangeSetting>> settingMapByServiceItemType = allTimeRangeSetting.stream()
                .collect(Collectors.groupingBy(BookingTimeRangeSetting::getServiceItemType));

        // 每个 serviceItemType 取 setting id 最小的那个
        Map<Long, BookingTimeRangeSetting> settingsMap = new HashMap<>();
        settingMapByServiceItemType.values().forEach(values -> values.stream()
                .min(Comparator.comparingLong(BookingTimeRangeSetting::getId))
                .ifPresent(minSetting -> settingsMap.put(minSetting.getId(), minSetting)));

        // businessId to current date
        var businessIds = settingsMap.values().stream()
                .map(BookingTimeRangeSetting::getBusinessId)
                .toList();
        var overrides = availabilitySettingService.getArrivalPickUpOverrideBySettingIds(
                settingsMap.keySet().stream().toList());
        responseObserver.onNext(ListArrivalPickUpTimeOverridesResponse.newBuilder()
                .addAllOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(
                        overrides, settingsMap, businessHelper.batchGetBusinessDateTimeMap(businessIds)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchCreateArrivalPickUpTimeOverride(
            BatchCreateArrivalPickUpTimeOverrideRequest request,
            StreamObserver<BatchCreateArrivalPickUpTimeOverrideResponse> responseObserver) {
        List<BookingTimeRangeSetting> settings = availabilitySettingService.getCompanyBookingTimeRangeSettings(
                request.getCompanyId(), List.of(request.getBusinessId()), List.of(request.getServiceItemType()));
        if (CollectionUtils.isEmpty(settings)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "settingId for arrival time not found");
        }
        // business 下，一个 care type 对应一个 setting 配置。因此这里取第一条
        BookingTimeRangeSetting setting = settings.get(0);
        List<BookingTimeRangeOverride> overrides = new ArrayList<>();
        if (!CollectionUtils.isEmpty(request.getOverridesList())) {
            overrides.addAll(ArrivalPickUpTimeConverter.INSTANCE.buildBookingTimeRangeOverride(
                    setting.getId(), request.getOverridesList()));
        }
        availabilitySettingService.batchCreateArrivalPickUpOverrides(overrides);
        responseObserver.onNext(BatchCreateArrivalPickUpTimeOverrideResponse.newBuilder()
                .addAllOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(
                        overrides,
                        Map.of(setting.getId(), setting),
                        businessHelper.batchGetBusinessDateTimeMap(List.of(setting.getBusinessId()))))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteArrivalPickUpTimeOverride(
            BatchDeleteArrivalPickUpTimeOverrideRequest request,
            StreamObserver<BatchDeleteArrivalPickUpTimeOverrideResponse> responseObserver) {
        availabilitySettingService.batchDeleteArrivalPickUpOverrides(
                request.hasCompanyId() ? request.getCompanyId() : null, request.getIdsList());
        responseObserver.onNext(BatchDeleteArrivalPickUpTimeOverrideResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void batchUpdateArrivalPickUpTimeOverride(
            BatchUpdateArrivalPickUpTimeOverrideRequest request,
            StreamObserver<BatchUpdateArrivalPickUpTimeOverrideResponse> responseObserver) {
        if (CollectionUtils.isEmpty(request.getOverridesList())) {
            responseObserver.onNext(BatchUpdateArrivalPickUpTimeOverrideResponse.getDefaultInstance());
            responseObserver.onCompleted();
        }
        var toUpdates = ArrivalPickUpTimeConverter.INSTANCE.buildUpdateArrivalPickUpTimeOverrideRequest(
                request.getOverridesList());
        var result = availabilitySettingService.batchUpdateArrivalPickUpOverrides(
                request.hasCompanyId() ? request.getCompanyId() : null, toUpdates);

        Map<Long, BookingTimeRangeSetting> settingsMap = availabilitySettingService
                .getBookingTimeRangeSettingByIds(result.stream()
                        .map(BookingTimeRangeOverride::getSettingId)
                        .toList())
                .stream()
                .collect(Collectors.toMap(BookingTimeRangeSetting::getId, s -> s, (k1, k2) -> k2));

        var businessIds = settingsMap.values().stream()
                .map(BookingTimeRangeSetting::getBusinessId)
                .toList();
        responseObserver.onNext(BatchUpdateArrivalPickUpTimeOverrideResponse.newBuilder()
                .addAllOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(
                        result, settingsMap, businessHelper.batchGetBusinessDateTimeMap(businessIds)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listCapacityOverrides(
            ListCapacityOverridesRequest request, StreamObserver<ListCapacityOverridesResponse> responseObserver) {
        var capacityOverrides = availabilitySettingService.getCapacityOverrideList(
                request.getCompanyId(), request.getBusinessId(), request.getServiceItemType());

        responseObserver.onNext(ListCapacityOverridesResponse.newBuilder()
                .addAllCapacityOverrides(LodgingCapacityConverter.INSTANCE.toCapacityOverrideModels(
                        capacityOverrides, businessHelper.getBusinessCurrentDate(request.getBusinessId())))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void createCapacityOverride(
            CreateCapacityOverrideRequest request, StreamObserver<CreateCapacityOverrideResponse> responseObserver) {
        LodgingCapacitySetting lodgingCapacitySetting = availabilitySettingService.getLodgingCapacitySetting(
                request.getCompanyId(), request.getBusinessId(), request.getServiceItemType());
        var settingId = lodgingCapacitySetting.getId();
        availabilitySettingService.createCapacityOverride(
                LodgingCapacityConverter.INSTANCE.toLodgingCapacityOverrideEntity(
                        settingId, request.getCapacityOverride()));

        var capacityOverrides = availabilitySettingService.getCapacityOverrideList(
                request.getCompanyId(), request.getBusinessId(), request.getServiceItemType());

        responseObserver.onNext(CreateCapacityOverrideResponse.newBuilder()
                .addAllCapacityOverrides(LodgingCapacityConverter.INSTANCE.toCapacityOverrideModels(
                        capacityOverrides, businessHelper.getBusinessCurrentDate(request.getBusinessId())))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteCapacityOverride(
            DeleteCapacityOverrideRequest request, StreamObserver<DeleteCapacityOverrideResponse> responseObserver) {
        LodgingCapacitySetting lodgingCapacitySetting =
                availabilitySettingService.checkAndGetCapacitySettingByOverrideId(
                        request.getCompanyId(), request.getBusinessId(), request.getId());
        availabilitySettingService.deleteCapacityOverride(request.getId());

        var capacityOverrides = availabilitySettingService.getCapacityOverrideList(
                request.getCompanyId(),
                request.getBusinessId(),
                ServiceItemType.forNumber(lodgingCapacitySetting.getServiceItemType()));

        responseObserver.onNext(DeleteCapacityOverrideResponse.newBuilder()
                .addAllCapacityOverrides(LodgingCapacityConverter.INSTANCE.toCapacityOverrideModels(
                        capacityOverrides, businessHelper.getBusinessCurrentDate(request.getBusinessId())))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateCapacityOverride(
            UpdateCapacityOverrideRequest request, StreamObserver<UpdateCapacityOverrideResponse> responseObserver) {
        LodgingCapacitySetting lodgingCapacitySetting =
                availabilitySettingService.checkAndGetCapacitySettingByOverrideId(
                        request.getCompanyId(),
                        request.getBusinessId(),
                        request.getCapacityOverride().getId());
        availabilitySettingService.updateCapacityOverride(
                LodgingCapacityConverter.INSTANCE.toLodgingCapacityOverrideEntity(null, request.getCapacityOverride()));

        var capacityOverrides = availabilitySettingService.getCapacityOverrideList(
                request.getCompanyId(),
                request.getBusinessId(),
                ServiceItemType.forNumber(lodgingCapacitySetting.getServiceItemType()));

        responseObserver.onNext(UpdateCapacityOverrideResponse.newBuilder()
                .addAllCapacityOverrides(LodgingCapacityConverter.INSTANCE.toCapacityOverrideModels(
                        capacityOverrides, businessHelper.getBusinessCurrentDate(request.getBusinessId())))
                .build());
        responseObserver.onCompleted();
    }
}

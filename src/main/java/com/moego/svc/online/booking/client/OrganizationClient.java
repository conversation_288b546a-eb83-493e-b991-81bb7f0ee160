package com.moego.svc.online.booking.client;

import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.organization.v1.CloseDateDef;
import com.moego.idl.models.organization.v1.CompanyPreferenceSettingModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.TimeRangeDef;
import com.moego.idl.models.organization.v1.WorkingHoursDef;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetClosedDateListRequest;
import com.moego.idl.service.organization.v1.GetClosedHolidayListRequest;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.GetWorkingHourListRequest;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class OrganizationClient {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyService;

    public List<LocationBriefView> getLocationList(Long companyId) {
        return businessServiceBlockingStub
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .build())
                .getLocationList();
    }

    CompanyPreferenceSettingModel getCompanyPreferenceSetting(long companyId) {
        return companyService
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting();
    }

    public String getCompanyTimeZoneName(Long companyId) {
        return getCompanyPreferenceSetting(companyId).getTimeZone().getName();
    }

    public Map<DayOfWeek, List<TimeRangeDef>> getBusinessWorkingHour(Long companyId, Long businessId) {
        WorkingHoursDef workingHour = businessServiceBlockingStub
                .getWorkingHourList(GetWorkingHourListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setTokenCompanyId(companyId)
                        .build())
                .getWorkingHour();
        return Map.of(
                DayOfWeek.MONDAY, workingHour.getMondayList(),
                DayOfWeek.TUESDAY, workingHour.getTuesdayList(),
                DayOfWeek.WEDNESDAY, workingHour.getWednesdayList(),
                DayOfWeek.THURSDAY, workingHour.getThursdayList(),
                DayOfWeek.FRIDAY, workingHour.getFridayList(),
                DayOfWeek.SATURDAY, workingHour.getSaturdayList(),
                DayOfWeek.SUNDAY, workingHour.getSundayList());
    }

    public Map<LocalDate, List<DayTimeRangeDef>> getAvailableTimeRange(
            Long companyId, Long businessId, LocalDate startDate, LocalDate endDate) {
        Map<LocalDate, List<DayTimeRangeDef>> result = new HashMap<>();
        Map<DayOfWeek, List<TimeRangeDef>> weekTimeRange = getBusinessWorkingHour(companyId, businessId);
        for (LocalDate cur = startDate; !cur.isAfter(endDate); cur = cur.plusDays(1)) {
            List<DayTimeRangeDef> dayTimeRange = weekTimeRange.getOrDefault(cur.getDayOfWeek(), List.of()).stream()
                    .map(k -> DayTimeRangeDef.newBuilder()
                            .setStartTime(k.getStartTime())
                            .setEndTime(k.getEndTime())
                            .build())
                    .toList();
            result.put(cur, dayTimeRange);
        }
        return result;
    }

    public List<LocalDate> getBusinessClosedDatesWithinRange(
            Long companyId, Long businessId, LocalDate startDate, LocalDate endDate) {
        List<LocalDate> unavailableDates = getBusinessAllClosedDates(companyId, businessId);
        return unavailableDates.stream()
                .filter(date -> !date.isBefore(startDate) && !date.isAfter(endDate))
                .toList();
    }

    public List<LocalDate> getBusinessAllClosedDates(Long companyId, Long businessId) {
        // query closed date
        List<LocalDate> unavailableDates = new ArrayList<>();
        List<CloseDateDef> closeDateDefs = businessServiceBlockingStub
                .getClosedDateList(GetClosedDateListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setTokenCompanyId(companyId)
                        .build())
                .getClosedDateList();

        for (CloseDateDef closeDateDef : closeDateDefs) {
            unavailableDates.addAll(Stream.iterate(
                            LocalDate.parse(closeDateDef.getStartDate()),
                            date -> !date.isAfter(LocalDate.parse(closeDateDef.getEndDate())),
                            date -> date.plusDays(1))
                    .toList());
        }

        // query holiday
        var holidayDefs = businessServiceBlockingStub
                .getClosedHolidayList(GetClosedHolidayListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setTokenCompanyId(companyId)
                        .build())
                .getClosedDateList();

        // merge unavailable dates
        for (CloseDateDef holidayDef : holidayDefs) {
            unavailableDates.addAll(Stream.iterate(
                            LocalDate.parse(holidayDef.getStartDate()),
                            date -> !date.isAfter(LocalDate.parse(holidayDef.getEndDate())),
                            date -> date.plusDays(1))
                    .toList());
        }
        return unavailableDates;
    }
}

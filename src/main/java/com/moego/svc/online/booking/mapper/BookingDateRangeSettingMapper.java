package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingDateRangeSettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingDateRangeSetting;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingDateRangeSettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingDateRangeSettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, companyId, serviceItemType, startDateType, specificStartDate, maxStartDateOffset, endDateType, specificEndDate, maxEndDateOffset, createdAt, updatedAt, updatedBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingDateRangeSetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingDateRangeSetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingDateRangeSettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="start_date_type", property="startDateType", jdbcType=JdbcType.INTEGER),
        @Result(column="specific_start_date", property="specificStartDate", jdbcType=JdbcType.DATE),
        @Result(column="max_start_date_offset", property="maxStartDateOffset", jdbcType=JdbcType.INTEGER),
        @Result(column="end_date_type", property="endDateType", jdbcType=JdbcType.INTEGER),
        @Result(column="specific_end_date", property="specificEndDate", jdbcType=JdbcType.DATE),
        @Result(column="max_end_date_offset", property="maxEndDateOffset", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.BIGINT)
    })
    List<BookingDateRangeSetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingDateRangeSettingResult")
    Optional<BookingDateRangeSetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int insertMultiple(Collection<BookingDateRangeSetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingDateRangeSetting, c ->
            c.map(businessId).toProperty("businessId")
            .map(companyId).toProperty("companyId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(startDateType).toProperty("startDateType")
            .map(specificStartDate).toProperty("specificStartDate")
            .map(maxStartDateOffset).toProperty("maxStartDateOffset")
            .map(endDateType).toProperty("endDateType")
            .map(specificEndDate).toProperty("specificEndDate")
            .map(maxEndDateOffset).toProperty("maxEndDateOffset")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(updatedBy).toProperty("updatedBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int insertSelective(BookingDateRangeSetting row) {
        return MyBatis3Utils.insert(this::insert, row, bookingDateRangeSetting, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(startDateType).toPropertyWhenPresent("startDateType", row::getStartDateType)
            .map(specificStartDate).toPropertyWhenPresent("specificStartDate", row::getSpecificStartDate)
            .map(maxStartDateOffset).toPropertyWhenPresent("maxStartDateOffset", row::getMaxStartDateOffset)
            .map(endDateType).toPropertyWhenPresent("endDateType", row::getEndDateType)
            .map(specificEndDate).toPropertyWhenPresent("specificEndDate", row::getSpecificEndDate)
            .map(maxEndDateOffset).toPropertyWhenPresent("maxEndDateOffset", row::getMaxEndDateOffset)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", row::getUpdatedBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default Optional<BookingDateRangeSetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default List<BookingDateRangeSetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default List<BookingDateRangeSetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default Optional<BookingDateRangeSetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingDateRangeSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingDateRangeSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(companyId).equalTo(row::getCompanyId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(startDateType).equalTo(row::getStartDateType)
                .set(specificStartDate).equalTo(row::getSpecificStartDate)
                .set(maxStartDateOffset).equalTo(row::getMaxStartDateOffset)
                .set(endDateType).equalTo(row::getEndDateType)
                .set(specificEndDate).equalTo(row::getSpecificEndDate)
                .set(maxEndDateOffset).equalTo(row::getMaxEndDateOffset)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(updatedBy).equalTo(row::getUpdatedBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingDateRangeSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(startDateType).equalToWhenPresent(row::getStartDateType)
                .set(specificStartDate).equalToWhenPresent(row::getSpecificStartDate)
                .set(maxStartDateOffset).equalToWhenPresent(row::getMaxStartDateOffset)
                .set(endDateType).equalToWhenPresent(row::getEndDateType)
                .set(specificEndDate).equalToWhenPresent(row::getSpecificEndDate)
                .set(maxEndDateOffset).equalToWhenPresent(row::getMaxEndDateOffset)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(updatedBy).equalToWhenPresent(row::getUpdatedBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    default int updateByPrimaryKeySelective(BookingDateRangeSetting row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(startDateType).equalToWhenPresent(row::getStartDateType)
            .set(specificStartDate).equalToWhenPresent(row::getSpecificStartDate)
            .set(maxStartDateOffset).equalToWhenPresent(row::getMaxStartDateOffset)
            .set(endDateType).equalToWhenPresent(row::getEndDateType)
            .set(specificEndDate).equalToWhenPresent(row::getSpecificEndDate)
            .set(maxEndDateOffset).equalToWhenPresent(row::getMaxEndDateOffset)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}
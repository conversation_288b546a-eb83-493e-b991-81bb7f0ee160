package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.DaycareAddOnDetailDynamicSqlSupport.daycareAddOnDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.DaycareAddOnDetail;
import com.moego.svc.online.booking.mapper.DaycareAddOnDetailMapper;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class DaycareAddOnDetailService {

    private final DaycareAddOnDetailMapper daycareAddOnDetailMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public DaycareAddOnDetail get(long id) {
        return daycareAddOnDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(DaycareAddOnDetail entity) {

        if (entity.getQuantityPerDay() == null) {
            entity.setQuantityPerDay(1);
        }

        check(entity);

        daycareAddOnDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private static void check(DaycareAddOnDetail entity) {
        if (!CommonUtil.isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        }
        if (!CommonUtil.isNormal(entity.getServiceDetailId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (!CommonUtil.isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        }
        if (!CommonUtil.isNormal(entity.getAddOnId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "addOnId is required");
        }
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(DaycareAddOnDetail entity) {
        entity.setUpdatedAt(new Date());
        return daycareAddOnDetailMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return daycareAddOnDetailMapper.update(c -> c.set(daycareAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(daycareAddOnDetail.id, isEqualTo(id))
                .and(daycareAddOnDetail.deletedAt, isNull()));
    }

    /**
     * List daycare add-on detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId booking request id
     * @return daycare service detail id -> list of daycare add-on detail
     */
    public Map<Long, List<DaycareAddOnDetail>> listByBookingRequestId(long bookingRequestId) {
        return daycareAddOnDetailMapper
                .select(c -> c.where(daycareAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(daycareAddOnDetail.deletedAt, isNull()))
                .stream()
                .collect(Collectors.groupingBy(DaycareAddOnDetail::getServiceDetailId));
    }

    /**
     * List daycare add-on detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of booking request id
     * @return bookingRequestId -> daycareServiceDetailId -> list of daycare add-on detail
     */
    public Map<Long, Map<Long, List<DaycareAddOnDetail>>> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return daycareAddOnDetailMapper
                .select(c -> c.where(daycareAddOnDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(daycareAddOnDetail.deletedAt, isNull()))
                .stream()
                .collect(Collectors.groupingBy(
                        DaycareAddOnDetail::getBookingRequestId,
                        Collectors.groupingBy(DaycareAddOnDetail::getServiceDetailId)));
    }
}

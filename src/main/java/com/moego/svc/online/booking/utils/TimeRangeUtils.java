package com.moego.svc.online.booking.utils;

import com.google.common.collect.ImmutableMap;
import com.moego.common.utils.DateUtil;
import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.ScheduleType;
import com.moego.idl.models.online_booking.v1.TimeRangeDef;
import com.moego.svc.online.booking.dto.UsedLocalTimeDTO;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class TimeRangeUtils {

    public static final byte WEEK_PERIOD = 7;

    public static final Map<Integer, Function<TimeRangeDef, DayOfWeekTimeRangeDef>> WEEK_FOR_GET = ImmutableMap.of(
            0, k -> k.hasFirstWeek() ? k.getFirstWeek() : null,
            1, k -> k.hasSecondWeek() ? k.getSecondWeek() : null,
            2, k -> k.hasThirdWeek() ? k.getThirdWeek() : null,
            3, k -> k.hasForthWeek() ? k.getForthWeek() : null);

    public static final Map<Integer, BiConsumer<TimeRangeDef.Builder, DayOfWeekTimeRangeDef>> WEEK_FOR_SET =
            ImmutableMap.of(
                    0,
                            (k, v) -> {
                                if (v != null) k.setFirstWeek(v);
                            },
                    1,
                            (k, v) -> {
                                if (v != null) k.setSecondWeek(v);
                            },
                    2,
                            (k, v) -> {
                                if (v != null) k.setThirdWeek(v);
                            },
                    3,
                            (k, v) -> {
                                if (v != null) k.setForthWeek(v);
                            });

    public static final Map<Integer, Function<DayOfWeekTimeRangeDef, List<DayTimeRangeDef>>> DAY_OF_WEEK_FOR_GET =
            ImmutableMap.of(
                    0,
                    DayOfWeekTimeRangeDef::getSundayList,
                    1,
                    DayOfWeekTimeRangeDef::getMondayList,
                    2,
                    DayOfWeekTimeRangeDef::getTuesdayList,
                    3,
                    DayOfWeekTimeRangeDef::getWednesdayList,
                    4,
                    DayOfWeekTimeRangeDef::getThursdayList,
                    5,
                    DayOfWeekTimeRangeDef::getFridayList,
                    6,
                    DayOfWeekTimeRangeDef::getSaturdayList);

    public static final Map<Integer, BiConsumer<DayOfWeekTimeRangeDef.Builder, List<DayTimeRangeDef>>>
            DAY_OF_WEEK_FOR_ADD_ALL = ImmutableMap.of(
                    0,
                    DayOfWeekTimeRangeDef.Builder::addAllSunday,
                    1,
                    DayOfWeekTimeRangeDef.Builder::addAllMonday,
                    2,
                    DayOfWeekTimeRangeDef.Builder::addAllTuesday,
                    3,
                    DayOfWeekTimeRangeDef.Builder::addAllWednesday,
                    4,
                    DayOfWeekTimeRangeDef.Builder::addAllThursday,
                    5,
                    DayOfWeekTimeRangeDef.Builder::addAllFriday,
                    6,
                    DayOfWeekTimeRangeDef.Builder::addAllSaturday);

    public static List<DayTimeRangeDef> getTimeRangeByDate(
            TimeRangeDef timeRangeDef, ScheduleType scheduleType, LocalDate scheduleStartDate, LocalDate curDate) {
        if (curDate.isBefore(scheduleStartDate)) {
            return List.of();
        }
        int dayOfWeek = DateUtil.localDateToWeek(curDate);

        // 计算 startDate 所在星期的星期日，也就是绝对开始时间
        LocalDate absoluteStartDate = scheduleStartDate.minusDays(DateUtil.localDateToWeek(scheduleStartDate));
        // 计算 date 距离 absoluteStartDate 的天数差
        long daysBetween = ChronoUnit.DAYS.between(absoluteStartDate, curDate);
        // 计算 date 是距离 absoluteStartDate 的星期差
        long weeksBetween = Math.floorDiv(daysBetween, WEEK_PERIOD);
        // 计算循环中第几周，0 是第一周，1 是第二周，以此类推
        int weekNumber = Math.toIntExact(Math.floorMod(weeksBetween, scheduleType.getNumber()));

        var weekFunction = WEEK_FOR_GET.get(weekNumber);
        var dayFunction = DAY_OF_WEEK_FOR_GET.get(dayOfWeek);
        if (weekFunction == null || dayFunction == null) {
            return List.of();
        }

        DayOfWeekTimeRangeDef dayOfWeekTimeRangeDef = weekFunction.apply(timeRangeDef);
        if (dayOfWeekTimeRangeDef == null) {
            return List.of();
        }
        List<DayTimeRangeDef> workingHour = dayFunction.apply(dayOfWeekTimeRangeDef);
        return workingHour == null ? List.of() : workingHour;
    }

    /**
     * 在 [start,end] 时间段内 ，结合已有预约、ob request 过滤出仍有空余的时间段
     * 按 appointment 和 ob request 的开始时间计算时间段占用情况
     */
    public static Map<LocalDate, List<DayTimeRangeDef>> getAvailableTimeRange(
            Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday,
            List<UsedLocalTimeDTO> existAppointments,
            List<UsedLocalTimeDTO> existBookingRequests,
            LocalDate start,
            LocalDate end) {

        Map<LocalDate, List<UsedLocalTimeDTO>> dayAppointments =
                existAppointments.stream().collect(Collectors.groupingBy(UsedLocalTimeDTO::startDate));
        Map<LocalDate, List<UsedLocalTimeDTO>> dayBookingRequests =
                existBookingRequests.stream().collect(Collectors.groupingBy(UsedLocalTimeDTO::startDate));

        return start.datesUntil(end.plusDays(1)).collect(Collectors.toMap(cur -> cur, cur -> {
            List<UsedLocalTimeDTO> appointmentsForDay = dayAppointments.getOrDefault(cur, List.of());
            List<UsedLocalTimeDTO> bookingRequestsForDay = dayBookingRequests.getOrDefault(cur, List.of());

            return getAvailableTimeRangeInDay(
                    appointmentsForDay, bookingRequestsForDay, timeRangeForEveryday.getOrDefault(cur, List.of()));
        }));
    }

    /**
     * 过滤出一天中仍有空余的时间段
     */
    /*private*/ static List<DayTimeRangeDef> getAvailableTimeRangeInDay(
            List<UsedLocalTimeDTO> appointments,
            List<UsedLocalTimeDTO> bookingRequests,
            List<DayTimeRangeDef> timeRanges) {

        List<DayTimeRangeDef> updatedRanges = new ArrayList<>();

        timeRanges.forEach(def -> {
            if (!def.hasPetCapacity()) {
                updatedRanges.add(def); // 不限制容量的直接加入
                return;
            }
            long petCapacity = def.getPetCapacity();
            long usedByAppointments = appointments.stream()
                    .filter(appointment -> isStartTimeInRange(def, appointment.startTime()))
                    .count();
            long usedByRequests = bookingRequests.stream()
                    .filter(request -> isStartTimeInRange(def, request.startTime()))
                    .count();
            long remaining = petCapacity - usedByAppointments - usedByRequests;
            if (remaining > 0) {
                // 创建一个副本，修改 petCapacity 为剩余容量
                DayTimeRangeDef newDef =
                        def.toBuilder().setPetCapacity((int) remaining).build();
                updatedRanges.add(newDef);
            }
        });

        return updatedRanges;
    }

    /*private*/ static boolean isStartTimeInRange(DayTimeRangeDef timeRangeDef, int startTime) {
        // 一定要单独判断 == ，有一种情况是：startTime 和 def.startTime,def.endTime 都相等，用下面的 return 无法返回这种情况
        if (startTime == timeRangeDef.getStartTime()) {
            return true;
        }
        return startTime > timeRangeDef.getStartTime() && startTime < timeRangeDef.getEndTime();
    }
}

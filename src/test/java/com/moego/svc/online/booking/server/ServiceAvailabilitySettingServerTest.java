package com.moego.svc.online.booking.server;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.client.MetadataClient;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.service.AvailabilitySettingService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ServiceAvailabilitySettingServerTest {

    @Mock
    private MetadataClient metadataClient;

    @Mock
    private AvailabilitySettingService availabilitySettingService;

    @Mock
    private BusinessHelper businessHelper;

    @InjectMocks
    private ServiceAvailabilitySettingServer serviceAvailabilitySettingServer;

    @Test
    void listArrivalPickUpTimeOverrides_withValidRequest_shouldReturnOverrides() {
        List<Long> businessIds = List.of(1L, 2L);
        List<ServiceItemType> serviceItemTypes = List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE);
        ListArrivalPickUpTimeOverridesRequest request = ListArrivalPickUpTimeOverridesRequest.newBuilder()
                .setCompanyId(123L)
                .addAllBusinessIds(businessIds)
                .addAllServiceItemTypes(serviceItemTypes)
                .build();

        when(availabilitySettingService.getCompanyBookingTimeRangeSettings(123L, businessIds, serviceItemTypes))
                .thenReturn(List.of());

        when(availabilitySettingService.getArrivalPickUpOverrideBySettingIds(anyList()))
                .thenReturn(List.of());
        when(businessHelper.batchGetBusinessDateTimeMap(anyList())).thenReturn(Map.of());

        var responseObserver = mock(StreamObserver.class);
        serviceAvailabilitySettingServer.listArrivalPickUpTimeOverrides(request, responseObserver);
    }

    @Test
    void batchCreateArrivalPickUpTimeOverride_withEmptySettings_shouldThrowException() {
        BatchCreateArrivalPickUpTimeOverrideRequest request = BatchCreateArrivalPickUpTimeOverrideRequest.newBuilder()
                .setCompanyId(123L)
                .setBusinessId(1L)
                .setServiceItemType(ServiceItemType.BOARDING)
                .build();

        when(availabilitySettingService.getCompanyBookingTimeRangeSettings(
                        123L, List.of(1L), List.of(ServiceItemType.BOARDING)))
                .thenReturn(List.of());

        var responseObserver = mock(StreamObserver.class);
        assertThrows(
                ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "settingId for arrival time not found")
                        .getClass(),
                () -> serviceAvailabilitySettingServer.batchCreateArrivalPickUpTimeOverride(request, responseObserver));
    }

    @Test
    void batchDeleteArrivalPickUpTimeOverride_withValidRequest_shouldDeleteOverrides() {
        BatchDeleteArrivalPickUpTimeOverrideRequest request = BatchDeleteArrivalPickUpTimeOverrideRequest.newBuilder()
                .setCompanyId(123L)
                .addIds(1L)
                .addIds(2L)
                .build();
        doNothing().when(availabilitySettingService).batchDeleteArrivalPickUpOverrides(123L, List.of(1L, 2L));

        serviceAvailabilitySettingServer.batchDeleteArrivalPickUpTimeOverride(request, mock(StreamObserver.class));
    }

    @Test
    void batchDeleteArrivalPickUpTimeOverride_withEmptyIds_shouldReturnEmptyResponse() {
        BatchDeleteArrivalPickUpTimeOverrideRequest request = BatchDeleteArrivalPickUpTimeOverrideRequest.newBuilder()
                .setCompanyId(123L)
                .build();

        serviceAvailabilitySettingServer.batchDeleteArrivalPickUpTimeOverride(request, mock(StreamObserver.class));
    }
}

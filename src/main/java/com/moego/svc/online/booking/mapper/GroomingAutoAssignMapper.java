package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.GroomingAutoAssignDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.GroomingAutoAssign;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface GroomingAutoAssignMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<GroomingAutoAssignMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, staffId, startTime, createdAt, updatedAt, deletedAt, groomingServiceDetailId);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<GroomingAutoAssign> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<GroomingAutoAssign> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="GroomingAutoAssignResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="grooming_service_detail_id", property="groomingServiceDetailId", jdbcType=JdbcType.BIGINT)
    })
    List<GroomingAutoAssign> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("GroomingAutoAssignResult")
    Optional<GroomingAutoAssign> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int insertMultiple(Collection<GroomingAutoAssign> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, groomingAutoAssign, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(staffId).toProperty("staffId")
            .map(startTime).toProperty("startTime")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(groomingServiceDetailId).toProperty("groomingServiceDetailId")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int insertSelective(GroomingAutoAssign row) {
        return MyBatis3Utils.insert(this::insert, row, groomingAutoAssign, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(groomingServiceDetailId).toPropertyWhenPresent("groomingServiceDetailId", row::getGroomingServiceDetailId)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default Optional<GroomingAutoAssign> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default List<GroomingAutoAssign> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default List<GroomingAutoAssign> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default Optional<GroomingAutoAssign> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, groomingAutoAssign, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    static UpdateDSL<UpdateModel> updateAllColumns(GroomingAutoAssign row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(staffId).equalTo(row::getStaffId)
                .set(startTime).equalTo(row::getStartTime)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(groomingServiceDetailId).equalTo(row::getGroomingServiceDetailId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(GroomingAutoAssign row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(groomingServiceDetailId).equalToWhenPresent(row::getGroomingServiceDetailId);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: grooming_auto_assign")
    default int updateByPrimaryKeySelective(GroomingAutoAssign row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(groomingServiceDetailId).equalToWhenPresent(row::getGroomingServiceDetailId)
            .where(id, isEqualTo(row::getId))
        );
    }
}
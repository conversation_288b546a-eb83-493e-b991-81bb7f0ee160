package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static com.moego.svc.online.booking.mapper.GroomingAddOnDetailDynamicSqlSupport.groomingAddOnDetail;
import static com.moego.svc.online.booking.mapper.GroomingAutoAssignDynamicSqlSupport.groomingAutoAssign;
import static com.moego.svc.online.booking.mapper.GroomingAutoAssignMapper.updateSelectiveColumns;
import static com.moego.svc.online.booking.mapper.GroomingServiceDetailDynamicSqlSupport.groomingServiceDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.api.IOBService;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.BookOnlineSubmitParams;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.entity.GroomingAutoAssign;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.helper.PetHelper;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.GroomingAddOnDetailMapper;
import com.moego.svc.online.booking.mapper.GroomingAutoAssignMapper;
import com.moego.svc.online.booking.mapper.GroomingServiceDetailMapper;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class GroomingAutoAssignService {

    private final GroomingAutoAssignMapper groomingAutoAssignMapper;
    private final GroomingServiceDetailMapper groomingServiceDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final IOBService obApi;
    private final GroomingAddOnDetailMapper groomingAddOnDetailMapper;
    private final PetHelper petHelper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public GroomingAutoAssign get(long id) {
        return groomingAutoAssignMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(GroomingAutoAssign entity) {
        groomingAutoAssignMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(GroomingAutoAssign entity) {
        entity.setUpdatedAt(new Date());
        return groomingAutoAssignMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Update a record by booking request id and grooming service detail id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int updateByGroomingServiceDetailId(GroomingAutoAssign entity) {
        entity.setUpdatedAt(new Date());
        return groomingAutoAssignMapper.update(c -> updateSelectiveColumns(entity, c)
                .where(groomingAutoAssign.bookingRequestId, isEqualTo(entity.getBookingRequestId()))
                .and(groomingAutoAssign.groomingServiceDetailId, isEqualTo(entity.getGroomingServiceDetailId()))
                .and(groomingAutoAssign.deletedAt, isNull()));
    }

    /**
     * Insert or Update a record by booking request id and grooming service detail id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int upsertByGroomingServiceDetailId(GroomingAutoAssign entity) {
        Optional<GroomingAutoAssign> existingRecord = groomingAutoAssignMapper.selectOne(
                c -> c.where(groomingAutoAssign.bookingRequestId, isEqualTo(entity.getBookingRequestId()))
                        .and(groomingAutoAssign.groomingServiceDetailId, isEqualTo(entity.getGroomingServiceDetailId()))
                        .and(groomingAutoAssign.deletedAt, isNull()));
        if (existingRecord.isPresent()) {
            entity.setId(existingRecord.get().getId());
            return this.updateByGroomingServiceDetailId(entity);
        } else {
            return groomingAutoAssignMapper.insertSelective(entity);
        }
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return groomingAutoAssignMapper.update(c -> c.set(groomingAutoAssign.deletedAt)
                .equalTo(new Date())
                .where(groomingAutoAssign.id, isEqualTo(id))
                .and(groomingAutoAssign.deletedAt, isNull()));
    }

    public GroomingAutoAssign getByBookingRequestId(long bookingRequestId) {
        return groomingAutoAssignMapper
                .selectOne(c -> c.where(groomingAutoAssign.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(groomingAutoAssign.deletedAt, isNull()))
                .orElse(null);
    }

    public Map<Long, GroomingAutoAssign> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return groomingAutoAssignMapper
                .select(c -> c.where(groomingAutoAssign.bookingRequestId, isIn(bookingRequestIds))
                        .and(groomingAutoAssign.deletedAt, isNull()))
                .stream()
                .collect(Collectors.toMap(GroomingAutoAssign::getBookingRequestId, Function.identity(), (a, b) -> a));
    }

    /**
     * Auto assign staff and time to the specified {@link GroomingServiceDetail}.
     *
     * @param groomingServiceDetailId groomingServiceDetailId
     * @return assigned GroomingAutoAssign, or null if no need to assign
     */
    @Nullable
    public GroomingAutoAssign assign(long groomingServiceDetailId) {
        var serviceDetail = getGroomingServiceDetail(groomingServiceDetailId);
        if (serviceDetail == null) {
            return null;
        }

        if (isNormal(serviceDetail.getStaffId()) && serviceDetail.getStartTime() != null) {
            // 已经分配过了
            return null;
        }

        var autoAssignResult = obApi.doAutoAssign(buildBookOnlineSubmitParams(serviceDetail));
        if (autoAssignResult == null) {
            return null;
        }

        var groomingAutoAssign = new GroomingAutoAssign();
        if (isNormal(autoAssignResult.staffId())) {
            groomingAutoAssign.setStaffId(Long.valueOf(autoAssignResult.staffId()));
        }
        if (autoAssignResult.appointmentTime() != null) {
            groomingAutoAssign.setStartTime(autoAssignResult.appointmentTime());
        }

        return groomingAutoAssign;
    }

    private BookOnlineSubmitParams buildBookOnlineSubmitParams(GroomingServiceDetail serviceDetail) {

        var bookingRequest = mustGetBookingRequest(serviceDetail.getBookingRequestId());

        var bookOnlineSubmitParams = new BookOnlineSubmitParams();
        bookOnlineSubmitParams.setBusinessId(Math.toIntExact(bookingRequest.getBusinessId()));
        bookOnlineSubmitParams.setCompanyId(bookingRequest.getCompanyId());
        bookOnlineSubmitParams.setCustomerData(buildCustomerData(bookingRequest.getCustomerId()));
        bookOnlineSubmitParams.setPetData(buildPetData(serviceDetail));
        bookOnlineSubmitParams.setAppointmentDate(serviceDetail.getStartDate());
        if (serviceDetail.getStartTime() != null) {
            bookOnlineSubmitParams.setAppointmentStartTime(serviceDetail.getStartTime());
            bookOnlineSubmitParams.setNoStartTime(false);
        } else {
            bookOnlineSubmitParams.setNoStartTime(true);
        }
        if (isNormal(serviceDetail.getStaffId())) {
            bookOnlineSubmitParams.setStaffId(Math.toIntExact(serviceDetail.getStaffId()));
        }
        bookOnlineSubmitParams.setEndDate(serviceDetail.getEndDate());

        return bookOnlineSubmitParams;
    }

    private List<BookOnlinePetParams> buildPetData(GroomingServiceDetail serviceDetail) {

        var pet = buildBookOnlinePetParams(petHelper.mustGetPet(serviceDetail.getPetId()));

        pet.setServiceId(Math.toIntExact(serviceDetail.getServiceId()));
        pet.setAddons(buildBookOnlinePetParamsAddon(serviceDetail.getId()));
        pet.setStartDate(serviceDetail.getStartDate());
        pet.setEndDate(serviceDetail.getEndDate());

        return List.of(pet);
    }

    private static BookOnlinePetParams buildBookOnlinePetParams(BusinessCustomerPetInfoModel petInfo) {
        var pet = new BookOnlinePetParams();
        pet.setPetId((int) petInfo.getId());
        pet.setAvatarPath(petInfo.getAvatarPath());
        pet.setPetName(petInfo.getPetName());
        pet.setBreed(petInfo.getBreed());
        pet.setPetTypeId(petInfo.getPetTypeValue());
        pet.setGender((byte) petInfo.getGenderValue());
        pet.setWeight(petInfo.getWeight());
        pet.setFixed(petInfo.getFixed());
        pet.setBehavior(petInfo.getBehavior());
        pet.setIsSelected(true); // 在 grooming_service_detail 表里，肯定是选中的
        return pet;
    }

    private List<BookOnlinePetParams.Addon> buildBookOnlinePetParamsAddon(long serviceDetailId) {
        return listGroomingAddonDetail(serviceDetailId).stream()
                .map(e -> {
                    var addon = new BookOnlinePetParams.Addon();
                    addon.setId(Math.toIntExact(e.getAddOnId()));
                    return addon;
                })
                .toList();
    }

    private List<GroomingAddOnDetail> listGroomingAddonDetail(long serviceDetailId) {
        return groomingAddOnDetailMapper.select(
                c -> c.where(groomingAddOnDetail.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(groomingServiceDetail.deletedAt, isNull()));
    }

    private static BookOnlineCustomerParams buildCustomerData(long customerId) {
        var bookOnlineCustomerParams = new BookOnlineCustomerParams();
        bookOnlineCustomerParams.setCustomerId(Math.toIntExact(customerId));
        return bookOnlineCustomerParams;
    }

    private BookingRequest mustGetBookingRequest(long bookingRequestId) {
        return bookingRequestMapper
                .selectOne(c ->
                        c.where(bookingRequest.id, isEqualTo(bookingRequestId)).and(bookingRequest.deletedAt, isNull()))
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + bookingRequestId));
    }

    @Nullable
    private GroomingServiceDetail getGroomingServiceDetail(long groomingServiceDetailId) {
        return groomingServiceDetailMapper
                .selectOne(c -> c.where(groomingServiceDetail.id, isEqualTo(groomingServiceDetailId))
                        .and(groomingServiceDetail.deletedAt, isNull()))
                .orElse(null);
    }
}

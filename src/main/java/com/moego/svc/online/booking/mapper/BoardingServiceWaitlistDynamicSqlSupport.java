package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BoardingServiceWaitlistDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_waitlist")
    public static final BoardingServiceWaitlist boardingServiceWaitlist = new BoardingServiceWaitlist();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.id")
    public static final SqlColumn<Long> id = boardingServiceWaitlist.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = boardingServiceWaitlist.bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = boardingServiceWaitlist.serviceDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.start_date")
    public static final SqlColumn<LocalDate> startDate = boardingServiceWaitlist.startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.end_date")
    public static final SqlColumn<LocalDate> endDate = boardingServiceWaitlist.endDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = boardingServiceWaitlist.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = boardingServiceWaitlist.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_service_waitlist.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = boardingServiceWaitlist.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_service_waitlist")
    public static final class BoardingServiceWaitlist extends AliasableSqlTable<BoardingServiceWaitlist> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<LocalDate> endDate = column("end_date", JDBCType.DATE);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public BoardingServiceWaitlist() {
            super("boarding_service_waitlist", BoardingServiceWaitlist::new);
        }
    }
}
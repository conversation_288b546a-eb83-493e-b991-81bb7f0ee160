package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DaycareServiceWaitlistModel;
import com.moego.svc.online.booking.entity.DaycareServiceWaitlist;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class})
public interface DaycareServiceWaitlistConverter {
    DaycareServiceWaitlistConverter INSTANCE = Mappers.getMapper(DaycareServiceWaitlistConverter.class);

    DaycareServiceWaitlistModel entityToModel(DaycareServiceWaitlist entity);
}

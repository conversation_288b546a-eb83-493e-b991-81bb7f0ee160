package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class StaffAvailabilitySlotDayDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    public static final StaffAvailabilitySlotDay staffAvailabilitySlotDay = new StaffAvailabilitySlotDay();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.id")
    public static final SqlColumn<Long> id = staffAvailabilitySlotDay.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.company_id")
    public static final SqlColumn<Long> companyId = staffAvailabilitySlotDay.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.business_id")
    public static final SqlColumn<Long> businessId = staffAvailabilitySlotDay.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.staff_id")
    public static final SqlColumn<Long> staffId = staffAvailabilitySlotDay.staffId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.day_of_week")
    public static final SqlColumn<Integer> dayOfWeek = staffAvailabilitySlotDay.dayOfWeek;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.is_available")
    public static final SqlColumn<Boolean> isAvailable = staffAvailabilitySlotDay.isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.start_time")
    public static final SqlColumn<Integer> startTime = staffAvailabilitySlotDay.startTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.end_time")
    public static final SqlColumn<Integer> endTime = staffAvailabilitySlotDay.endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.capacity")
    public static final SqlColumn<Integer> capacity = staffAvailabilitySlotDay.capacity;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.limit_ids")
    public static final SqlColumn<List<Long>> limitIds = staffAvailabilitySlotDay.limitIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.created_at")
    public static final SqlColumn<Date> createdAt = staffAvailabilitySlotDay.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability_slot_day.updated_at")
    public static final SqlColumn<Date> updatedAt = staffAvailabilitySlotDay.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability_slot_day")
    public static final class StaffAvailabilitySlotDay extends AliasableSqlTable<StaffAvailabilitySlotDay> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> dayOfWeek = column("day_of_week", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isAvailable = column("is_available", JDBCType.BIT);

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> capacity = column("capacity", JDBCType.INTEGER);

        public final SqlColumn<List<Long>> limitIds = column("limit_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public StaffAvailabilitySlotDay() {
            super("staff_availability_slot_day", StaffAvailabilitySlotDay::new);
        }
    }
}
package com.moego.svc.online.booking.server;

import static com.moego.idl.service.online_booking.v1.BoardingAutoAssignServiceGrpc.BoardingAutoAssignServiceImplBase;
import static com.moego.svc.online.booking.mapper.BoardingAutoAssignDynamicSqlSupport.boardingAutoAssign;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.online_booking.v1.BoardingAutoAssignModel;
import com.moego.idl.service.online_booking.v1.GetBoardingAutoAssignByServiceDetailIdRequest;
import com.moego.idl.service.online_booking.v1.GetBoardingAutoAssignByServiceDetailIdResponse;
import com.moego.idl.service.online_booking.v1.ListBoardingAutoAssignByBookingRequestIdsRequest;
import com.moego.idl.service.online_booking.v1.ListBoardingAutoAssignByBookingRequestIdsResponse;
import com.moego.idl.service.online_booking.v1.ListBoardingAutoAssignByServiceDetailIdsRequest;
import com.moego.idl.service.online_booking.v1.ListBoardingAutoAssignByServiceDetailIdsResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.mapper.BoardingAutoAssignMapper;
import com.moego.svc.online.booking.mapstruct.BoardingAutoAssignConverter;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@GrpcService
@RequiredArgsConstructor
public class BoardingAutoAssignServer extends BoardingAutoAssignServiceImplBase {

    private final BoardingAutoAssignMapper boardingAutoAssignMapper;

    @Override
    public void listBoardingAutoAssignByBookingRequestIds(
            ListBoardingAutoAssignByBookingRequestIdsRequest request,
            StreamObserver<ListBoardingAutoAssignByBookingRequestIdsResponse> responseObserver) {
        List<Long> bookingRequestIds = request.getBookingRequestIdsList();

        List<BoardingAutoAssignModel> models = !ObjectUtils.isEmpty(bookingRequestIds)
                ? boardingAutoAssignMapper
                        .select(c -> c.where(boardingAutoAssign.bookingRequestId, isIn(bookingRequestIds))
                                .and(boardingAutoAssign.deletedAt, isNull()))
                        .stream()
                        .map(BoardingAutoAssignConverter.INSTANCE::entityToModel)
                        .toList()
                : List.of();

        responseObserver.onNext(ListBoardingAutoAssignByBookingRequestIdsResponse.newBuilder()
                .addAllRecords(models)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getBoardingAutoAssignByServiceDetailId(
            GetBoardingAutoAssignByServiceDetailIdRequest request,
            StreamObserver<GetBoardingAutoAssignByServiceDetailIdResponse> responseObserver) {
        GetBoardingAutoAssignByServiceDetailIdResponse.Builder builder =
                GetBoardingAutoAssignByServiceDetailIdResponse.newBuilder();
        boardingAutoAssignMapper
                .selectOne(c -> c.where(
                                boardingAutoAssign.boardingServiceDetailId, isEqualTo(request.getServiceDetailId()))
                        .and(boardingAutoAssign.deletedAt, isNull()))
                .map(BoardingAutoAssignConverter.INSTANCE::entityToModel)
                .ifPresent(builder::setRecord);

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void listBoardingAutoAssignByServiceDetailIds(
            ListBoardingAutoAssignByServiceDetailIdsRequest request,
            StreamObserver<ListBoardingAutoAssignByServiceDetailIdsResponse> responseObserver) {
        List<Long> serviceDetailIds = request.getServiceDetailIdsList();

        List<BoardingAutoAssignModel> models = !ObjectUtils.isEmpty(serviceDetailIds)
                ? boardingAutoAssignMapper
                        .select(c -> c.where(boardingAutoAssign.boardingServiceDetailId, isIn(serviceDetailIds))
                                .and(boardingAutoAssign.deletedAt, isNull()))
                        .stream()
                        .map(BoardingAutoAssignConverter.INSTANCE::entityToModel)
                        .toList()
                : List.of();

        responseObserver.onNext(ListBoardingAutoAssignByServiceDetailIdsResponse.newBuilder()
                .addAllRecords(models)
                .build());
        responseObserver.onCompleted();
    }
}

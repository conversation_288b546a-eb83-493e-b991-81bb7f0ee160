package com.moego.svc.online.booking.service;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.svc.online.booking.entity.StaffSlotFreeService;
import com.moego.svc.online.booking.mapper.StaffSlotFreeServiceDynamicSqlSupport;
import com.moego.svc.online.booking.mapper.StaffSlotFreeServiceMapper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class StaffSlotFreeServiceService {

    private final StaffSlotFreeServiceMapper staffSlotFreeServiceMapper;

    /**
     * 根据员工ID列表查询相关的服务记录
     *
     */
    public List<StaffSlotFreeService> getByStaffIds(Long businessId, List<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return List.of();
        }

        return staffSlotFreeServiceMapper.select(
                c -> c.where(StaffSlotFreeServiceDynamicSqlSupport.businessId, isEqualTo(businessId))
                        .and(StaffSlotFreeServiceDynamicSqlSupport.staffId, isIn(staffIds)));
    }

    /**
     * 删除指定员工的现有记录并插入新记录
     *
     */
    @Transactional
    public int deleteAndInsert(Long companyId, Long businessId, Long staffId, List<Long> serviceIds) {
        int insertedCount = 0;

        // 删除现有记录
        staffSlotFreeServiceMapper.delete(
                c -> c.where(StaffSlotFreeServiceDynamicSqlSupport.businessId, isEqualTo(businessId))
                        .and(StaffSlotFreeServiceDynamicSqlSupport.staffId, isEqualTo(staffId)));

        // 批量插入新记录
        if (!CollectionUtils.isEmpty(serviceIds)) {
            for (Long serviceId : serviceIds) {
                var staffSlotFreeServiceRecord = new StaffSlotFreeService();
                staffSlotFreeServiceRecord.setCompanyId(companyId);
                staffSlotFreeServiceRecord.setBusinessId(businessId);
                staffSlotFreeServiceRecord.setStaffId(staffId);
                staffSlotFreeServiceRecord.setServiceId(serviceId);
                staffSlotFreeServiceMapper.insertSelective(staffSlotFreeServiceRecord);
                insertedCount++;
            }
        }

        return insertedCount;
    }
}

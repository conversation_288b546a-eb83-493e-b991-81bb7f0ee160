package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.FeedingDynamicSqlSupport.feeding;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.svc.online.booking.entity.Feeding;
import com.moego.svc.online.booking.mapper.FeedingMapper;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class FeedingService {

    private final FeedingMapper feedingMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public Feeding get(long id) {
        return feedingMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(Feeding entity) {
        feedingMapper.insertSelective(entity);
        return entity.getId();
    }

    @Transactional
    public int insertMultiple(List<Feeding> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        records.forEach(feedingMapper::insertSelective);
        return records.size();
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(Feeding entity) {
        entity.setUpdatedAt(new Date());
        return feedingMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return feedingMapper.update(c -> c.set(feeding.deletedAt)
                .equalTo(new Date())
                .where(feeding.id, isEqualTo(id))
                .and(feeding.deletedAt, isNull()));
    }

    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return feedingMapper.update(c -> c.set(feeding.deletedAt)
                .equalTo(new Date())
                .where(feeding.id, isIn(ids))
                .and(feeding.deletedAt, isNull()));
    }

    /**
     * Get Feeding by serviceDetailId, not include deleted record.
     *
     * @param serviceDetailId serviceDetailId
     * @return Feeding
     */
    @Nullable
    public Feeding getByServiceDetailId(long serviceDetailId) {
        return feedingMapper
                .selectOne(c -> c.where(feeding.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(feeding.deletedAt, isNull()))
                .orElse(null);
    }

    /**
     * List Feeding by booking request id, not include deleted record.
     *
     * @param bookingRequestId booking request id
     * @return list of Feeding
     */
    public List<Feeding> listByBookingRequestId(long bookingRequestId) {
        return feedingMapper.select(c ->
                c.where(feeding.bookingRequestId, isEqualTo(bookingRequestId)).and(feeding.deletedAt, isNull()));
    }

    private static void addFieldToNote(StringJoiner noteBuilder, String label, String value) {
        if (StringUtils.hasText(value)) {
            noteBuilder.add(String.format("%s: %s", label, value));
        }
    }

    /**
     * List Feeding by booking request id, not include deleted record.
     *
     * @param bookingRequestIds list of booking request id
     * @param serviceItemType  service item type
     * @return bookingRequestId -> serviceDetailId -> List<Feeding>
     */
    public Map<Long, Map<Long, List<Feeding>>> listByBookingRequestId(
            List<Long> bookingRequestIds, Integer serviceItemType) {
        if (CollectionUtils.isEmpty(bookingRequestIds) || serviceItemType == null) {
            return Map.of();
        }
        return feedingMapper
                .select(c -> c.where(feeding.bookingRequestId, isIn(bookingRequestIds))
                        .and(feeding.deletedAt, isNull())
                        .and(feeding.serviceDetailType, isEqualTo(serviceItemType)))
                .stream()
                .collect(Collectors.groupingBy(
                        Feeding::getBookingRequestId, Collectors.groupingBy(Feeding::getServiceDetailId)));
    }
}

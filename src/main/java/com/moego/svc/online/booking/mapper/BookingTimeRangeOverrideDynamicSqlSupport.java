package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingTimeRangeOverrideDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    public static final BookingTimeRangeOverride bookingTimeRangeOverride = new BookingTimeRangeOverride();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.id")
    public static final SqlColumn<Long> id = bookingTimeRangeOverride.id;

    /**
     * Database Column Remarks:
     *   The id of booking time range setting
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.setting_id")
    public static final SqlColumn<Long> settingId = bookingTimeRangeOverride.settingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.start_date")
    public static final SqlColumn<LocalDate> startDate = bookingTimeRangeOverride.startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.end_date")
    public static final SqlColumn<LocalDate> endDate = bookingTimeRangeOverride.endDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = bookingTimeRangeOverride.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = bookingTimeRangeOverride.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.is_available")
    public static final SqlColumn<Boolean> isAvailable = bookingTimeRangeOverride.isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.day_time_range")
    public static final SqlColumn<String> dayTimeRange = bookingTimeRangeOverride.dayTimeRange;

    /**
     * Database Column Remarks:
     *   1.arrival time 2.pick up time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.time_range_type")
    public static final SqlColumn<Integer> timeRangeType = bookingTimeRangeOverride.timeRangeType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_override.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = bookingTimeRangeOverride.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_override")
    public static final class BookingTimeRangeOverride extends AliasableSqlTable<BookingTimeRangeOverride> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> settingId = column("setting_id", JDBCType.BIGINT);

        public final SqlColumn<LocalDate> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<LocalDate> endDate = column("end_date", JDBCType.DATE);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> isAvailable = column("is_available", JDBCType.BIT);

        public final SqlColumn<String> dayTimeRange = column("day_time_range", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Integer> timeRangeType = column("time_range_type", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public BookingTimeRangeOverride() {
            super("booking_time_range_override", BookingTimeRangeOverride::new);
        }
    }
}
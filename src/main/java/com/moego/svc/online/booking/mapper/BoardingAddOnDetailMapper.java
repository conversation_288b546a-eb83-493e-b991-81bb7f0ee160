package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BoardingAddOnDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import com.moego.svc.online.booking.typehandler.BoardingAddOnDetailSpecificDatesTypeHandler;
import com.moego.svc.online.booking.typehandler.PetDetailDateTypeTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BoardingAddOnDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BoardingAddOnDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, serviceDetailId, petId, addOnId, specificDates, isEveryday, servicePrice, taxId, duration, createdAt, updatedAt, deletedAt, quantityPerDay, dateType, startDate);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BoardingAddOnDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BoardingAddOnDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BoardingAddOnDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_detail_id", property="serviceDetailId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="add_on_id", property="addOnId", jdbcType=JdbcType.BIGINT),
        @Result(column="specific_dates", property="specificDates", typeHandler=BoardingAddOnDetailSpecificDatesTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="is_everyday", property="isEveryday", jdbcType=JdbcType.BIT),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="tax_id", property="taxId", jdbcType=JdbcType.BIGINT),
        @Result(column="duration", property="duration", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="quantity_per_day", property="quantityPerDay", jdbcType=JdbcType.INTEGER),
        @Result(column="date_type", property="dateType", typeHandler=PetDetailDateTypeTypeHandler.class, jdbcType=JdbcType.INTEGER),
        @Result(column="start_date", property="startDate", jdbcType=JdbcType.DATE)
    })
    List<BoardingAddOnDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BoardingAddOnDetailResult")
    Optional<BoardingAddOnDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int insertMultiple(Collection<BoardingAddOnDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, boardingAddOnDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(serviceDetailId).toProperty("serviceDetailId")
            .map(petId).toProperty("petId")
            .map(addOnId).toProperty("addOnId")
            .map(specificDates).toProperty("specificDates")
            .map(isEveryday).toProperty("isEveryday")
            .map(servicePrice).toProperty("servicePrice")
            .map(taxId).toProperty("taxId")
            .map(duration).toProperty("duration")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
            .map(quantityPerDay).toProperty("quantityPerDay")
            .map(dateType).toProperty("dateType")
            .map(startDate).toProperty("startDate")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int insertSelective(BoardingAddOnDetail row) {
        return MyBatis3Utils.insert(this::insert, row, boardingAddOnDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(serviceDetailId).toPropertyWhenPresent("serviceDetailId", row::getServiceDetailId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(addOnId).toPropertyWhenPresent("addOnId", row::getAddOnId)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
            .map(isEveryday).toPropertyWhenPresent("isEveryday", row::getIsEveryday)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(taxId).toPropertyWhenPresent("taxId", row::getTaxId)
            .map(duration).toPropertyWhenPresent("duration", row::getDuration)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
            .map(quantityPerDay).toPropertyWhenPresent("quantityPerDay", row::getQuantityPerDay)
            .map(dateType).toPropertyWhenPresent("dateType", row::getDateType)
            .map(startDate).toPropertyWhenPresent("startDate", row::getStartDate)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default Optional<BoardingAddOnDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default List<BoardingAddOnDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default List<BoardingAddOnDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default Optional<BoardingAddOnDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, boardingAddOnDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(BoardingAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(serviceDetailId).equalTo(row::getServiceDetailId)
                .set(petId).equalTo(row::getPetId)
                .set(addOnId).equalTo(row::getAddOnId)
                .set(specificDates).equalTo(row::getSpecificDates)
                .set(isEveryday).equalTo(row::getIsEveryday)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(taxId).equalTo(row::getTaxId)
                .set(duration).equalTo(row::getDuration)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt)
                .set(quantityPerDay).equalTo(row::getQuantityPerDay)
                .set(dateType).equalTo(row::getDateType)
                .set(startDate).equalTo(row::getStartDate);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BoardingAddOnDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(addOnId).equalToWhenPresent(row::getAddOnId)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates)
                .set(isEveryday).equalToWhenPresent(row::getIsEveryday)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(taxId).equalToWhenPresent(row::getTaxId)
                .set(duration).equalToWhenPresent(row::getDuration)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
                .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay)
                .set(dateType).equalToWhenPresent(row::getDateType)
                .set(startDate).equalToWhenPresent(row::getStartDate);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_add_on_detail")
    default int updateByPrimaryKeySelective(BoardingAddOnDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(serviceDetailId).equalToWhenPresent(row::getServiceDetailId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(addOnId).equalToWhenPresent(row::getAddOnId)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .set(isEveryday).equalToWhenPresent(row::getIsEveryday)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(taxId).equalToWhenPresent(row::getTaxId)
            .set(duration).equalToWhenPresent(row::getDuration)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .set(quantityPerDay).equalToWhenPresent(row::getQuantityPerDay)
            .set(dateType).equalToWhenPresent(row::getDateType)
            .set(startDate).equalToWhenPresent(row::getStartDate)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.entity;

import com.moego.idl.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange;
import com.moego.idl.models.online_booking.v1.CapacityOverrideUnitType;
import jakarta.annotation.Generated;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table lodging_capacity_override
 */
public class LodgingCapacityOverride {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of lodging_capacity_setting
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.setting_id")
    private Long settingId;

    /**
     * Database Column Remarks:
     *   array of date range json
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.date_ranges")
    private List<CapacityDateRange> dateRanges;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.capacity")
    private Integer capacity;

    /**
     * Database Column Remarks:
     *   1.pet number  2.percent
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.unit_type")
    private CapacityOverrideUnitType unitType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.setting_id")
    public Long getSettingId() {
        return settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.setting_id")
    public void setSettingId(Long settingId) {
        this.settingId = settingId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.date_ranges")
    public List<CapacityDateRange> getDateRanges() {
        return dateRanges;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.date_ranges")
    public void setDateRanges(List<CapacityDateRange> dateRanges) {
        this.dateRanges = dateRanges;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.capacity")
    public Integer getCapacity() {
        return capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.capacity")
    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.unit_type")
    public CapacityOverrideUnitType getUnitType() {
        return unitType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.unit_type")
    public void setUnitType(CapacityOverrideUnitType unitType) {
        this.unitType = unitType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: lodging_capacity_override.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", settingId=").append(settingId);
        sb.append(", dateRanges=").append(dateRanges);
        sb.append(", capacity=").append(capacity);
        sb.append(", unitType=").append(unitType);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        LodgingCapacityOverride other = (LodgingCapacityOverride) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSettingId() == null ? other.getSettingId() == null : this.getSettingId().equals(other.getSettingId()))
            && (this.getDateRanges() == null ? other.getDateRanges() == null : this.getDateRanges().equals(other.getDateRanges()))
            && (this.getCapacity() == null ? other.getCapacity() == null : this.getCapacity().equals(other.getCapacity()))
            && (this.getUnitType() == null ? other.getUnitType() == null : this.getUnitType().equals(other.getUnitType()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_override")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSettingId() == null) ? 0 : getSettingId().hashCode());
        result = prime * result + ((getDateRanges() == null) ? 0 : getDateRanges().hashCode());
        result = prime * result + ((getCapacity() == null) ? 0 : getCapacity().hashCode());
        result = prime * result + ((getUnitType() == null) ? 0 : getUnitType().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
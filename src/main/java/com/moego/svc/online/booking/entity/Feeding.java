package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Database Table Remarks:
 *   Stores information about feedings.
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table feeding
 */
public class Feeding {
    /**
     * Database Column Remarks:
     *   The primary key identifier for each feeding.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The booking request identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The service detail identifier.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_id")
    private Long serviceDetailId;

    /**
     * Database Column Remarks:
     *   service detail type, 1: boarding, 2: daycare
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_type")
    private Integer serviceDetailType;

    /**
     * Database Column Remarks:
     *   Feeding time.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.time")
    private String time;

    /**
     * Database Column Remarks:
     *   Feeding amount, must be greater than 0.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount")
    private BigDecimal amount;

    /**
     * Database Column Remarks:
     *   Feeding unit.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.unit")
    private String unit;

    /**
     * Database Column Remarks:
     *   Food type.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_type")
    private String foodType;

    /**
     * Database Column Remarks:
     *   Food source.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_source")
    private String foodSource;

    /**
     * Database Column Remarks:
     *   Feeding instructions.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.instruction")
    private String instruction;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.deleted_at")
    private Date deletedAt;

    /**
     * Database Column Remarks:
     *   Additional notes about the feeding.
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.note")
    private String note;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount_str")
    private String amountStr;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_id")
    public Long getServiceDetailId() {
        return serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_id")
    public void setServiceDetailId(Long serviceDetailId) {
        this.serviceDetailId = serviceDetailId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_type")
    public Integer getServiceDetailType() {
        return serviceDetailType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.service_detail_type")
    public void setServiceDetailType(Integer serviceDetailType) {
        this.serviceDetailType = serviceDetailType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.time")
    public String getTime() {
        return time;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.time")
    public void setTime(String time) {
        this.time = time;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount")
    public BigDecimal getAmount() {
        return amount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount")
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.unit")
    public String getUnit() {
        return unit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.unit")
    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_type")
    public String getFoodType() {
        return foodType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_type")
    public void setFoodType(String foodType) {
        this.foodType = foodType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_source")
    public String getFoodSource() {
        return foodSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.food_source")
    public void setFoodSource(String foodSource) {
        this.foodSource = foodSource;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.instruction")
    public String getInstruction() {
        return instruction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.instruction")
    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.note")
    public String getNote() {
        return note;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.note")
    public void setNote(String note) {
        this.note = note;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount_str")
    public String getAmountStr() {
        return amountStr;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: feeding.amount_str")
    public void setAmountStr(String amountStr) {
        this.amountStr = amountStr;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", serviceDetailId=").append(serviceDetailId);
        sb.append(", serviceDetailType=").append(serviceDetailType);
        sb.append(", time=").append(time);
        sb.append(", amount=").append(amount);
        sb.append(", unit=").append(unit);
        sb.append(", foodType=").append(foodType);
        sb.append(", foodSource=").append(foodSource);
        sb.append(", instruction=").append(instruction);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append(", note=").append(note);
        sb.append(", amountStr=").append(amountStr);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        Feeding other = (Feeding) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getServiceDetailId() == null ? other.getServiceDetailId() == null : this.getServiceDetailId().equals(other.getServiceDetailId()))
            && (this.getServiceDetailType() == null ? other.getServiceDetailType() == null : this.getServiceDetailType().equals(other.getServiceDetailType()))
            && (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getFoodType() == null ? other.getFoodType() == null : this.getFoodType().equals(other.getFoodType()))
            && (this.getFoodSource() == null ? other.getFoodSource() == null : this.getFoodSource().equals(other.getFoodSource()))
            && (this.getInstruction() == null ? other.getInstruction() == null : this.getInstruction().equals(other.getInstruction()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()))
            && (this.getNote() == null ? other.getNote() == null : this.getNote().equals(other.getNote()))
            && (this.getAmountStr() == null ? other.getAmountStr() == null : this.getAmountStr().equals(other.getAmountStr()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: feeding")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getServiceDetailId() == null) ? 0 : getServiceDetailId().hashCode());
        result = prime * result + ((getServiceDetailType() == null) ? 0 : getServiceDetailType().hashCode());
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getFoodType() == null) ? 0 : getFoodType().hashCode());
        result = prime * result + ((getFoodSource() == null) ? 0 : getFoodSource().hashCode());
        result = prime * result + ((getInstruction() == null) ? 0 : getInstruction().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
        result = prime * result + ((getAmountStr() == null) ? 0 : getAmountStr().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Database Table Remarks:
 *   The daycare service detail
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table daycare_service_detail
 */
public class DaycareServiceDetail {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.booking_request_id")
    private Long bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.pet_id")
    private Long petId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_id")
    private Long serviceId;

    /**
     * Database Column Remarks:
     *   The specific dates of the daycare service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.specific_dates")
    private String specificDates;

    /**
     * Database Column Remarks:
     *   The price of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_price")
    private BigDecimal servicePrice;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.tax_id")
    private Long taxId;

    /**
     * Database Column Remarks:
     *   The max duration of the daycare service, unit minute
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.max_duration")
    private Integer maxDuration;

    /**
     * Database Column Remarks:
     *   The arrival time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.start_time")
    private Integer startTime;

    /**
     * Database Column Remarks:
     *   The pickup time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.end_time")
    private Integer endTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.deleted_at")
    private Date deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.booking_request_id")
    public Long getBookingRequestId() {
        return bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.booking_request_id")
    public void setBookingRequestId(Long bookingRequestId) {
        this.bookingRequestId = bookingRequestId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.pet_id")
    public Long getPetId() {
        return petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.pet_id")
    public void setPetId(Long petId) {
        this.petId = petId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_id")
    public Long getServiceId() {
        return serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_id")
    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.specific_dates")
    public String getSpecificDates() {
        return specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.specific_dates")
    public void setSpecificDates(String specificDates) {
        this.specificDates = specificDates;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_price")
    public BigDecimal getServicePrice() {
        return servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.service_price")
    public void setServicePrice(BigDecimal servicePrice) {
        this.servicePrice = servicePrice;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.tax_id")
    public Long getTaxId() {
        return taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.tax_id")
    public void setTaxId(Long taxId) {
        this.taxId = taxId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.max_duration")
    public Integer getMaxDuration() {
        return maxDuration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.max_duration")
    public void setMaxDuration(Integer maxDuration) {
        this.maxDuration = maxDuration;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.start_time")
    public Integer getStartTime() {
        return startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.start_time")
    public void setStartTime(Integer startTime) {
        this.startTime = startTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.end_time")
    public Integer getEndTime() {
        return endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.end_time")
    public void setEndTime(Integer endTime) {
        this.endTime = endTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.deleted_at")
    public Date getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_detail.deleted_at")
    public void setDeletedAt(Date deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", bookingRequestId=").append(bookingRequestId);
        sb.append(", petId=").append(petId);
        sb.append(", serviceId=").append(serviceId);
        sb.append(", specificDates=").append(specificDates);
        sb.append(", servicePrice=").append(servicePrice);
        sb.append(", taxId=").append(taxId);
        sb.append(", maxDuration=").append(maxDuration);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        DaycareServiceDetail other = (DaycareServiceDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBookingRequestId() == null ? other.getBookingRequestId() == null : this.getBookingRequestId().equals(other.getBookingRequestId()))
            && (this.getPetId() == null ? other.getPetId() == null : this.getPetId().equals(other.getPetId()))
            && (this.getServiceId() == null ? other.getServiceId() == null : this.getServiceId().equals(other.getServiceId()))
            && (this.getSpecificDates() == null ? other.getSpecificDates() == null : this.getSpecificDates().equals(other.getSpecificDates()))
            && (this.getServicePrice() == null ? other.getServicePrice() == null : this.getServicePrice().equals(other.getServicePrice()))
            && (this.getTaxId() == null ? other.getTaxId() == null : this.getTaxId().equals(other.getTaxId()))
            && (this.getMaxDuration() == null ? other.getMaxDuration() == null : this.getMaxDuration().equals(other.getMaxDuration()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_detail")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBookingRequestId() == null) ? 0 : getBookingRequestId().hashCode());
        result = prime * result + ((getPetId() == null) ? 0 : getPetId().hashCode());
        result = prime * result + ((getServiceId() == null) ? 0 : getServiceId().hashCode());
        result = prime * result + ((getSpecificDates() == null) ? 0 : getSpecificDates().hashCode());
        result = prime * result + ((getServicePrice() == null) ? 0 : getServicePrice().hashCode());
        result = prime * result + ((getTaxId() == null) ? 0 : getTaxId().hashCode());
        result = prime * result + ((getMaxDuration() == null) ? 0 : getMaxDuration().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
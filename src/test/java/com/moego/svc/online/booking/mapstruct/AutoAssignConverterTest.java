package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class AutoAssignConverterTest {

    private AutoAssignConverter converter;

    @BeforeEach
    void setUp() {
        converter = AutoAssignConverter.INSTANCE;
    }

    @Test
    void testBuildAssignRequire_SingleModel() {
        BoardingServiceDetailModel model = BoardingServiceDetailModel.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .setStartDate("2023-06-01")
                .setEndDate("2023-06-05")
                .build();

        GetAutoAssignResponse.AssignRequire result = converter.buildAssignRequire(model);

        assertNotNull(result);
        assertEquals(1L, result.getPetId());
        assertEquals(2L, result.getServiceId());
        assertEquals("2023-06-01", result.getStartDate());
        assertEquals("2023-06-05", result.getEndDate());
    }

    @Test
    void testBuildAssignRequire_ListOfModels() {
        BoardingServiceDetailModel model1 = BoardingServiceDetailModel.newBuilder()
                .setPetId(1L)
                .setServiceId(2L)
                .setStartDate("2023-06-01")
                .setEndDate("2023-06-05")
                .build();
        BoardingServiceDetailModel model2 = BoardingServiceDetailModel.newBuilder()
                .setPetId(3L)
                .setServiceId(4L)
                .setStartDate("2023-06-10")
                .setEndDate("2023-06-15")
                .build();

        List<BoardingServiceDetailModel> models = Arrays.asList(model1, model2);

        List<GetAutoAssignResponse.AssignRequire> results =
                converter.buildAssignRequire(models, Collections.emptyList());

        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals(1L, results.get(0).getPetId());
        assertEquals(3L, results.get(1).getPetId());
    }

    @Test
    void testBuildAssignRequire_EmptyList() {
        List<GetAutoAssignResponse.AssignRequire> results =
                converter.buildAssignRequire(Collections.emptyList(), Collections.emptyList());

        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testBuildAutoAssignLodgingDetail_SingleLodging() {
        Long lodgingUnitId = 1L;
        Map<Long, LodgingUnitModel> lodgingUnitMap = new HashMap<>();
        Map<Long, LodgingTypeModel> lodgingTypeMap = new HashMap<>();

        LodgingUnitModel lodgingUnit = LodgingUnitModel.newBuilder()
                .setId(1L)
                .setName("Unit 1")
                .setLodgingTypeId(10L)
                .build();
        lodgingUnitMap.put(1L, lodgingUnit);

        LodgingTypeModel lodgingType =
                LodgingTypeModel.newBuilder().setId(10L).setName("Type A").build();
        lodgingTypeMap.put(10L, lodgingType);

        GetAutoAssignResponse.LodgingDetail result =
                converter.buildAutoAssignLodgingDetail(lodgingUnitId, lodgingUnitMap, lodgingTypeMap);

        assertNotNull(result);
        assertEquals(1L, result.getLodgingId());
        assertEquals("Unit 1", result.getLodgingUnitName());
        assertEquals("Type A", result.getLodgingTypeName());
    }

    @Test
    void testBuildAutoAssignLodgingDetail_ListOfLodgings() {
        List<Long> lodgingUnitIds = Arrays.asList(1L, 2L);
        Map<Long, LodgingUnitModel> lodgingUnitMap = new HashMap<>();
        Map<Long, LodgingTypeModel> lodgingTypeMap = new HashMap<>();

        LodgingUnitModel lodgingUnit1 = LodgingUnitModel.newBuilder()
                .setId(1L)
                .setName("Unit 1")
                .setLodgingTypeId(10L)
                .build();
        LodgingUnitModel lodgingUnit2 = LodgingUnitModel.newBuilder()
                .setId(2L)
                .setName("Unit 2")
                .setLodgingTypeId(20L)
                .build();
        lodgingUnitMap.put(1L, lodgingUnit1);
        lodgingUnitMap.put(2L, lodgingUnit2);

        LodgingTypeModel lodgingType1 =
                LodgingTypeModel.newBuilder().setId(10L).setName("Type A").build();
        LodgingTypeModel lodgingType2 =
                LodgingTypeModel.newBuilder().setId(20L).setName("Type B").build();
        lodgingTypeMap.put(10L, lodgingType1);
        lodgingTypeMap.put(20L, lodgingType2);

        List<GetAutoAssignResponse.LodgingDetail> results =
                converter.buildAutoAssignLodgingDetail(lodgingUnitIds, lodgingUnitMap, lodgingTypeMap);

        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals(1L, results.get(0).getLodgingId());
        assertEquals("Unit 1", results.get(0).getLodgingUnitName());
        assertEquals("Type A", results.get(0).getLodgingTypeName());
        assertEquals(2L, results.get(1).getLodgingId());
        assertEquals("Unit 2", results.get(1).getLodgingUnitName());
        assertEquals("Type B", results.get(1).getLodgingTypeName());
    }

    @Test
    void testBuildAutoAssignLodgingDetail_EmptyList() {
        List<GetAutoAssignResponse.LodgingDetail> results =
                converter.buildAutoAssignLodgingDetail(Collections.emptyList(), new HashMap<>(), new HashMap<>());

        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testBuildAutoAssignLodgingDetail_MissingLodgingUnit() {
        Long lodgingUnitId = 1L;
        Map<Long, LodgingUnitModel> lodgingUnitMap = new HashMap<>();
        Map<Long, LodgingTypeModel> lodgingTypeMap = new HashMap<>();

        GetAutoAssignResponse.LodgingDetail result =
                converter.buildAutoAssignLodgingDetail(lodgingUnitId, lodgingUnitMap, lodgingTypeMap);

        assertNotNull(result);
        assertEquals(1L, result.getLodgingId());
        assertEquals("", result.getLodgingUnitName());
        assertEquals("", result.getLodgingTypeName());
    }

    @Test
    void testBuildAutoAssignLodgingDetail_MissingLodgingType() {
        Long lodgingUnitId = 1L;
        Map<Long, LodgingUnitModel> lodgingUnitMap = new HashMap<>();
        Map<Long, LodgingTypeModel> lodgingTypeMap = new HashMap<>();

        LodgingUnitModel lodgingUnit = LodgingUnitModel.newBuilder()
                .setId(1L)
                .setName("Unit 1")
                .setLodgingTypeId(10L)
                .build();
        lodgingUnitMap.put(1L, lodgingUnit);

        GetAutoAssignResponse.LodgingDetail result =
                converter.buildAutoAssignLodgingDetail(lodgingUnitId, lodgingUnitMap, lodgingTypeMap);

        assertNotNull(result);
        assertEquals(1L, result.getLodgingId());
        assertEquals("Unit 1", result.getLodgingUnitName());
        assertEquals("", result.getLodgingTypeName());
    }

    @Test
    void evaluationToAssignRequire_ListOfModels() {
        var model1 = EvaluationTestDetailModel.newBuilder()
                .setPetId(1L)
                .setEvaluationId(2L)
                .build();
        var model2 = EvaluationTestDetailModel.newBuilder()
                .setPetId(3L)
                .setEvaluationId(4L)
                .build();

        var evaluations = Map.of(
                2L,
                EvaluationBriefView.newBuilder()
                        .setId(2L)
                        .setAllowStaffAutoAssign(true)
                        .build(),
                4L,
                EvaluationBriefView.newBuilder()
                        .setId(4L)
                        .setAllowStaffAutoAssign(true)
                        .build());

        var results = converter.evaluationToAssignRequire(List.of(model1, model2), evaluations);

        var expect = List.of(
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(1L)
                        .setServiceId(2L)
                        .build(),
                GetAutoAssignResponse.AssignRequire.newBuilder()
                        .setPetId(3L)
                        .setServiceId(4L)
                        .build());

        assertThat(results).isEqualTo(expect);
    }

    @Test
    void evaluationToAssignRequire_EmptyList() {
        var results = converter.evaluationToAssignRequire(Collections.emptyList(), Map.of());

        assertThat(results).isEmpty();
    }
}

package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.svc.online.booking.mapper.DogWalkingServiceDetailDynamicSqlSupport.dogWalkingServiceDetail;
import static com.moego.svc.online.booking.mapper.DogWalkingServiceDetailMapper.updateSelectiveColumns;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.DogWalkingServiceDetail;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.DogWalkingServiceDetailMapper;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class DogWalkingServiceDetailService {

    private final DogWalkingServiceDetailMapper serviceDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final ServiceHelper serviceHelper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public DogWalkingServiceDetail get(long id) {
        return serviceDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(DogWalkingServiceDetail entity) {

        populate(entity);

        serviceDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private void populate(DogWalkingServiceDetail entity) {

        check(entity);

        var bookingRequest = bookingRequestMapper
                .selectByPrimaryKey(entity.getBookingRequestId())
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + entity.getBookingRequestId()));

        var builder = MustGetCustomizedServiceParam.builder();
        builder.serviceId(entity.getServiceId());
        builder.companyId(bookingRequest.getCompanyId());
        builder.businessId(bookingRequest.getBusinessId());
        builder.petId(entity.getPetId());
        if (isNormal(entity.getStaffId())) {
            builder.staffId(entity.getStaffId());
        }

        var service = serviceHelper.mustGetCustomizedService(builder.build());

        if (entity.getServiceTime() == null) {
            entity.setServiceTime(service.getDuration());
        }
        if (entity.getServicePrice() == null) {
            entity.setServicePrice(BigDecimal.valueOf(service.getPrice()));
        }
        if (entity.getTaxId() == null) {
            entity.setTaxId(service.getTaxId());
        }

        int dayCount = 0;

        if (entity.getStartTime() != null && entity.getServiceTime() != null) {
            dayCount = (entity.getStartTime() + entity.getServiceTime()) / 1440;
            var endTime = (entity.getStartTime() + entity.getServiceTime()) % 1440;
            entity.setEndTime(endTime);
        }

        if (entity.getStartDate() != null) {
            entity.setEndDate(entity.getStartDate().plusDays(dayCount));
        }
    }

    private static void check(DogWalkingServiceDetail entity) {
        if (!isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is invalid");
        }
        if (!isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is invalid");
        }
        if (!isNormal(entity.getServiceId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceId is invalid");
        }
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return serviceDetailMapper.update(c -> c.set(dogWalkingServiceDetail.deletedAt)
                .equalTo(LocalDateTime.now())
                .where(dogWalkingServiceDetail.id, isEqualTo(id))
                .and(dogWalkingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List dog walking service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId bookingRequestId
     * @return list of dog walking service detail
     */
    public List<DogWalkingServiceDetail> listByBookingRequestId(long bookingRequestId) {
        return serviceDetailMapper.select(
                c -> c.where(dogWalkingServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(dogWalkingServiceDetail.deletedAt, isNull()));
    }

    /**
     * List dog walking service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of dog walking service detail
     */
    public List<DogWalkingServiceDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return serviceDetailMapper.select(
                c -> c.where(dogWalkingServiceDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(dogWalkingServiceDetail.deletedAt, isNull()));
    }

    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return serviceDetailMapper.update(c -> c.set(dogWalkingServiceDetail.deletedAt)
                .equalTo(LocalDateTime.now())
                .where(dogWalkingServiceDetail.id, isIn(ids))
                .and(dogWalkingServiceDetail.deletedAt, isNull()));
    }

    public int update(DogWalkingServiceDetail detail) {
        return serviceDetailMapper.update(c -> updateSelectiveColumns(detail, c)
                .where(dogWalkingServiceDetail.id, isEqualTo(detail.getId()))
                .and(dogWalkingServiceDetail.deletedAt, isNull()));
    }
}

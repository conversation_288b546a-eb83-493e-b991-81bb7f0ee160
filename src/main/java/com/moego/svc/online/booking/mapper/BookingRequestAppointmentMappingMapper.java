package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingRequestAppointmentMappingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingRequestAppointmentMapping;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingRequestAppointmentMappingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingRequestAppointmentMappingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, appointmentId, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingRequestAppointmentMapping> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingRequestAppointmentMapping> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingRequestAppointmentMappingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="appointment_id", property="appointmentId", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BookingRequestAppointmentMapping> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingRequestAppointmentMappingResult")
    Optional<BookingRequestAppointmentMapping> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int insertMultiple(Collection<BookingRequestAppointmentMapping> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingRequestAppointmentMapping, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(appointmentId).toProperty("appointmentId")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int insertSelective(BookingRequestAppointmentMapping row) {
        return MyBatis3Utils.insert(this::insert, row, bookingRequestAppointmentMapping, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(appointmentId).toPropertyWhenPresent("appointmentId", row::getAppointmentId)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default Optional<BookingRequestAppointmentMapping> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default List<BookingRequestAppointmentMapping> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default List<BookingRequestAppointmentMapping> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default Optional<BookingRequestAppointmentMapping> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingRequestAppointmentMapping, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingRequestAppointmentMapping row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(appointmentId).equalTo(row::getAppointmentId)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingRequestAppointmentMapping row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    default int updateByPrimaryKeySelective(BookingRequestAppointmentMapping row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(appointmentId).equalToWhenPresent(row::getAppointmentId)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
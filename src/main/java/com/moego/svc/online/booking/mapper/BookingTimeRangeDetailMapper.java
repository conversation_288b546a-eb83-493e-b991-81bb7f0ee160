package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingTimeRangeDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingTimeRangeDetail;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingTimeRangeDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingTimeRangeDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, settingId, timeRangeType, firstWeek, secondWeek, thirdWeek, forthWeek, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingTimeRangeDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingTimeRangeDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingTimeRangeDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="setting_id", property="settingId", jdbcType=JdbcType.BIGINT),
        @Result(column="time_range_type", property="timeRangeType", jdbcType=JdbcType.INTEGER),
        @Result(column="first_week", property="firstWeek", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="second_week", property="secondWeek", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="third_week", property="thirdWeek", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="forth_week", property="forthWeek", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BookingTimeRangeDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingTimeRangeDetailResult")
    Optional<BookingTimeRangeDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int insertMultiple(Collection<BookingTimeRangeDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingTimeRangeDetail, c ->
            c.map(settingId).toProperty("settingId")
            .map(timeRangeType).toProperty("timeRangeType")
            .map(firstWeek).toProperty("firstWeek")
            .map(secondWeek).toProperty("secondWeek")
            .map(thirdWeek).toProperty("thirdWeek")
            .map(forthWeek).toProperty("forthWeek")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int insertSelective(BookingTimeRangeDetail row) {
        return MyBatis3Utils.insert(this::insert, row, bookingTimeRangeDetail, c ->
            c.map(settingId).toPropertyWhenPresent("settingId", row::getSettingId)
            .map(timeRangeType).toPropertyWhenPresent("timeRangeType", row::getTimeRangeType)
            .map(firstWeek).toPropertyWhenPresent("firstWeek", row::getFirstWeek)
            .map(secondWeek).toPropertyWhenPresent("secondWeek", row::getSecondWeek)
            .map(thirdWeek).toPropertyWhenPresent("thirdWeek", row::getThirdWeek)
            .map(forthWeek).toPropertyWhenPresent("forthWeek", row::getForthWeek)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default Optional<BookingTimeRangeDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default List<BookingTimeRangeDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default List<BookingTimeRangeDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default Optional<BookingTimeRangeDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingTimeRangeDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingTimeRangeDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalTo(row::getSettingId)
                .set(timeRangeType).equalTo(row::getTimeRangeType)
                .set(firstWeek).equalTo(row::getFirstWeek)
                .set(secondWeek).equalTo(row::getSecondWeek)
                .set(thirdWeek).equalTo(row::getThirdWeek)
                .set(forthWeek).equalTo(row::getForthWeek)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingTimeRangeDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(settingId).equalToWhenPresent(row::getSettingId)
                .set(timeRangeType).equalToWhenPresent(row::getTimeRangeType)
                .set(firstWeek).equalToWhenPresent(row::getFirstWeek)
                .set(secondWeek).equalToWhenPresent(row::getSecondWeek)
                .set(thirdWeek).equalToWhenPresent(row::getThirdWeek)
                .set(forthWeek).equalToWhenPresent(row::getForthWeek)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_detail")
    default int updateByPrimaryKeySelective(BookingTimeRangeDetail row) {
        return update(c ->
            c.set(settingId).equalToWhenPresent(row::getSettingId)
            .set(timeRangeType).equalToWhenPresent(row::getTimeRangeType)
            .set(firstWeek).equalToWhenPresent(row::getFirstWeek)
            .set(secondWeek).equalToWhenPresent(row::getSecondWeek)
            .set(thirdWeek).equalToWhenPresent(row::getThirdWeek)
            .set(forthWeek).equalToWhenPresent(row::getForthWeek)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeDef;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.ScheduleType;
import com.moego.idl.models.online_booking.v1.TimeRangeDef;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class AvailabilityUtilTest {

    @Test
    void getAvailableTimeRanges_NullArrivalPickUpDef_ReturnsNull() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeRanges(null, startDate, endDate, TimeRangeType.ARRIVAL_TIME);

        assertThat(result).isNull();
    }

    @Test
    void getAvailableTimeRanges_NotCustomized_ReturnsNull() {
        ArrivalPickUpTimeDef arrivalPickUpDef =
                ArrivalPickUpTimeDef.newBuilder().setIsCustomized(false).build();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.getAvailableTimeRanges(
                arrivalPickUpDef, startDate, endDate, TimeRangeType.ARRIVAL_TIME);

        assertThat(result).isNull();
    }

    @Test
    void getAvailableTimeRanges_InvalidTimeRangeType_ReturnsNull() {
        ArrivalPickUpTimeDef arrivalPickUpDef =
                ArrivalPickUpTimeDef.newBuilder().setIsCustomized(true).build();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.getAvailableTimeRanges(
                arrivalPickUpDef, startDate, endDate, TimeRangeType.UNRECOGNIZED);

        assertThat(result).isNull();
    }

    @Test
    void getAvailableTimeRanges_ArrivalTimeType_ReturnsCorrectRanges() {
        ArrivalPickUpTimeDef arrivalPickUpDef = ArrivalPickUpTimeDef.newBuilder()
                .setIsCustomized(true)
                .setArrivalTimeRange(TimeRangeDef.newBuilder()
                        .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                                .addAllSunday(List.of(DayTimeRangeDef.newBuilder()
                                        .setStartTime(540)
                                        .setEndTime(720)
                                        .build()))
                                .build())
                        .build())
                .setScheduleType(ScheduleType.EVERY_WEEK)
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .build();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.getAvailableTimeRanges(
                arrivalPickUpDef, startDate, endDate, TimeRangeType.ARRIVAL_TIME);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()),
                LocalDate.of(2023, 1, 2),
                List.of(),
                LocalDate.of(2023, 1, 3),
                List.of(),
                LocalDate.of(2023, 1, 4),
                List.of(),
                LocalDate.of(2023, 1, 5),
                List.of(),
                LocalDate.of(2023, 1, 6),
                List.of(),
                LocalDate.of(2023, 1, 7),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getAvailableTimeRanges_PickUpTimeType_ReturnsCorrectRanges() {
        ArrivalPickUpTimeDef arrivalPickUpDef = ArrivalPickUpTimeDef.newBuilder()
                .setIsCustomized(true)
                .setPickUpTimeRange(TimeRangeDef.newBuilder()
                        .setFirstWeek(DayOfWeekTimeRangeDef.newBuilder()
                                .addAllMonday(List.of(DayTimeRangeDef.newBuilder()
                                        .setStartTime(600)
                                        .setEndTime(780)
                                        .build()))
                                .build())
                        .build())
                .setScheduleType(ScheduleType.EVERY_WEEK)
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .build();
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 7);

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.getAvailableTimeRanges(
                arrivalPickUpDef, startDate, endDate, TimeRangeType.PICK_UP_TIME);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .build()),
                LocalDate.of(2023, 1, 3),
                List.of(),
                LocalDate.of(2023, 1, 4),
                List.of(),
                LocalDate.of(2023, 1, 5),
                List.of(),
                LocalDate.of(2023, 1, 6),
                List.of(),
                LocalDate.of(2023, 1, 7),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    // ==================== getAvailableTimeInRange 测试 ====================

    @Test
    void getAvailableTimeInRange_NoOverrides_ReturnsOriginalRanges() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .build()));
        List<ArrivalPickUpTimeOverrideModel> overrides = List.of();

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeInRange(startDate, endDate, finalTimeRange, overrides);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .build()),
                LocalDate.of(2023, 1, 3),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getAvailableTimeInRange_WithUnavailableOverride() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));
        List<ArrivalPickUpTimeOverrideModel> overrides = List.of(ArrivalPickUpTimeOverrideModel.newBuilder()
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .setEndDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .setIsAvailable(false)
                .build());

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeInRange(startDate, endDate, finalTimeRange, overrides);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1), List.of(),
                LocalDate.of(2023, 1, 2), List.of(),
                LocalDate.of(2023, 1, 3), List.of());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getAvailableTimeInRange_WithAvailableOverride_WithFinalTimeRange() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));
        List<ArrivalPickUpTimeOverrideModel> overrides = List.of(ArrivalPickUpTimeOverrideModel.newBuilder()
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .setEndDate(ProtobufUtil.toProtobufDate("2023-01-02"))
                .setIsAvailable(true)
                .addAllDayTimeRanges(List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(480)
                        .setEndTime(600)
                        .setPetCapacity(5)
                        .build()))
                .build());

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeInRange(startDate, endDate, finalTimeRange, overrides);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(480)
                        .setEndTime(600)
                        .setPetCapacity(5)
                        .build()),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(480)
                        .setEndTime(600)
                        .setPetCapacity(5)
                        .build()),
                LocalDate.of(2023, 1, 3),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getAvailableTimeInRange_WithAvailableOverride_WithoutFinalTimeRange() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        List<ArrivalPickUpTimeOverrideModel> overrides = List.of(ArrivalPickUpTimeOverrideModel.newBuilder()
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .setEndDate(ProtobufUtil.toProtobufDate("2023-01-01"))
                .setIsAvailable(true)
                .addAllDayTimeRanges(List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(480)
                        .setEndTime(600)
                        .setPetCapacity(5)
                        .build()))
                .build());

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeInRange(startDate, endDate, null, overrides);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(480)
                        .setEndTime(600)
                        .build()), // 注意：没有 petCapacity
                LocalDate.of(2023, 1, 2),
                List.of(),
                LocalDate.of(2023, 1, 3),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    @Test
    void getAvailableTimeInRange_OverrideOutsideRange_Ignored() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 3);
        Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange = Map.of(
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));
        List<ArrivalPickUpTimeOverrideModel> overrides = List.of(ArrivalPickUpTimeOverrideModel.newBuilder()
                .setStartDate(ProtobufUtil.toProtobufDate("2023-01-05"))
                .setEndDate(ProtobufUtil.toProtobufDate("2023-01-06"))
                .setIsAvailable(false)
                .build());

        Map<LocalDate, List<DayTimeRangeDef>> result =
                AvailabilityUtil.getAvailableTimeInRange(startDate, endDate, finalTimeRange, overrides);

        Map<LocalDate, List<DayTimeRangeDef>> expected = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(),
                LocalDate.of(2023, 1, 2),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()),
                LocalDate.of(2023, 1, 3),
                List.of());

        assertThat(result).isEqualTo(expected);
    }

    // ==================== mergeTimeRanges 测试 ====================

    @Test
    void mergeTimeRanges_EmptyMaps_ReturnsEmptyMap() {
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of();
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of();

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        assertThat(result).isEmpty();
    }

    @Test
    void mergeTimeRanges_OneEmptyMap_ReturnsEmptyMap() {
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of();

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        assertThat(result).isEmpty();
    }

    @Test
    void mergeTimeRanges_NullMaps_ReturnsEmptyMap() {
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                LocalDate.of(2023, 1, 1),
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));

        assertThat(AvailabilityUtil.mergeTimeRanges(map1, null)).isEmpty();
        assertThat(AvailabilityUtil.mergeTimeRanges(null, map1)).isEmpty();
    }

    @Test
    void mergeTimeRanges_NoOverlap_ReturnsEmptyRanges() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(780)
                        .setEndTime(960)
                        .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        assertThat(result.get(testDate)).isEmpty();
    }

    @Test
    void mergeTimeRanges_PartialOverlap_ReturnsIntersection() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(600)
                .setEndTime(720)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result.get(testDate)).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeTimeRanges_FullOverlap_ReturnsIntersection() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(3)
                        .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(720)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result.get(testDate)).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeTimeRanges_MultipleRanges_ReturnsCorrectIntersections() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(
                        DayTimeRangeDef.newBuilder()
                                .setStartTime(540)
                                .setEndTime(720)
                                .setPetCapacity(5)
                                .build(),
                        DayTimeRangeDef.newBuilder()
                                .setStartTime(900)
                                .setEndTime(1080)
                                .setPetCapacity(3)
                                .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(
                        DayTimeRangeDef.newBuilder()
                                .setStartTime(600)
                                .setEndTime(780)
                                .setPetCapacity(4)
                                .build(),
                        DayTimeRangeDef.newBuilder()
                                .setStartTime(960)
                                .setEndTime(1140)
                                .setPetCapacity(2)
                                .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(720)
                        .setPetCapacity(4) // min(5, 4)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(960)
                        .setEndTime(1080)
                        .setPetCapacity(2) // min(3, 2)
                        .build());

        assertThat(result.get(testDate)).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeTimeRanges_NoPetCapacity_UsesMaxCapacity() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .build())); // 没有 petCapacity，默认为 999
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(600)
                .setEndTime(720)
                .setPetCapacity(3) // min(999, 3)
                .build());

        assertThat(result.get(testDate)).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeTimeRanges_ZeroPetCapacity_FilteredOut() {
        LocalDate testDate = LocalDate.of(2023, 1, 1);
        Map<LocalDate, List<DayTimeRangeDef>> map1 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build()));
        Map<LocalDate, List<DayTimeRangeDef>> map2 = Map.of(
                testDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(0)
                        .build()));

        Map<LocalDate, List<DayTimeRangeDef>> result = AvailabilityUtil.mergeTimeRanges(map1, map2);

        // 交集为 600-720，容量为 min(5, 0) = 0，被过滤掉
        assertThat(result.get(testDate)).isEmpty();
    }

    // ==================== intersectTimeRanges 测试 ====================

    @Test
    void intersectTimeRanges_EmptyLists_ReturnsEmptyList() {
        List<DayTimeRangeDef> ranges1 = List.of();
        List<DayTimeRangeDef> ranges2 = List.of();

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        assertThat(result).isEmpty();
    }

    @Test
    void intersectTimeRanges_OneEmptyList_ReturnsEmptyList() {
        List<DayTimeRangeDef> ranges1 = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build());
        List<DayTimeRangeDef> ranges2 = List.of();

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        assertThat(result).isEmpty();
    }

    @Test
    void intersectTimeRanges_NoOverlap_ReturnsEmptyList() {
        List<DayTimeRangeDef> ranges1 = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build());
        List<DayTimeRangeDef> ranges2 = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(780).setEndTime(960).build());

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        assertThat(result).isEmpty();
    }

    @Test
    void intersectTimeRanges_PartialOverlap_ReturnsIntersection() {
        List<DayTimeRangeDef> ranges1 = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(720)
                .setPetCapacity(5)
                .build());
        List<DayTimeRangeDef> ranges2 = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(600)
                .setEndTime(780)
                .setPetCapacity(3)
                .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(600)
                .setEndTime(720)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void intersectTimeRanges_MultipleOverlaps_ReturnsAllIntersections() {
        List<DayTimeRangeDef> ranges1 = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(900)
                        .setEndTime(1080)
                        .setPetCapacity(3)
                        .build());
        List<DayTimeRangeDef> ranges2 = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(4)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(960)
                        .setEndTime(1140)
                        .setPetCapacity(2)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        List<DayTimeRangeDef> expected = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(720)
                        .setPetCapacity(4) // min(5, 4)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(960)
                        .setEndTime(1080)
                        .setPetCapacity(2) // min(3, 2)
                        .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void intersectTimeRanges_OverlappingIntersections_MergedCorrectly() {
        List<DayTimeRangeDef> ranges1 = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(720)
                .setPetCapacity(5)
                .build());
        List<DayTimeRangeDef> ranges2 = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(660)
                        .setPetCapacity(4)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(660)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.intersectTimeRanges(ranges1, ranges2);

        // 两个交集区间 600-660 和 660-720 应该被合并为 600-720
        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(600)
                .setEndTime(720)
                .setPetCapacity(3) // min(5, 4, 3)
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    // ==================== mergeOverlappingRanges 测试 ====================

    @Test
    void mergeOverlappingRanges_EmptyList_ReturnsEmptyList() {
        List<DayTimeRangeDef> ranges = List.of();

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        assertThat(result).isEmpty();
    }

    @Test
    void mergeOverlappingRanges_SingleRange_ReturnsSameRange() {
        List<DayTimeRangeDef> ranges = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(720)
                .setPetCapacity(5)
                .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        assertThat(result).usingRecursiveComparison().isEqualTo(ranges);
    }

    @Test
    void mergeOverlappingRanges_NoOverlap_ReturnsAllRanges() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(780)
                        .setEndTime(960)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        assertThat(result).usingRecursiveComparison().isEqualTo(ranges);
    }

    @Test
    void mergeOverlappingRanges_OverlappingRanges_MergedCorrectly() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(780)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeOverlappingRanges_ContiguousRanges_MergedCorrectly() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(720)
                        .setEndTime(900)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(900)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeOverlappingRanges_MultipleOverlaps_MergedCorrectly() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(660)
                        .setEndTime(840)
                        .setPetCapacity(4)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(840)
                .setPetCapacity(3) // 取所有区间的最小值
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeOverlappingRanges_NoPetCapacity_UsesMaxCapacity() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(720).build(), // 没有 petCapacity，默认为 999
                DayTimeRangeDef.newBuilder()
                        .setStartTime(600)
                        .setEndTime(780)
                        .setPetCapacity(3)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(780)
                .setPetCapacity(3) // min(999, 3)
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void mergeOverlappingRanges_UnsortedRanges_SortedAndMerged() {
        List<DayTimeRangeDef> ranges = List.of(
                DayTimeRangeDef.newBuilder()
                        .setStartTime(720)
                        .setEndTime(900)
                        .setPetCapacity(3)
                        .build(),
                DayTimeRangeDef.newBuilder()
                        .setStartTime(540)
                        .setEndTime(720)
                        .setPetCapacity(5)
                        .build());

        List<DayTimeRangeDef> result = AvailabilityUtil.mergeOverlappingRanges(ranges);

        List<DayTimeRangeDef> expected = List.of(DayTimeRangeDef.newBuilder()
                .setStartTime(540)
                .setEndTime(900)
                .setPetCapacity(3) // 取最小值
                .build());

        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }
}

package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.TimeRangeDef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.BookingTimeRangeDetail;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONObject;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {DateConverter.class})
public interface TimeRangeConverter {

    TimeRangeConverter INSTANCE = Mappers.getMapper(TimeRangeConverter.class);

    BookingTimeRangeDetail defToPO(TimeRangeDef def);

    TimeRangeDef poToDef(BookingTimeRangeDetail po);

    default String toString(DayOfWeekTimeRangeDef def) {
        return JsonUtil.toJson(def);
    }

    default DayOfWeekTimeRangeDef stringToDef(String def) {
        return JsonUtil.toBean(def, DayOfWeekTimeRangeDef.class);
    }

    default String toString(List<DayTimeRangeDef> timeRanges) {
        return JsonUtil.toJson(timeRanges);
    }

    default List<DayTimeRangeDef> stringToDayTimeRangeList(String str) {
        List<DayTimeRangeDef> result = new ArrayList<>();
        JSONArray jsonArray = new JSONArray(str);
        for (int i = 0; i < jsonArray.length(); i++) {
            result.add(jsonToDayTimeRangeDef(jsonArray.getJSONObject(i)));
        }
        return result;
    }

    default DayTimeRangeDef jsonToDayTimeRangeDef(JSONObject json) {
        int startTime = json.optInt("startTime");
        int endTime = json.optInt("endTime");
        var build = DayTimeRangeDef.newBuilder().setStartTime(startTime).setEndTime(endTime);
        if (json.has("petCapacity")) {
            build.setPetCapacity(json.getInt("petCapacity"));
        }
        return build.build();
    }
}

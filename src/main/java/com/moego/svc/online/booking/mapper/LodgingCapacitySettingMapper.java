package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.LodgingCapacitySettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface LodgingCapacitySettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<LodgingCapacitySettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, serviceItemType, isCapacityLimited, capacityLimit, allowWaitlistSignups);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<LodgingCapacitySetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<LodgingCapacitySetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="LodgingCapacitySettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="is_capacity_limited", property="isCapacityLimited", jdbcType=JdbcType.BIT),
        @Result(column="capacity_limit", property="capacityLimit", jdbcType=JdbcType.INTEGER),
        @Result(column="allow_waitlist_signups", property="allowWaitlistSignups", jdbcType=JdbcType.BIT)
    })
    List<LodgingCapacitySetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("LodgingCapacitySettingResult")
    Optional<LodgingCapacitySetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int insertMultiple(Collection<LodgingCapacitySetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, lodgingCapacitySetting, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(isCapacityLimited).toProperty("isCapacityLimited")
            .map(capacityLimit).toProperty("capacityLimit")
            .map(allowWaitlistSignups).toProperty("allowWaitlistSignups")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int insertSelective(LodgingCapacitySetting row) {
        return MyBatis3Utils.insert(this::insert, row, lodgingCapacitySetting, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(isCapacityLimited).toPropertyWhenPresent("isCapacityLimited", row::getIsCapacityLimited)
            .map(capacityLimit).toPropertyWhenPresent("capacityLimit", row::getCapacityLimit)
            .map(allowWaitlistSignups).toPropertyWhenPresent("allowWaitlistSignups", row::getAllowWaitlistSignups)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default Optional<LodgingCapacitySetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default List<LodgingCapacitySetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default List<LodgingCapacitySetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default Optional<LodgingCapacitySetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, lodgingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(LodgingCapacitySetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(isCapacityLimited).equalTo(row::getIsCapacityLimited)
                .set(capacityLimit).equalTo(row::getCapacityLimit)
                .set(allowWaitlistSignups).equalTo(row::getAllowWaitlistSignups);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(LodgingCapacitySetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(isCapacityLimited).equalToWhenPresent(row::getIsCapacityLimited)
                .set(capacityLimit).equalToWhenPresent(row::getCapacityLimit)
                .set(allowWaitlistSignups).equalToWhenPresent(row::getAllowWaitlistSignups);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: lodging_capacity_setting")
    default int updateByPrimaryKeySelective(LodgingCapacitySetting row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(isCapacityLimited).equalToWhenPresent(row::getIsCapacityLimited)
            .set(capacityLimit).equalToWhenPresent(row::getCapacityLimit)
            .set(allowWaitlistSignups).equalToWhenPresent(row::getAllowWaitlistSignups)
            .where(id, isEqualTo(row::getId))
        );
    }
}
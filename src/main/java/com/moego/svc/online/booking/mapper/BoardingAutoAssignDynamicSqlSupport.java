package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BoardingAutoAssignDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    public static final BoardingAutoAssign boardingAutoAssign = new BoardingAutoAssign();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.id")
    public static final SqlColumn<Long> id = boardingAutoAssign.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = boardingAutoAssign.bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.boarding_service_detail_id")
    public static final SqlColumn<Long> boardingServiceDetailId = boardingAutoAssign.boardingServiceDetailId;

    /**
     * Database Column Remarks:
     *   The id of lodging
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.lodging_id")
    public static final SqlColumn<Long> lodgingId = boardingAutoAssign.lodgingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.created_at")
    public static final SqlColumn<Date> createdAt = boardingAutoAssign.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.updated_at")
    public static final SqlColumn<Date> updatedAt = boardingAutoAssign.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: boarding_auto_assign.deleted_at")
    public static final SqlColumn<Date> deletedAt = boardingAutoAssign.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: boarding_auto_assign")
    public static final class BoardingAutoAssign extends AliasableSqlTable<BoardingAutoAssign> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> boardingServiceDetailId = column("boarding_service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<Long> lodgingId = column("lodging_id", JDBCType.BIGINT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public BoardingAutoAssign() {
            super("boarding_auto_assign", BoardingAutoAssign::new);
        }
    }
}
package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.StaffSlotFreeServiceDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.StaffSlotFreeService;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface StaffSlotFreeServiceMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<StaffSlotFreeServiceMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, businessId, staffId, serviceId, createdAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<StaffSlotFreeService> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<StaffSlotFreeService> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="StaffSlotFreeServiceResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<StaffSlotFreeService> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("StaffSlotFreeServiceResult")
    Optional<StaffSlotFreeService> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int insertMultiple(Collection<StaffSlotFreeService> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, staffSlotFreeService, c ->
            c.map(companyId).toProperty("companyId")
            .map(businessId).toProperty("businessId")
            .map(staffId).toProperty("staffId")
            .map(serviceId).toProperty("serviceId")
            .map(createdAt).toProperty("createdAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int insertSelective(StaffSlotFreeService row) {
        return MyBatis3Utils.insert(this::insert, row, staffSlotFreeService, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default Optional<StaffSlotFreeService> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default List<StaffSlotFreeService> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default List<StaffSlotFreeService> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default Optional<StaffSlotFreeService> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, staffSlotFreeService, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    static UpdateDSL<UpdateModel> updateAllColumns(StaffSlotFreeService row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(businessId).equalTo(row::getBusinessId)
                .set(staffId).equalTo(row::getStaffId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(createdAt).equalTo(row::getCreatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(StaffSlotFreeService row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    default int updateByPrimaryKeySelective(StaffSlotFreeService row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
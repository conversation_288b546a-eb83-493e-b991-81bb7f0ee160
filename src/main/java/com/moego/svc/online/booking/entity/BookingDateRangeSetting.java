package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_date_range_setting
 */
public class BookingDateRangeSetting {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.service_item_type")
    private Integer serviceItemType;

    /**
     * Database Column Remarks:
     *   0: no limit, 1: offset from today, 2: specific date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.start_date_type")
    private Integer startDateType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_start_date")
    private Date specificStartDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_start_date_offset")
    private Integer maxStartDateOffset;

    /**
     * Database Column Remarks:
     *   0: no limit, 1: offset from today, 2: specific date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.end_date_type")
    private Integer endDateType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_end_date")
    private Date specificEndDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_end_date_offset")
    private Integer maxEndDateOffset;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.created_at")
    private Date createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_at")
    private Date updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_by")
    private Long updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.service_item_type")
    public Integer getServiceItemType() {
        return serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.service_item_type")
    public void setServiceItemType(Integer serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.start_date_type")
    public Integer getStartDateType() {
        return startDateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.start_date_type")
    public void setStartDateType(Integer startDateType) {
        this.startDateType = startDateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_start_date")
    public Date getSpecificStartDate() {
        return specificStartDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_start_date")
    public void setSpecificStartDate(Date specificStartDate) {
        this.specificStartDate = specificStartDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_start_date_offset")
    public Integer getMaxStartDateOffset() {
        return maxStartDateOffset;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_start_date_offset")
    public void setMaxStartDateOffset(Integer maxStartDateOffset) {
        this.maxStartDateOffset = maxStartDateOffset;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.end_date_type")
    public Integer getEndDateType() {
        return endDateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.end_date_type")
    public void setEndDateType(Integer endDateType) {
        this.endDateType = endDateType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_end_date")
    public Date getSpecificEndDate() {
        return specificEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_end_date")
    public void setSpecificEndDate(Date specificEndDate) {
        this.specificEndDate = specificEndDate;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_end_date_offset")
    public Integer getMaxEndDateOffset() {
        return maxEndDateOffset;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_end_date_offset")
    public void setMaxEndDateOffset(Integer maxEndDateOffset) {
        this.maxEndDateOffset = maxEndDateOffset;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.created_at")
    public Date getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.created_at")
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_at")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_at")
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_by")
    public Long getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_by")
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessId=").append(businessId);
        sb.append(", companyId=").append(companyId);
        sb.append(", serviceItemType=").append(serviceItemType);
        sb.append(", startDateType=").append(startDateType);
        sb.append(", specificStartDate=").append(specificStartDate);
        sb.append(", maxStartDateOffset=").append(maxStartDateOffset);
        sb.append(", endDateType=").append(endDateType);
        sb.append(", specificEndDate=").append(specificEndDate);
        sb.append(", maxEndDateOffset=").append(maxEndDateOffset);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingDateRangeSetting other = (BookingDateRangeSetting) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getServiceItemType() == null ? other.getServiceItemType() == null : this.getServiceItemType().equals(other.getServiceItemType()))
            && (this.getStartDateType() == null ? other.getStartDateType() == null : this.getStartDateType().equals(other.getStartDateType()))
            && (this.getSpecificStartDate() == null ? other.getSpecificStartDate() == null : this.getSpecificStartDate().equals(other.getSpecificStartDate()))
            && (this.getMaxStartDateOffset() == null ? other.getMaxStartDateOffset() == null : this.getMaxStartDateOffset().equals(other.getMaxStartDateOffset()))
            && (this.getEndDateType() == null ? other.getEndDateType() == null : this.getEndDateType().equals(other.getEndDateType()))
            && (this.getSpecificEndDate() == null ? other.getSpecificEndDate() == null : this.getSpecificEndDate().equals(other.getSpecificEndDate()))
            && (this.getMaxEndDateOffset() == null ? other.getMaxEndDateOffset() == null : this.getMaxEndDateOffset().equals(other.getMaxEndDateOffset()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getServiceItemType() == null) ? 0 : getServiceItemType().hashCode());
        result = prime * result + ((getStartDateType() == null) ? 0 : getStartDateType().hashCode());
        result = prime * result + ((getSpecificStartDate() == null) ? 0 : getSpecificStartDate().hashCode());
        result = prime * result + ((getMaxStartDateOffset() == null) ? 0 : getMaxStartDateOffset().hashCode());
        result = prime * result + ((getEndDateType() == null) ? 0 : getEndDateType().hashCode());
        result = prime * result + ((getSpecificEndDate() == null) ? 0 : getSpecificEndDate().hashCode());
        result = prime * result + ((getMaxEndDateOffset() == null) ? 0 : getMaxEndDateOffset().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.offering.v1.ServiceCategoryModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.online_booking.v1.ApplicableServices;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.GetServiceListResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.svc.online.booking.entity.ObCustomizeCareType;
import com.moego.svc.online.booking.mapper.ObCustomizeCareTypeMapper;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;

@ExtendWith(MockitoExtension.class)
class BookingCareTypeServiceTest {

    @Mock
    private ObCustomizeCareTypeMapper obCustomizeCareTypeMapper;

    @Mock
    private ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceBlockingStub;

    @InjectMocks
    private BookingCareTypeService bookingCareTypeService;

    private ObCustomizeCareType testEntity;
    private List<ObCustomizeCareType> testEntities;

    @BeforeEach
    void setUp() {
        testEntity = new ObCustomizeCareType();
        testEntity.setId(1L);
        testEntity.setCompanyId(123L);
        testEntity.setBusinessId(456L);
        testEntity.setName("Test Care Type");
        testEntity.setServiceType(ServiceType.SERVICE);
        testEntity.setServiceItemType(ServiceItemType.GROOMING);
        testEntity.setSort(1);
        testEntity.setSelectedServices(Arrays.asList(1L, 2L, 3L));
        testEntity.setIsAllServiceApplicable(false);
        testEntity.setCreatedAt(LocalDateTime.now());
        testEntity.setUpdatedAt(LocalDateTime.now());

        testEntities = Arrays.asList(testEntity);
    }

    @Test
    void testInsert_AutoGenerateSort_EmptyList() {
        // Given - 测试通过insert方法间接测试getNextSortValue逻辑
        ObCustomizeCareType entity = new ObCustomizeCareType();
        entity.setCompanyId(123L);
        entity.setBusinessId(456L);
        entity.setServiceType(ServiceType.ADDON);
        entity.setName("Test Addon");
        entity.setServiceItemType(ServiceItemType.GROOMING);
        entity.setSelectedServices(Arrays.asList(1L, 2L));
        entity.setIsAllServiceApplicable(false);
        // 不设置sort，让方法自动生成

        when(obCustomizeCareTypeMapper.select(any(SelectDSLCompleter.class)))
                .thenReturn(Collections.emptyList()); // 模拟没有现有记录
        when(obCustomizeCareTypeMapper.insertSelective(any(ObCustomizeCareType.class)))
                .thenAnswer(invocation -> {
                    ObCustomizeCareType arg = invocation.getArgument(0);
                    arg.setId(1L); // 模拟数据库生成的ID
                    return 1;
                });

        // When
        long result = bookingCareTypeService.insert(entity);

        // Then
        assertEquals(1L, result);
        assertEquals(1, entity.getSort()); // 验证sort被设置为1
    }

    @Test
    void testInsert_AutoGenerateSort_WithExisting() {
        // Given
        ObCustomizeCareType entity = new ObCustomizeCareType();
        entity.setCompanyId(123L);
        entity.setBusinessId(456L);
        entity.setServiceType(ServiceType.ADDON);
        entity.setName("Test Addon");
        entity.setServiceItemType(ServiceItemType.GROOMING);
        entity.setSelectedServices(Arrays.asList(1L, 2L));
        entity.setIsAllServiceApplicable(false);

        ObCustomizeCareType existingEntity = new ObCustomizeCareType();
        existingEntity.setSort(5);
        when(obCustomizeCareTypeMapper.select(any(SelectDSLCompleter.class))).thenReturn(Arrays.asList(existingEntity));
        when(obCustomizeCareTypeMapper.insertSelective(any(ObCustomizeCareType.class)))
                .thenAnswer(invocation -> {
                    ObCustomizeCareType arg = invocation.getArgument(0);
                    arg.setId(2L);
                    return 1;
                });

        // When
        long result = bookingCareTypeService.insert(entity);

        // Then
        assertEquals(2L, result);
        assertEquals(6, entity.getSort()); // 验证sort被设置为6 (5+1)
    }

    @Test
    void testCheckServiceDuplicate_NoConflict() {
        // Given
        ObCustomizeCareType originalEntity = new ObCustomizeCareType();
        originalEntity.setId(1L);
        originalEntity.setCompanyId(123L);
        originalEntity.setBusinessId(456L);
        originalEntity.setIsAllServiceApplicable(false);
        originalEntity.setSelectedServices(Arrays.asList(1L, 2L));

        ObCustomizeCareType updateEntity = new ObCustomizeCareType();
        updateEntity.setId(1L);
        updateEntity.setCompanyId(123L);
        updateEntity.setBusinessId(456L);
        updateEntity.setIsAllServiceApplicable(false);
        updateEntity.setSelectedServices(Arrays.asList(3L, 4L));

        Set<Long> originalIds = new HashSet<>(Arrays.asList(1L, 2L));
        Set<Long> updateIds = new HashSet<>(Arrays.asList(3L, 4L));

        // Mock no existing conflicts
        when(obCustomizeCareTypeMapper.select(any(SelectDSLCompleter.class))).thenReturn(Collections.emptyList());

        // When & Then
        assertDoesNotThrow(() -> {
            bookingCareTypeService.checkServiceDuplicate(originalEntity, updateEntity, originalIds, updateIds);
        });
    }

    @Test
    void testGetActiveServiceOBCareTypeViews_EmptySelectedServices() {
        // Given
        BookingCareTypeView careTypeView = BookingCareTypeView.newBuilder()
                .setId(1L)
                .setName("Test Care Type")
                .setApplicableServices(ApplicableServices.newBuilder()
                        .setIsAllServiceApplicable(false)
                        .build())
                .build();

        List<BookingCareTypeView> views = Arrays.asList(careTypeView);
        Long companyId = 123L;
        Long businessId = 456L;

        // When
        List<BookingCareTypeView> result =
                bookingCareTypeService.getActiveServiceOBCareTypeViews(views, companyId, businessId);

        // Then
        assertEquals(1, result.size());
        assertEquals(careTypeView, result.get(0));
    }

    @Test
    void testGetActiveServiceOBCareTypeViews_WithSelectedServices() {
        // Given
        BookingCareTypeView careTypeView = BookingCareTypeView.newBuilder()
                .setId(1L)
                .setName("Test Care Type")
                .setApplicableServices(ApplicableServices.newBuilder()
                        .setIsAllServiceApplicable(false)
                        .addAllSelectedServices(Arrays.asList(1L, 2L, 3L))
                        .build())
                .build();

        List<BookingCareTypeView> views = Arrays.asList(careTypeView);
        Long companyId = 123L;
        Long businessId = 456L;

        // Mock service response with proper structure
        ServiceModel mockService = ServiceModel.newBuilder()
                .setServiceId(1L)
                .setName("Test Service")
                .build();

        ServiceCategoryModel mockCategory = ServiceCategoryModel.newBuilder()
                .setCategoryId(1L)
                .setName("Test Category")
                .addServices(mockService)
                .build();

        GetServiceListResponse mockResponse = GetServiceListResponse.newBuilder()
                .addCategoryList(mockCategory)
                .build();

        when(serviceBlockingStub.getServiceList(any(GetServiceListRequest.class)))
                .thenReturn(mockResponse);

        // When
        List<BookingCareTypeView> result =
                bookingCareTypeService.getActiveServiceOBCareTypeViews(views, companyId, businessId);

        // Then
        assertEquals(1, result.size());
    }

    @Test
    void testBatchUpdateSort_EmptyList() {
        // Given
        List<BookingCareTypeService.SortItem> sortItems = Collections.emptyList();
        Long staffId = 100L;

        // When
        int result = bookingCareTypeService.batchUpdateSort(sortItems, staffId);

        // Then
        assertEquals(0, result);
    }

    @Test
    void testBatchUpdateSort_NullList() {
        // Given
        List<BookingCareTypeService.SortItem> sortItems = null;
        Long staffId = 100L;

        // When
        int result = bookingCareTypeService.batchUpdateSort(sortItems, staffId);

        // Then
        assertEquals(0, result);
    }
}

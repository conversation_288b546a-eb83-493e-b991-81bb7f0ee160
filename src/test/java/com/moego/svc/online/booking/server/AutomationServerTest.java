package com.moego.svc.online.booking.server;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AutomationConditionDef;
import com.moego.idl.models.online_booking.v1.ProfileUpdateCondition;
import com.moego.idl.models.online_booking.v1.VaccineStatusCondition;
import com.moego.idl.service.online_booking.v1.GetAutomationSettingRequest;
import com.moego.idl.service.online_booking.v1.GetAutomationSettingResponse;
import com.moego.idl.service.online_booking.v1.UpdateAutomationSettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateAutomationSettingResponse;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.AutomationSetting;
import com.moego.svc.online.booking.mapper.AutomationSettingMapper;
import com.moego.svc.online.booking.service.AutomationSettingService;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AutomationServerTest {

    @Mock
    private AutomationSettingMapper automationSettingMapper;

    @Mock
    private AutomationSettingService automationSettingService;

    @Mock
    private StreamObserver<GetAutomationSettingResponse> getResponseObserver;

    @Mock
    private StreamObserver<UpdateAutomationSettingResponse> updateResponseObserver;

    @InjectMocks
    private AutomationServer automationServer;

    /**
     * {@link AutomationServer#getAutomationSetting(GetAutomationSettingRequest, StreamObserver)}
     */
    @Test
    void testGetAutomationSetting_Existing() {
        long businessId = 1L;
        long companyId = 2L;
        ServiceItemType serviceItemType = ServiceItemType.GROOMING;

        AutomationSetting existingSetting = new AutomationSetting();
        existingSetting.setBusinessId(businessId);
        existingSetting.setCompanyId(companyId);
        existingSetting.setServiceItemType(serviceItemType.getNumber());
        existingSetting.setEnableAutoAccept(true);
        existingSetting.setAutoAcceptCondition(JsonUtil.toJson(AutomationConditionDef.getDefaultInstance()));

        when(automationSettingService.getAutomationSetting(anyLong(), anyLong(), any()))
                .thenReturn(existingSetting);

        GetAutomationSettingRequest request = GetAutomationSettingRequest.newBuilder()
                .setBusinessId(businessId)
                .setCompanyId(companyId)
                .setServiceItemType(serviceItemType)
                .build();

        automationServer.getAutomationSetting(request, getResponseObserver);

        verify(getResponseObserver).onNext(any(GetAutomationSettingResponse.class));
        verify(getResponseObserver).onCompleted();
    }

    @Test
    void testUpdateAutomationSetting_Existing() {
        long businessId = 1L;
        long companyId = 2L;
        long staffId = 3L;
        ServiceItemType serviceItemType = ServiceItemType.GROOMING;

        AutomationSetting existingSetting = new AutomationSetting();
        existingSetting.setId(1L);
        existingSetting.setBusinessId(businessId);
        existingSetting.setCompanyId(companyId);
        existingSetting.setServiceItemType(serviceItemType.getNumber());

        when(automationSettingService.getAutomationSetting(anyLong(), anyLong(), any()))
                .thenReturn(existingSetting);

        AutomationConditionDef newCondition = AutomationConditionDef.newBuilder()
                .setAcceptClientType(AcceptClientType.ACCEPT_CLIENT_TYPE_NEW)
                .setProfileUpdateCondition(ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL)
                .setVaccineStatusCondition(VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL)
                .build();

        UpdateAutomationSettingRequest request = UpdateAutomationSettingRequest.newBuilder()
                .setBusinessId(businessId)
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .setServiceItemType(serviceItemType)
                .setEnableAutoAccept(true)
                .setAutoAcceptCondition(newCondition)
                .build();

        automationServer.updateAutomationSetting(request, updateResponseObserver);

        verify(automationSettingMapper).updateByPrimaryKeySelective(any(AutomationSetting.class));
        verify(updateResponseObserver).onNext(any(UpdateAutomationSettingResponse.class));
        verify(updateResponseObserver).onCompleted();
    }

    @Test
    void testUpdateAutomationSetting_New() {
        long businessId = 1L;
        long companyId = 2L;
        long staffId = 3L;
        ServiceItemType serviceItemType = ServiceItemType.GROOMING;

        AutomationSetting defaultSetting = new AutomationSetting();
        defaultSetting.setBusinessId(businessId);
        defaultSetting.setCompanyId(companyId);
        defaultSetting.setServiceItemType(serviceItemType.getNumber());

        when(automationSettingService.getAutomationSetting(anyLong(), anyLong(), any()))
                .thenReturn(defaultSetting);

        AutomationConditionDef newCondition = AutomationConditionDef.newBuilder()
                .setAcceptClientType(AcceptClientType.ACCEPT_CLIENT_TYPE_NEW)
                .setProfileUpdateCondition(ProfileUpdateCondition.PROFILE_UPDATE_CONDITION_ALL)
                .setVaccineStatusCondition(VaccineStatusCondition.VACCINE_STATUS_CONDITION_ALL)
                .build();

        UpdateAutomationSettingRequest request = UpdateAutomationSettingRequest.newBuilder()
                .setBusinessId(businessId)
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .setServiceItemType(serviceItemType)
                .setEnableAutoAccept(true)
                .setAutoAcceptCondition(newCondition)
                .build();

        automationServer.updateAutomationSetting(request, updateResponseObserver);

        verify(automationSettingMapper).insertSelective(any(AutomationSetting.class));
        verify(updateResponseObserver).onNext(any(UpdateAutomationSettingResponse.class));
        verify(updateResponseObserver).onCompleted();
    }
}

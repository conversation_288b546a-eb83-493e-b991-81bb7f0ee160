package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.AcceptCustomerSettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.AcceptCustomerSetting;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface AcceptCustomerSettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<AcceptCustomerSettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, companyId, serviceItemType, acceptedCustomerType, createdAt, updatedAt, updateBy);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<AcceptCustomerSetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<AcceptCustomerSetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="AcceptCustomerSettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.BIGINT),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="accepted_customer_type", property="acceptedCustomerType", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_by", property="updateBy", jdbcType=JdbcType.BIGINT)
    })
    List<AcceptCustomerSetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("AcceptCustomerSettingResult")
    Optional<AcceptCustomerSetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int insertMultiple(Collection<AcceptCustomerSetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, acceptCustomerSetting, c ->
            c.map(businessId).toProperty("businessId")
            .map(companyId).toProperty("companyId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(acceptedCustomerType).toProperty("acceptedCustomerType")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(updateBy).toProperty("updateBy")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int insertSelective(AcceptCustomerSetting row) {
        return MyBatis3Utils.insert(this::insert, row, acceptCustomerSetting, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(acceptedCustomerType).toPropertyWhenPresent("acceptedCustomerType", row::getAcceptedCustomerType)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(updateBy).toPropertyWhenPresent("updateBy", row::getUpdateBy)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default Optional<AcceptCustomerSetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default List<AcceptCustomerSetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default List<AcceptCustomerSetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default Optional<AcceptCustomerSetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, acceptCustomerSetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(AcceptCustomerSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(companyId).equalTo(row::getCompanyId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(acceptedCustomerType).equalTo(row::getAcceptedCustomerType)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(updateBy).equalTo(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AcceptCustomerSetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(acceptedCustomerType).equalToWhenPresent(row::getAcceptedCustomerType)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(updateBy).equalToWhenPresent(row::getUpdateBy);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: accept_customer_setting")
    default int updateByPrimaryKeySelective(AcceptCustomerSetting row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(acceptedCustomerType).equalToWhenPresent(row::getAcceptedCustomerType)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(updateBy).equalToWhenPresent(row::getUpdateBy)
            .where(id, isEqualTo(row::getId))
        );
    }
}
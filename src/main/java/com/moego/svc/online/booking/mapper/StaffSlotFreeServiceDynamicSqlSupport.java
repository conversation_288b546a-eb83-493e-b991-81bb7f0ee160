package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class StaffSlotFreeServiceDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    public static final StaffSlotFreeService staffSlotFreeService = new StaffSlotFreeService();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.id")
    public static final SqlColumn<Long> id = staffSlotFreeService.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.company_id")
    public static final SqlColumn<Long> companyId = staffSlotFreeService.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.business_id")
    public static final SqlColumn<Long> businessId = staffSlotFreeService.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.staff_id")
    public static final SqlColumn<Long> staffId = staffSlotFreeService.staffId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.service_id")
    public static final SqlColumn<Long> serviceId = staffSlotFreeService.serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_slot_free_service.created_at")
    public static final SqlColumn<Date> createdAt = staffSlotFreeService.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_slot_free_service")
    public static final class StaffSlotFreeService extends AliasableSqlTable<StaffSlotFreeService> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public StaffSlotFreeService() {
            super("staff_slot_free_service", StaffSlotFreeService::new);
        }
    }
}
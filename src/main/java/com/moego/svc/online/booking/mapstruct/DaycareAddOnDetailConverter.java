package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.service.online_booking.v1.CreateDaycareAddOnDetailRequest;
import com.moego.idl.service.online_booking.v1.UpdateDaycareAddOnDetailRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.DaycareAddOnDetail;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DaycareAddOnDetailConverter {
    DaycareAddOnDetailConverter INSTANCE = Mappers.getMapper(DaycareAddOnDetailConverter.class);

    @Mapping(target = "specificDates", ignore = true)
    DaycareAddOnDetailModel entityToModel(DaycareAddOnDetail entity);

    @Mapping(target = "specificDates", ignore = true)
    DaycareAddOnDetail createRequestToEntity(CreateDaycareAddOnDetailRequest createRequest);

    default DaycareAddOnDetail updateRequestToEntity(UpdateDaycareAddOnDetailRequest updateRequest) {
        var detail = new DaycareAddOnDetail();
        detail.setId(updateRequest.getId());
        if (updateRequest.hasSpecificDates()) {
            detail.setSpecificDates(
                    JsonUtil.toJson(updateRequest.getSpecificDates().getValuesList()));
            detail.setIsEveryday(false);
        }
        if (updateRequest.hasIsEveryday()) {
            detail.setSpecificDates("[]");
            detail.setIsEveryday(updateRequest.getIsEveryday());
        }
        if (updateRequest.hasQuantityPerDay()) {
            detail.setQuantityPerDay(updateRequest.getQuantityPerDay());
        }
        return detail;
    }

    /*
     * Do NOT use any of the methods below,
     * their purpose is to perform mutual conversions between Protobuf and Java value types.
     */

    default com.google.protobuf.Timestamp dateToPBTimestamp(java.util.Date date) {
        return com.google.protobuf.util.Timestamps.fromDate(date);
    }

    default java.util.Date pbTimestampToDate(com.google.protobuf.Timestamp timestamp) {
        return new java.util.Date(com.google.protobuf.util.Timestamps.toMillis(timestamp));
    }

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    @AfterMapping
    default void setSpecificDates(DaycareAddOnDetail source, @MappingTarget DaycareAddOnDetailModel.Builder target) {
        Optional.ofNullable(source.getSpecificDates())
                .filter(StringUtils::hasText)
                .map(e -> JsonUtil.toList(e, String.class))
                .ifPresent(target::addAllSpecificDates);
    }

    @AfterMapping
    default void setSpecificDates(CreateDaycareAddOnDetailRequest source, @MappingTarget DaycareAddOnDetail target) {
        target.setSpecificDates(JsonUtil.toJson(source.getSpecificDatesList()));
    }
}

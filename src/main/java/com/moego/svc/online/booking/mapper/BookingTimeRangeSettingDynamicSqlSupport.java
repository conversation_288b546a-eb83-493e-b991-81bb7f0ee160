package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingTimeRangeSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    public static final BookingTimeRangeSetting bookingTimeRangeSetting = new BookingTimeRangeSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.id")
    public static final SqlColumn<Long> id = bookingTimeRangeSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.company_id")
    public static final SqlColumn<Long> companyId = bookingTimeRangeSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.business_id")
    public static final SqlColumn<Long> businessId = bookingTimeRangeSetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = bookingTimeRangeSetting.serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.is_customized")
    public static final SqlColumn<Boolean> isCustomized = bookingTimeRangeSetting.isCustomized;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.schedule_type")
    public static final SqlColumn<Integer> scheduleType = bookingTimeRangeSetting.scheduleType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.start_date")
    public static final SqlColumn<Date> startDate = bookingTimeRangeSetting.startDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.end_date")
    public static final SqlColumn<Date> endDate = bookingTimeRangeSetting.endDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.updated_by")
    public static final SqlColumn<Long> updatedBy = bookingTimeRangeSetting.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.created_at")
    public static final SqlColumn<Date> createdAt = bookingTimeRangeSetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.updated_at")
    public static final SqlColumn<Date> updatedAt = bookingTimeRangeSetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_time_range_setting.capacity_setting_id")
    public static final SqlColumn<Long> capacitySettingId = bookingTimeRangeSetting.capacitySettingId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_time_range_setting")
    public static final class BookingTimeRangeSetting extends AliasableSqlTable<BookingTimeRangeSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Boolean> isCustomized = column("is_customized", JDBCType.BIT);

        public final SqlColumn<Integer> scheduleType = column("schedule_type", JDBCType.INTEGER);

        public final SqlColumn<Date> startDate = column("start_date", JDBCType.DATE);

        public final SqlColumn<Date> endDate = column("end_date", JDBCType.DATE);

        public final SqlColumn<Long> updatedBy = column("updated_by", JDBCType.BIGINT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> capacitySettingId = column("capacity_setting_id", JDBCType.BIGINT);

        public BookingTimeRangeSetting() {
            super("booking_time_range_setting", BookingTimeRangeSetting::new);
        }
    }
}
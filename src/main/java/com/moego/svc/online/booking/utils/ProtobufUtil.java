package com.moego.svc.online.booking.utils;

import com.google.type.Date;
import jakarta.annotation.Nonnull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2025/3/27
 */
public final class ProtobufUtil {
    private ProtobufUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Convert yyyy-MM-dd date string to Google Protobuf Date.
     *
     * @param date yyyy-MM-dd date string
     * @return Google Protobuf Date
     */
    @Nonnull
    public static Date toProtobufDate(@Nonnull String date) {
        return toProtobufDate(LocalDate.parse(date));
    }

    /**
     * Convert {@link LocalDate} to Google Protobuf Date.
     *
     * @param localDate LocalDate
     * @return Google Protobuf Date
     */
    @Nonnull
    public static Date toProtobufDate(@Nonnull LocalDate localDate) {
        return Date.newBuilder()
                .setYear(localDate.getYear())
                .setMonth(localDate.getMonthValue())
                .setDay(localDate.getDayOfMonth())
                .build();
    }

    /**
     * Convert Google Protobuf Date to {@link LocalDate}.
     *
     * @param date Google Protobuf Date
     * @return LocalDate
     */
    @Nonnull
    public static LocalDate toLocalDate(@Nonnull Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }
}

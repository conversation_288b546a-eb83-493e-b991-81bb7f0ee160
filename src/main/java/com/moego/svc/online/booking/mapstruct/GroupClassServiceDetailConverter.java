package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.GroupClassServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateGroupClassServiceDetailRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.GroupClassServiceDetail;
import java.util.Optional;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class})
public interface GroupClassServiceDetailConverter {
    GroupClassServiceDetailConverter INSTANCE = Mappers.getMapper(GroupClassServiceDetailConverter.class);

    @Mapping(target = "specificDates", ignore = true)
    GroupClassServiceDetailModel entityToModel(GroupClassServiceDetail entity);

    @Mapping(target = "specificDates", ignore = true)
    GroupClassServiceDetail createRequestToEntity(CreateGroupClassServiceDetailRequest createRequest);

    @AfterMapping
    default void setSpecificDates(
            GroupClassServiceDetail source, @MappingTarget GroupClassServiceDetailModel.Builder target) {
        Optional.ofNullable(source.getSpecificDates())
                .filter(StringUtils::hasText)
                .map(e -> JsonUtil.toList(e, String.class))
                .ifPresent(target::addAllSpecificDates);
    }

    @AfterMapping
    default void setSpecificDates(
            CreateGroupClassServiceDetailRequest source, @MappingTarget GroupClassServiceDetail target) {
        target.setSpecificDates(JsonUtil.toJson(source.getSpecificDatesList()));
    }
}

package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.GroupClassServiceDetailDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.GroupClassServiceDetail;
import com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface GroupClassServiceDetailMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<GroupClassServiceDetailMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    BasicColumn[] selectList = BasicColumn.columnList(id, bookingRequestId, petId, classInstanceId, staffId, serviceId, servicePrice, specificDates, startTime, endTime, durationPerSession, createdAt, updatedAt, deletedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<GroupClassServiceDetail> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<GroupClassServiceDetail> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="GroupClassServiceDetailResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="booking_request_id", property="bookingRequestId", jdbcType=JdbcType.BIGINT),
        @Result(column="pet_id", property="petId", jdbcType=JdbcType.BIGINT),
        @Result(column="class_instance_id", property="classInstanceId", jdbcType=JdbcType.BIGINT),
        @Result(column="staff_id", property="staffId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_id", property="serviceId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_price", property="servicePrice", jdbcType=JdbcType.NUMERIC),
        @Result(column="specific_dates", property="specificDates", typeHandler=StringToJsonbTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="start_time", property="startTime", jdbcType=JdbcType.INTEGER),
        @Result(column="end_time", property="endTime", jdbcType=JdbcType.INTEGER),
        @Result(column="duration_per_session", property="durationPerSession", jdbcType=JdbcType.INTEGER),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="deleted_at", property="deletedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<GroupClassServiceDetail> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("GroupClassServiceDetailResult")
    Optional<GroupClassServiceDetail> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int insertMultiple(Collection<GroupClassServiceDetail> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, groupClassServiceDetail, c ->
            c.map(bookingRequestId).toProperty("bookingRequestId")
            .map(petId).toProperty("petId")
            .map(classInstanceId).toProperty("classInstanceId")
            .map(staffId).toProperty("staffId")
            .map(serviceId).toProperty("serviceId")
            .map(servicePrice).toProperty("servicePrice")
            .map(specificDates).toProperty("specificDates")
            .map(startTime).toProperty("startTime")
            .map(endTime).toProperty("endTime")
            .map(durationPerSession).toProperty("durationPerSession")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
            .map(deletedAt).toProperty("deletedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int insertSelective(GroupClassServiceDetail row) {
        return MyBatis3Utils.insert(this::insert, row, groupClassServiceDetail, c ->
            c.map(bookingRequestId).toPropertyWhenPresent("bookingRequestId", row::getBookingRequestId)
            .map(petId).toPropertyWhenPresent("petId", row::getPetId)
            .map(classInstanceId).toPropertyWhenPresent("classInstanceId", row::getClassInstanceId)
            .map(staffId).toPropertyWhenPresent("staffId", row::getStaffId)
            .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
            .map(servicePrice).toPropertyWhenPresent("servicePrice", row::getServicePrice)
            .map(specificDates).toPropertyWhenPresent("specificDates", row::getSpecificDates)
            .map(startTime).toPropertyWhenPresent("startTime", row::getStartTime)
            .map(endTime).toPropertyWhenPresent("endTime", row::getEndTime)
            .map(durationPerSession).toPropertyWhenPresent("durationPerSession", row::getDurationPerSession)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
            .map(deletedAt).toPropertyWhenPresent("deletedAt", row::getDeletedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default Optional<GroupClassServiceDetail> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default List<GroupClassServiceDetail> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default List<GroupClassServiceDetail> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default Optional<GroupClassServiceDetail> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, groupClassServiceDetail, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    static UpdateDSL<UpdateModel> updateAllColumns(GroupClassServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalTo(row::getBookingRequestId)
                .set(petId).equalTo(row::getPetId)
                .set(classInstanceId).equalTo(row::getClassInstanceId)
                .set(staffId).equalTo(row::getStaffId)
                .set(serviceId).equalTo(row::getServiceId)
                .set(servicePrice).equalTo(row::getServicePrice)
                .set(specificDates).equalTo(row::getSpecificDates)
                .set(startTime).equalTo(row::getStartTime)
                .set(endTime).equalTo(row::getEndTime)
                .set(durationPerSession).equalTo(row::getDurationPerSession)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt)
                .set(deletedAt).equalTo(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(GroupClassServiceDetail row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
                .set(petId).equalToWhenPresent(row::getPetId)
                .set(classInstanceId).equalToWhenPresent(row::getClassInstanceId)
                .set(staffId).equalToWhenPresent(row::getStaffId)
                .set(serviceId).equalToWhenPresent(row::getServiceId)
                .set(servicePrice).equalToWhenPresent(row::getServicePrice)
                .set(specificDates).equalToWhenPresent(row::getSpecificDates)
                .set(startTime).equalToWhenPresent(row::getStartTime)
                .set(endTime).equalToWhenPresent(row::getEndTime)
                .set(durationPerSession).equalToWhenPresent(row::getDurationPerSession)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
                .set(deletedAt).equalToWhenPresent(row::getDeletedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    default int updateByPrimaryKeySelective(GroupClassServiceDetail row) {
        return update(c ->
            c.set(bookingRequestId).equalToWhenPresent(row::getBookingRequestId)
            .set(petId).equalToWhenPresent(row::getPetId)
            .set(classInstanceId).equalToWhenPresent(row::getClassInstanceId)
            .set(staffId).equalToWhenPresent(row::getStaffId)
            .set(serviceId).equalToWhenPresent(row::getServiceId)
            .set(servicePrice).equalToWhenPresent(row::getServicePrice)
            .set(specificDates).equalToWhenPresent(row::getSpecificDates)
            .set(startTime).equalToWhenPresent(row::getStartTime)
            .set(endTime).equalToWhenPresent(row::getEndTime)
            .set(durationPerSession).equalToWhenPresent(row::getDurationPerSession)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .set(deletedAt).equalToWhenPresent(row::getDeletedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.DaycareServiceDetailDynamicSqlSupport.daycareServiceDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.DaycareServiceDetail;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.DaycareServiceDetailMapper;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class DaycareServiceDetailService {

    private final DaycareServiceDetailMapper daycareServiceDetailMapper;
    private final BookingRequestMapper bookingRequestMapper;
    private final ServiceHelper serviceHelper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public DaycareServiceDetail get(long id) {
        return daycareServiceDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(DaycareServiceDetail entity) {

        populate(entity);

        daycareServiceDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private void populate(DaycareServiceDetail entity) {

        check(entity);

        var bookingRequest = bookingRequestMapper
                .selectByPrimaryKey(entity.getBookingRequestId())
                .orElseThrow(() -> ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "BookingRequest not found: " + entity.getBookingRequestId()));

        var builder = MustGetCustomizedServiceParam.builder();
        builder.serviceId(entity.getServiceId());
        builder.companyId(bookingRequest.getCompanyId());
        builder.businessId(bookingRequest.getBusinessId());
        builder.petId(entity.getPetId());

        var service = serviceHelper.mustGetCustomizedService(builder.build());

        if (entity.getServicePrice() == null) {
            entity.setServicePrice(BigDecimal.valueOf(service.getPrice()));
        }
        if (entity.getTaxId() == null) {
            entity.setTaxId(service.getTaxId());
        }
        if (entity.getMaxDuration() == null) {
            entity.setMaxDuration(service.getMaxDuration());
        }
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(DaycareServiceDetail entity) {
        entity.setUpdatedAt(new Date());
        return daycareServiceDetailMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return daycareServiceDetailMapper.update(c -> c.set(daycareServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(daycareServiceDetail.id, isEqualTo(id))
                .and(daycareServiceDetail.deletedAt, isNull()));
    }

    /**
     * List daycare service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId bookingRequestId
     * @return list of daycare service detail
     */
    public List<DaycareServiceDetail> listByBookingRequestId(long bookingRequestId) {
        return daycareServiceDetailMapper.select(
                c -> c.where(daycareServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(daycareServiceDetail.deletedAt, isNull()));
    }

    /**
     * List daycare service detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of bookingRequestId
     * @return list of daycare service detail
     */
    public List<DaycareServiceDetail> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return List.of();
        }
        return daycareServiceDetailMapper.select(
                c -> c.where(daycareServiceDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(daycareServiceDetail.deletedAt, isNull()));
    }

    private static void check(DaycareServiceDetail entity) {
        if (!isNormal(entity.getBookingRequestId()))
            throw bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        if (!isNormal(entity.getPetId())) throw bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        if (!isNormal(entity.getServiceId())) throw bizException(Code.CODE_PARAMS_ERROR, "serviceId is required");
    }
}

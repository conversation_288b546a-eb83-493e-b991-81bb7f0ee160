package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.MedicationDynamicSqlSupport.medication;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.svc.online.booking.entity.Medication;
import com.moego.svc.online.booking.mapper.MedicationMapper;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class MedicationService {

    private final MedicationMapper medicationMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public Medication get(long id) {
        return medicationMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(Medication entity) {
        medicationMapper.insertSelective(entity);
        return entity.getId();
    }

    @Transactional
    public int insertMultiple(List<Medication> records) {
        if (CollectionUtils.isEmpty(records)) {
            return 0;
        }
        records.forEach(medicationMapper::insertSelective);
        return records.size();
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(Medication entity) {
        entity.setUpdatedAt(new Date());
        return medicationMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return medicationMapper.update(c -> c.set(medication.deletedAt)
                .equalTo(new Date())
                .where(medication.id, isEqualTo(id))
                .and(medication.deletedAt, isNull()));
    }

    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return medicationMapper.update(c -> c.set(medication.deletedAt)
                .equalTo(new Date())
                .where(medication.id, isIn(ids))
                .and(medication.deletedAt, isNull()));
    }

    /**
     * Get Medication by serviceDetailId, not include deleted record.
     *
     * @param serviceDetailId serviceDetailId
     * @return Medication
     */
    @Nullable
    public Medication getByServiceDetailId(long serviceDetailId) {
        return medicationMapper
                .selectOne(c -> c.where(medication.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(medication.deletedAt, isNull()))
                .orElse(null);
    }

    /**
     * List Medication by booking request id, not include deleted record.
     *
     * @param bookingRequestId booking request id
     * @return list of Medication
     */
    public List<Medication> listByBookingRequestId(long bookingRequestId) {
        return medicationMapper.select(c -> c.where(medication.bookingRequestId, isEqualTo(bookingRequestId))
                .and(medication.deletedAt, isNull()));
    }

    /**
     * List Medication by booking request id, not include deleted record.
     *
     * @param bookingRequestIds list of booking request id
     * @param serviceItemType service item type
     * @return bookingRequestId -> serviceDetailId -> List<Medication>
     */
    public Map<Long, Map<Long, List<Medication>>> listByBookingRequestId(
            List<Long> bookingRequestIds, Integer serviceItemType) {
        if (CollectionUtils.isEmpty(bookingRequestIds) || serviceItemType == null) {
            return Map.of();
        }
        return medicationMapper
                .select(c -> c.where(medication.bookingRequestId, isIn(bookingRequestIds))
                        .and(medication.deletedAt, isNull())
                        .and(medication.serviceDetailType, isEqualTo(serviceItemType)))
                .stream()
                .collect(Collectors.groupingBy(
                        Medication::getBookingRequestId, Collectors.groupingBy(Medication::getServiceDetailId)));
    }

    private static void addFieldToNote(StringJoiner noteBuilder, String label, String value) {
        if (StringUtils.hasText(value)) {
            noteBuilder.add(String.format("%s: %s", label, value));
        }
    }
}

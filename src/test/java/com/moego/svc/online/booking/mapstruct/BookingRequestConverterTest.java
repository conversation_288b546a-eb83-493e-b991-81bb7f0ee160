package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.online_booking.v1.CountBookingRequestsRequest;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;

class BookingRequestConverterTest {

    @Test
    void countRequestToFilterDTO() {
        final var fromTime = 1730707899L;
        final var toTime = 1730794299L;
        final CountBookingRequestsRequest request = CountBookingRequestsRequest.newBuilder()
                .setTenant(
                        Tenant.newBuilder().setCompanyId(1L).setBusinessId(11L).build())
                .setFilters(CountBookingRequestsRequest.Filters.newBuilder()
                        .setCreatedTimeRange(Interval.newBuilder()
                                .setStartTime(Timestamp.newBuilder()
                                        .setSeconds(fromTime)
                                        .build())
                                .setEndTime(Timestamp.newBuilder()
                                        .setSeconds(toTime)
                                        .build())
                                .build())
                        .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.SCHEDULED))
                        .build())
                .build();

        final BookingRequestFilterDTO actual = BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request);

        final BookingRequestFilterDTO expected = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setBusinessIds(List.of(11L))
                .setStatuses(List.of(1, 3))
                .setCreatedAfter(new Date(fromTime * 1000))
                .setCreatedBefore(new Date(toTime * 1000));

        assertEquals(expected, actual);
    }

    @Test
    void countRequestToFilterDTOWithoutBusinessId() {
        final var fromTime = 1730707899L;
        final var toTime = 1730794299L;
        final CountBookingRequestsRequest request = CountBookingRequestsRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(1L).build())
                .setFilters(CountBookingRequestsRequest.Filters.newBuilder()
                        .setCreatedTimeRange(Interval.newBuilder()
                                .setStartTime(Timestamp.newBuilder()
                                        .setSeconds(fromTime)
                                        .build())
                                .setEndTime(Timestamp.newBuilder()
                                        .setSeconds(toTime)
                                        .build())
                                .build())
                        .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.SCHEDULED))
                        .build())
                .build();

        final BookingRequestFilterDTO actual = BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request);

        final BookingRequestFilterDTO expected = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setStatuses(List.of(1, 3))
                .setCreatedAfter(new Date(fromTime * 1000))
                .setCreatedBefore(new Date(toTime * 1000));

        assertEquals(expected, actual);
    }

    @Test
    void countRequestToFilterDTOOnlyTenant() {
        final CountBookingRequestsRequest request = CountBookingRequestsRequest.newBuilder()
                .setTenant(
                        Tenant.newBuilder().setCompanyId(1L).setBusinessId(2L).build())
                .build();

        final BookingRequestFilterDTO actual = BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request);

        final BookingRequestFilterDTO expected =
                new BookingRequestFilterDTO().setCompanyId(1L).setBusinessIds(List.of(2L));

        assertEquals(expected, actual);
    }

    @Test
    void countRequestToFilterDTOWithoutTimeRange() {
        final CountBookingRequestsRequest request = CountBookingRequestsRequest.newBuilder()
                .setTenant(
                        Tenant.newBuilder().setCompanyId(1L).setBusinessId(2L).build())
                .setFilters(CountBookingRequestsRequest.Filters.newBuilder()
                        .addAllStatuses(List.of(BookingRequestStatus.SUBMITTED, BookingRequestStatus.SCHEDULED))
                        .build())
                .build();

        final BookingRequestFilterDTO actual = BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request);

        final BookingRequestFilterDTO expected = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setBusinessIds(List.of(2L))
                .setStatuses(List.of(1, 3));

        assertEquals(expected, actual);
    }

    @Test
    void countRequestToFilterDTOWithoutStatuses() {
        final var fromTime = 1730707899L;
        final var toTime = 1730794299L;
        final CountBookingRequestsRequest request = CountBookingRequestsRequest.newBuilder()
                .setTenant(
                        Tenant.newBuilder().setCompanyId(1L).setBusinessId(11L).build())
                .setFilters(CountBookingRequestsRequest.Filters.newBuilder()
                        .setCreatedTimeRange(Interval.newBuilder()
                                .setStartTime(Timestamp.newBuilder()
                                        .setSeconds(fromTime)
                                        .build())
                                .setEndTime(Timestamp.newBuilder()
                                        .setSeconds(toTime)
                                        .build())
                                .build())
                        .build())
                .build();

        final BookingRequestFilterDTO actual = BookingRequestConverter.INSTANCE.countRequestToFilterDTO(request);

        final BookingRequestFilterDTO expected = new BookingRequestFilterDTO()
                .setCompanyId(1L)
                .setBusinessIds(List.of(11L))
                .setCreatedAfter(new Date(fromTime * 1000))
                .setCreatedBefore(new Date(toTime * 1000));

        assertEquals(expected, actual);
    }

    @Test
    void boardingAddOnToSelectedServiceAddOnDef_EverydayAddOn() {
        // Arrange
        BoardingAddOnDetailModel model = BoardingAddOnDetailModel.newBuilder()
                .setAddOnId(123L)
                .setServicePrice(25.0)
                .setQuantityPerDay(2)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY)
                .build();

        String startDate = "2024-04-01";
        Integer startTime = 540; // 9:00 AM
        Long associatedServiceId = 456L;

        // Act
        SelectedAddOnDef result = BookingRequestConverter.INSTANCE.boardingAddOnToSelectedServiceAddOnDef(
                model, startDate, startTime, associatedServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(123L, result.getAddOnId());
        assertEquals(startDate, result.getStartDate());
        assertEquals(startTime, result.getStartTime());
        assertEquals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY, result.getAddonDateType());
        assertEquals(25.0, result.getServicePrice());
        assertEquals(456L, result.getAssociatedServiceId());
        assertEquals(2, result.getQuantityPerDay());
    }

    @Test
    void boardingAddOnToSelectedServiceAddOnDef_SpecificDates() {
        // Arrange
        BoardingAddOnDetailModel model = BoardingAddOnDetailModel.newBuilder()
                .setAddOnId(123L)
                .setServicePrice(25.0)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .addAllSpecificDates(Arrays.asList("2024-04-01", "2024-04-02"))
                .setQuantityPerDay(1)
                .build();

        String startDate = "2024-04-01";
        Integer startTime = 540;
        Long associatedServiceId = 456L;

        // Act
        SelectedAddOnDef result = BookingRequestConverter.INSTANCE.boardingAddOnToSelectedServiceAddOnDef(
                model, startDate, startTime, associatedServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE, result.getAddonDateType());
        assertEquals(2, result.getSpecificDatesCount());
        assertTrue(result.getSpecificDatesList().contains("2024-04-01"));
        assertTrue(result.getSpecificDatesList().contains("2024-04-02"));
    }

    @Test
    void boardingAddOnToSelectedServiceAddOnDef_EverydayIncludeCheckoutDay() {
        // Arrange
        BoardingAddOnDetailModel model = BoardingAddOnDetailModel.newBuilder()
                .setAddOnId(123L)
                .setServicePrice(25.0)
                .setIsEveryday(false)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY)
                .setQuantityPerDay(1)
                .build();

        String startDate = "2024-04-01";
        Integer startTime = 540;
        Long associatedServiceId = 456L;

        // Act
        SelectedAddOnDef result = BookingRequestConverter.INSTANCE.boardingAddOnToSelectedServiceAddOnDef(
                model, startDate, startTime, associatedServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(PetDetailDateType.PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY, result.getAddonDateType());
    }

    @Test
    void boardingAddOnToSelectedServiceAddOnDef_SpecificDay() {
        // Arrange
        BoardingAddOnDetailModel model = BoardingAddOnDetailModel.newBuilder()
                .setAddOnId(123L)
                .setServicePrice(25.0)
                .addAllSpecificDates(Arrays.asList("2024-04-01", "2024-04-02"))
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE)
                .setQuantityPerDay(1)
                .build();

        String startDate = "2024-04-01";
        Integer startTime = 540;
        Long associatedServiceId = 456L;

        // Act
        SelectedAddOnDef result = BookingRequestConverter.INSTANCE.boardingAddOnToSelectedServiceAddOnDef(
                model, startDate, startTime, associatedServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(PetDetailDateType.PET_DETAIL_DATE_SPECIFIC_DATE, result.getAddonDateType());
    }

    @Test
    void boardingToSelectedServiceDef_withValidInputs_shouldReturnSelectedServiceDef() {
        var model = BoardingServiceDetailModel.newBuilder()
                .setServiceId(1L)
                .setStartDate("2023-10-01")
                .setEndDate("2023-10-10")
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .build();
        Long lodgingId = 2L;
        var feedings = List.of(FeedingModel.newBuilder()
                .setAmountStr("amountStr")
                .setUnit("unit")
                .setFoodType("foodType")
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setTime(600)
                        .setLabel("label value")
                        .build())
                .build());
        var medications = List.of(MedicationModel.newBuilder()
                .setAmountStr("medication amountStr")
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setTime(660)
                        .setLabel("medication label value")
                        .build())
                .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE)
                        .addAllSpecificDates(List.of("2023-10-01", "2023-10-03"))
                        .build())
                .build());

        var result =
                BookingRequestConverter.INSTANCE.boardingToSelectedServiceDef(model, lodgingId, feedings, medications);

        var expect = SelectedServiceDef.newBuilder()
                .setServiceId(1L)
                .setStartDate("2023-10-01")
                .setEndDate("2023-10-10")
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .addFeedings(AppointmentPetFeedingScheduleDef.newBuilder()
                        .setFeedingAmount("amountStr")
                        .setFeedingUnit("unit")
                        .setFeedingType("foodType")
                        .setFeedingSource("foodSource")
                        .setFeedingInstruction("instruction")
                        .setFeedingNote("note")
                        .addFeedingTimes(BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(600)
                                .putExtraJson("label", "label value")
                                .build())
                        .build())
                .addMedications(AppointmentPetMedicationScheduleDef.newBuilder()
                        .setMedicationAmount("medication amountStr")
                        .setMedicationUnit("medication unit")
                        .setMedicationName("medication name")
                        .setMedicationNote("medication notes")
                        .addMedicationTimes(BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(660)
                                .putExtraJson("label", "medication label value")
                                .build())
                        .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                                .setDateType(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE)
                                .addAllSpecificDates(List.of("2023-10-01", "2023-10-03"))
                                .build())
                        .build())
                .setLodgingId(2L)
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void boardingToSelectedServiceDef_withValueUnset_shouldReturnSelectedServiceDefWithoutLodgingId() {
        var model = BoardingServiceDetailModel.newBuilder()
                .setServiceId(1L)
                .setStartDate("2023-10-01")
                .setEndDate("2023-10-10")
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .build();
        List<FeedingModel> feedings = List.of();
        List<MedicationModel> medications = List.of();

        var result = BookingRequestConverter.INSTANCE.boardingToSelectedServiceDef(model, 0L, feedings, medications);

        var expect = SelectedServiceDef.newBuilder()
                .setServiceId(1L)
                .setStartDate("2023-10-01")
                .setEndDate("2023-10-10")
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .setLodgingId(0)
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void daycareToSelectedServiceDef_withValidInputs_shouldReturnSelectedServiceDef() {
        var model = DaycareServiceDetailModel.newBuilder()
                .setServiceId(1L)
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .addAllSpecificDates(List.of("2023-10-02", "2023-10-09"))
                .build();
        Long lodgingId = 2L;
        var feedings = List.of(FeedingModel.newBuilder()
                .setAmountStr("amountStr")
                .setUnit("unit")
                .setFoodType("foodType")
                .setFoodSource("foodSource")
                .setInstruction("instruction")
                .setNote("note")
                .addTime(FeedingModel.FeedingSchedule.newBuilder()
                        .setTime(600)
                        .setLabel("label value")
                        .build())
                .build());
        var medications = List.of(MedicationModel.newBuilder()
                .setAmountStr("medication amountStr")
                .setUnit("medication unit")
                .setMedicationName("medication name")
                .setNotes("medication notes")
                .addTime(MedicationModel.MedicationSchedule.newBuilder()
                        .setTime(660)
                        .setLabel("medication label value")
                        .build())
                .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                        .setDateType(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE)
                        .addAllSpecificDates(List.of("2023-10-01", "2023-10-03"))
                        .build())
                .build());

        var result = BookingRequestConverter.INSTANCE.daycareToSelectedServiceDef(
                model, "2023-10-01", "2023-10-10", lodgingId, feedings, medications);

        var expect = SelectedServiceDef.newBuilder()
                .setServiceId(1L)
                .setStartDate("2023-10-01")
                .setEndDate("2023-10-10")
                .setServiceTime(480)
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .addAllSpecificDates(List.of("2023-10-02", "2023-10-09"))
                .addFeedings(AppointmentPetFeedingScheduleDef.newBuilder()
                        .setFeedingAmount("amountStr")
                        .setFeedingUnit("unit")
                        .setFeedingType("foodType")
                        .setFeedingSource("foodSource")
                        .setFeedingInstruction("instruction")
                        .setFeedingNote("note")
                        .addFeedingTimes(BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(600)
                                .putExtraJson("label", "label value")
                                .build())
                        .build())
                .addMedications(AppointmentPetMedicationScheduleDef.newBuilder()
                        .setMedicationAmount("medication amountStr")
                        .setMedicationUnit("medication unit")
                        .setMedicationName("medication name")
                        .setMedicationNote("medication notes")
                        .addMedicationTimes(BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(660)
                                .putExtraJson("label", "medication label value")
                                .build())
                        .setSelectedDate(AppointmentPetMedicationScheduleDef.SelectedDateDef.newBuilder()
                                .setDateType(FeedingMedicationScheduleDateType.EVERYDAY_EXCEPT_CHECKOUT_DATE)
                                .addAllSpecificDates(List.of("2023-10-01", "2023-10-03"))
                                .build())
                        .build())
                .setLodgingId(2L)
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void daycareToSelectedServiceDef_withValueUnset_shouldReturnSelectedServiceDefWithoutLodgingId() {
        var model = DaycareServiceDetailModel.newBuilder()
                .setServiceId(1L)
                .setStartTime(540)
                .setEndTime(1020)
                .setServicePrice(100.0)
                .addAllSpecificDates(List.of("2023-10-02", "2023-10-09"))
                .build();
        List<FeedingModel> feedings = List.of();
        List<MedicationModel> medications = List.of();

        var result = BookingRequestConverter.INSTANCE.daycareToSelectedServiceDef(
                model, null, null, null, feedings, medications);

        var expect = SelectedServiceDef.newBuilder()
                .setServiceId(1L)
                .setStartTime(540)
                .setEndTime(1020)
                .setServiceTime(480)
                .setServicePrice(100.0)
                .addAllSpecificDates(List.of("2023-10-02", "2023-10-09"))
                .setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT)
                .build();

        assertThat(result).isEqualTo(expect);
    }
}

package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.PetToServiceDef;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreateAppointmentForOnlineBookingResponse;
import java.util.AbstractMap;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookingRequestModifyServiceTest {

    @Mock
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;

    @InjectMocks
    private BookingRequestModifyService bookingRequestModifyService;

    @Test
    void testCreateAppointment_Daycare() {
        // Arrange
        BookingRequestModel bookingRequest = createDaycareBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;
        List<PetToLodgingDef> petToLodgings = List.of(
                PetToLodgingDef.newBuilder().setPetId(21L).setLodgingUnitId(2L).build());
        List<PetToStaffDef> petToStaffs = List.of(PetToStaffDef.newBuilder()
                .setPetId(21L)
                .setServiceId(3L)
                .setStaffId(4L)
                .setStartTime(600)
                .build());

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(100L)
                        .build());

        // Act
        List<Long> result = bookingRequestModifyService.createAppointment(
                bookingRequest,
                companyId,
                businessId,
                staffId,
                appointmentStatus,
                petToLodgings,
                petToStaffs,
                List.of(),
                List.of());

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(100L));

        // Verify that createAppointmentForOnlineBooking was called twice (once for each date)
        verify(appointmentService, times(2)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateAppointment_Evaluation() {
        // Arrange
        BookingRequestModel bookingRequest = createDaycareBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;
        List<PetToServiceDef> petToServices = List.of(PetToServiceDef.newBuilder()
                .setPetId(21L)
                .setFromEvaluationServiceId(300L)
                .setToEvaluationServiceId(400L)
                .build());

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(100L)
                        .build());

        // Act
        List<Long> result = bookingRequestModifyService.createAppointment(
                bookingRequest,
                companyId,
                businessId,
                staffId,
                appointmentStatus,
                List.of(),
                List.of(),
                List.of(),
                petToServices);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(100L));

        // Verify that createAppointmentForOnlineBooking was called twice (once for each date)
        verify(appointmentService, times(2)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_Boarding() {
        // Arrange
        BookingRequestModel bookingRequest = createBoardingBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(200L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>());

        // Assert
        assertEquals(200L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_Grooming() {
        // Arrange
        BookingRequestModel bookingRequest = createGroomingBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(300L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>());

        // Assert
        assertEquals(300L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_Evaluation() {
        // Arrange
        BookingRequestModel bookingRequest = createEvaluationBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(400L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>());

        // Assert
        assertEquals(400L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_EvaluationWithPetToService() {
        // Arrange
        BookingRequestModel bookingRequest = createEvaluationBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;

        Map<AbstractMap.SimpleEntry<Long, Long>, Long> petToEvaluationMap =
                Map.of(new AbstractMap.SimpleEntry<>(21L, 33L), 34L);

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(400L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                Map.of(),
                Map.of(),
                Map.of(),
                petToEvaluationMap);

        // Assert
        assertEquals(400L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_Canceled() {
        // Arrange
        BookingRequestModel bookingRequest = createBoardingBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CANCELED;

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(500L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>(),
                new HashMap<>());

        // Assert
        assertEquals(500L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    @Test
    void testCreateOneAppointment_WithPetToLodgingAndPetToStaff() {
        // Arrange
        BookingRequestModel bookingRequest = createBoardingBookingRequest();
        Long companyId = 1L;
        Long businessId = 2L;
        Long staffId = 3L;
        AppointmentStatus appointmentStatus = AppointmentStatus.CONFIRMED;

        Map<Long, Long> petToLodgingMap = new HashMap<>();
        petToLodgingMap.put(21L, 100L);

        Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> petToStaffMap = new HashMap<>();
        petToStaffMap.put(
                new AbstractMap.SimpleEntry<>(21L, 31L),
                PetToStaffDef.newBuilder()
                        .setPetId(21L)
                        .setServiceId(31L)
                        .setStaffId(200L)
                        .build());

        when(appointmentService.createAppointmentForOnlineBooking(any()))
                .thenReturn(CreateAppointmentForOnlineBookingResponse.newBuilder()
                        .setAppointmentId(600L)
                        .build());

        // Act
        Long result = bookingRequestModifyService.createOneAppointment(
                companyId,
                businessId,
                staffId,
                bookingRequest,
                appointmentStatus,
                petToLodgingMap,
                petToStaffMap,
                new HashMap<>(),
                Map.of());

        // Assert
        assertEquals(600L, result);
        verify(appointmentService, times(1)).createAppointmentForOnlineBooking(any());
    }

    // Helper methods to create test data

    private BookingRequestModel createDaycareBookingRequest() {
        return BookingRequestModel.newBuilder()
                .setId(1L)
                .setCustomerId(10L)
                .setServiceTypeInclude(4)
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setDaycare(BookingRequestModel.DaycareService.newBuilder()
                                .setService(DaycareServiceDetailModel.newBuilder()
                                        .setPetId(20L)
                                        .setServiceId(30L)
                                        .addSpecificDates("2024-10-19")
                                        .addSpecificDates("2024-10-20")
                                        .build())
                                .addAddons(DaycareAddOnDetailModel.newBuilder()
                                        .setId(51L)
                                        .setIsEveryday(true)
                                        .addSpecificDates("2024-10-19")
                                        .setServicePrice(10.0)
                                        .setPetId(21L)
                                        .build())
                                .build())
                        .build())
                .setStartDate("2024-10-19")
                .setEndDate("2024-10-20")
                .setAdditionalNote("Additional note")
                .build();
    }

    private BookingRequestModel createBoardingBookingRequest() {
        return BookingRequestModel.newBuilder()
                .setId(2L)
                .setCustomerId(11L)
                .setServiceTypeInclude(2)
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setBoarding(BookingRequestModel.BoardingService.newBuilder()
                                .setService(BoardingServiceDetailModel.newBuilder()
                                        .setPetId(21L)
                                        .setServiceId(31L)
                                        .setLodgingId(41L)
                                        .build())
                                .addAddons(BoardingAddOnDetailModel.newBuilder()
                                        .setId(51L)
                                        .setIsEveryday(true)
                                        .addSpecificDates("2024-10-19")
                                        .setServicePrice(10.0)
                                        .setPetId(21L)
                                        .build())
                                .setFeeding(FeedingModel.newBuilder()
                                        .addTime(FeedingModel.FeedingSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .setAmount(2)
                                        .setUnit("cups")
                                        .setFoodType("Dry")
                                        .setFoodSource("Owner")
                                        .setInstruction("Feed with water")
                                        .build())
                                .setMedication(MedicationModel.newBuilder()
                                        .addTime(MedicationModel.MedicationSchedule.newBuilder()
                                                .setLabel("AM")
                                                .setTime(600)
                                                .build())
                                        .setAmount(1)
                                        .setUnit("pills")
                                        .setMedicationName("Painkiller")
                                        .setNotes("With food")
                                        .build())
                                .build())
                        .build())
                .setStartDate("2024-10-19")
                .setEndDate("2024-10-21")
                .build();
    }

    private BookingRequestModel createGroomingBookingRequest() {
        return BookingRequestModel.newBuilder()
                .setId(3L)
                .setCustomerId(12L)
                .setServiceTypeInclude(1)
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                                .setService(GroomingServiceDetailModel.newBuilder()
                                        .setPetId(22L)
                                        .setServiceId(32L)
                                        .build())
                                .addAddons(GroomingAddOnDetailModel.newBuilder()
                                        .setId(51L)
                                        .setServicePrice(10.0)
                                        .setPetId(21L)
                                        .build())
                                .build())
                        .build())
                .setStartDate("2024-10-19")
                .setEndDate("2024-10-19")
                .build();
    }

    private BookingRequestModel createEvaluationBookingRequest() {
        return BookingRequestModel.newBuilder()
                .setId(4L)
                .setCustomerId(13L)
                .setServiceTypeInclude(8)
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setEvaluation(BookingRequestModel.EvaluationService.newBuilder()
                                .setService(EvaluationTestDetailModel.newBuilder()
                                        .setPetId(21L)
                                        .setEvaluationId(33L)
                                        .build())
                                .build())
                        .build())
                .setStartDate("2024-10-19")
                .setEndDate("2024-10-19")
                .build();
    }

    @Test
    void buildSelectedEvaluationDef_withValidData_returnsCorrectSelectedEvaluationDef() {
        // Arrange
        var evaluationService = EvaluationTestDetailModel.newBuilder()
                .setPetId(21L)
                .setEvaluationId(33L)
                .setServicePrice(10.0)
                .setStartTime(600)
                .setEndTime(720)
                .setDuration(120)
                .build();
        var evaluationPetToStaffMap = Map.of(
                new AbstractMap.SimpleEntry<>(21L, 33L),
                PetToStaffDef.newBuilder()
                        .setPetId(21L)
                        .setServiceId(33L)
                        .setStaffId(34L)
                        .setStartTime(600)
                        .build());
        var petToEvaluationMap = Map.of(new AbstractMap.SimpleEntry<>(21L, 33L), 34L);

        // Act
        SelectedEvaluationDef result = bookingRequestModifyService.buildSelectedEvaluationDef(
                evaluationService, evaluationPetToStaffMap, petToEvaluationMap);

        var expect = SelectedEvaluationDef.newBuilder()
                .setServiceId(34L)
                .setStaffId(34L)
                .setStartTime(600)
                .setEndTime(720)
                .setServiceTime(120)
                .setServicePrice(10.0)
                .build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void buildSelectedEvaluationDef_withEmptyMaps_returnsDefaultSelectedEvaluationDef() {
        // Arrange
        var evaluationService = EvaluationTestDetailModel.newBuilder()
                .setPetId(21L)
                .setEvaluationId(33L)
                .setServicePrice(10.0)
                .setStartTime(600)
                .setEndTime(720)
                .setDuration(120)
                .build();
        Map<AbstractMap.SimpleEntry<Long, Long>, PetToStaffDef> evaluationPetToStaffMap = Map.of();
        Map<AbstractMap.SimpleEntry<Long, Long>, Long> petToEvaluationMap = Map.of();

        // Act
        SelectedEvaluationDef result = bookingRequestModifyService.buildSelectedEvaluationDef(
                evaluationService, evaluationPetToStaffMap, petToEvaluationMap);

        var expect = SelectedEvaluationDef.newBuilder()
                .setServiceId(33L)
                .setStartTime(600)
                .setEndTime(720)
                .setServiceTime(120)
                .setServicePrice(10.0)
                .build();

        assertThat(result).isEqualTo(expect);
    }
}

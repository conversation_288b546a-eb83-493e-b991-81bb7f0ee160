package com.moego.svc.online.booking.mapstruct;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.CapacityOverrideDef;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel;
import com.moego.idl.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange;
import com.moego.idl.models.online_booking.v1.LodgingAvailabilityDef;
import com.moego.svc.online.booking.entity.LodgingCapacityOverride;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import java.time.LocalDate;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LodgingCapacityConverter {

    LodgingCapacityConverter INSTANCE = Mappers.getMapper(LodgingCapacityConverter.class);

    default LodgingAvailabilityDef toLodgingAvailabilityDef(LodgingCapacitySetting entity) {
        if (entity == null) {
            return LodgingAvailabilityDef.getDefaultInstance();
        }
        return LodgingAvailabilityDef.newBuilder()
                .setIsCapacityLimited(entity.getIsCapacityLimited())
                .setCapacityLimit(entity.getCapacityLimit())
                .setAllowWaitlistSignups(entity.getAllowWaitlistSignups())
                .build();
    }

    default LodgingCapacitySetting toLodgingCapacitySetting(
            long companyId, long businessId, ServiceItemType serviceItemType, LodgingAvailabilityDef def) {
        LodgingCapacitySetting result = new LodgingCapacitySetting();
        result.setCompanyId(companyId);
        result.setBusinessId(businessId);
        result.setServiceItemType(serviceItemType.getNumber());
        result.setIsCapacityLimited(def.getIsCapacityLimited());
        result.setCapacityLimit(def.getCapacityLimit());
        if (def.hasAllowWaitlistSignups()) {
            result.setAllowWaitlistSignups(def.getAllowWaitlistSignups());
        }
        return result;
    }

    default List<CapacityOverrideModel> toCapacityOverrideModels(
            List<LodgingCapacityOverride> lodgingCapacityOverrideList, String businessCurrentDate) {
        return lodgingCapacityOverrideList.stream()
                .map(entity -> toCapacityOverrideModel(entity, businessCurrentDate))
                .toList();
    }

    default CapacityOverrideModel toCapacityOverrideModel(LodgingCapacityOverride entity, String businessCurrentDate) {
        boolean isActive = entity.getDateRanges().stream()
                .map(range -> DateConverter.INSTANCE.toLocalDate(range.getEndDate()))
                .anyMatch(endDate -> !endDate.isBefore(LocalDate.parse(businessCurrentDate)));
        return CapacityOverrideModel.newBuilder()
                .setId(entity.getId())
                .setCapacity(entity.getCapacity())
                .setUnitType(entity.getUnitType())
                .addAllDateRanges(entity.getDateRanges())
                .setIsActive(isActive)
                .build();
    }

    default LodgingCapacityOverride toLodgingCapacityOverrideEntity(
            Long settingId, CapacityOverrideDef capacityOverrideDef) {
        LodgingCapacityOverride result = new LodgingCapacityOverride();
        if (CommonUtil.isNormal(capacityOverrideDef.getId())) {
            result.setId(capacityOverrideDef.getId());
        }
        if (CommonUtil.isNormal(settingId)) {
            result.setSettingId(settingId);
        }
        result.setCapacity(capacityOverrideDef.getCapacity());
        result.setUnitType(capacityOverrideDef.getUnitType());
        result.setDateRanges(toCapacityDateRange(capacityOverrideDef.getDateRangesList()));
        return result;
    }

    List<CapacityDateRange> toCapacityDateRange(List<CapacityOverrideDef.CapacityDateRangeDef> dateRangeDefs);
}

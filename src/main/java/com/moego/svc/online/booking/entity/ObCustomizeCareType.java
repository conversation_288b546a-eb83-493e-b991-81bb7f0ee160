package com.moego.svc.online.booking.entity;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import jakarta.annotation.Generated;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table ob_customize_care_type
 */
public class ObCustomizeCareType {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.name")
    private String name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.company_id")
    private Long companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.business_id")
    private Long businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.description")
    private String description;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.icon")
    private String icon;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.image")
    private String image;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.sort")
    private Integer sort;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_type")
    private ServiceType serviceType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.is_all_service_applicable")
    private Boolean isAllServiceApplicable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.selected_services")
    private List<Long> selectedServices;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_item_type")
    private ServiceItemType serviceItemType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_by")
    private Long updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.deleted_at")
    private LocalDateTime deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.name")
    public void setName(String name) {
        this.name = name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.company_id")
    public Long getCompanyId() {
        return companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.company_id")
    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.business_id")
    public Long getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.business_id")
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.description")
    public String getDescription() {
        return description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.description")
    public void setDescription(String description) {
        this.description = description;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.icon")
    public String getIcon() {
        return icon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.icon")
    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.image")
    public String getImage() {
        return image;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.image")
    public void setImage(String image) {
        this.image = image;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.sort")
    public Integer getSort() {
        return sort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.sort")
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_type")
    public ServiceType getServiceType() {
        return serviceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_type")
    public void setServiceType(ServiceType serviceType) {
        this.serviceType = serviceType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.is_all_service_applicable")
    public Boolean getIsAllServiceApplicable() {
        return isAllServiceApplicable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.is_all_service_applicable")
    public void setIsAllServiceApplicable(Boolean isAllServiceApplicable) {
        this.isAllServiceApplicable = isAllServiceApplicable;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.selected_services")
    public List<Long> getSelectedServices() {
        return selectedServices;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.selected_services")
    public void setSelectedServices(List<Long> selectedServices) {
        this.selectedServices = selectedServices;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_item_type")
    public ServiceItemType getServiceItemType() {
        return serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.service_item_type")
    public void setServiceItemType(ServiceItemType serviceItemType) {
        this.serviceItemType = serviceItemType;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_by")
    public Long getUpdatedBy() {
        return updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_by")
    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.deleted_at")
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: ob_customize_care_type.deleted_at")
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", companyId=").append(companyId);
        sb.append(", businessId=").append(businessId);
        sb.append(", description=").append(description);
        sb.append(", icon=").append(icon);
        sb.append(", image=").append(image);
        sb.append(", sort=").append(sort);
        sb.append(", serviceType=").append(serviceType);
        sb.append(", isAllServiceApplicable=").append(isAllServiceApplicable);
        sb.append(", selectedServices=").append(selectedServices);
        sb.append(", serviceItemType=").append(serviceItemType);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", deletedAt=").append(deletedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        ObCustomizeCareType other = (ObCustomizeCareType) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getBusinessId() == null ? other.getBusinessId() == null : this.getBusinessId().equals(other.getBusinessId()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getIcon() == null ? other.getIcon() == null : this.getIcon().equals(other.getIcon()))
            && (this.getImage() == null ? other.getImage() == null : this.getImage().equals(other.getImage()))
            && (this.getSort() == null ? other.getSort() == null : this.getSort().equals(other.getSort()))
            && (this.getServiceType() == null ? other.getServiceType() == null : this.getServiceType().equals(other.getServiceType()))
            && (this.getIsAllServiceApplicable() == null ? other.getIsAllServiceApplicable() == null : this.getIsAllServiceApplicable().equals(other.getIsAllServiceApplicable()))
            && (this.getSelectedServices() == null ? other.getSelectedServices() == null : this.getSelectedServices().equals(other.getSelectedServices()))
            && (this.getServiceItemType() == null ? other.getServiceItemType() == null : this.getServiceItemType().equals(other.getServiceItemType()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()))
            && (this.getDeletedAt() == null ? other.getDeletedAt() == null : this.getDeletedAt().equals(other.getDeletedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: ob_customize_care_type")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getBusinessId() == null) ? 0 : getBusinessId().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getIcon() == null) ? 0 : getIcon().hashCode());
        result = prime * result + ((getImage() == null) ? 0 : getImage().hashCode());
        result = prime * result + ((getSort() == null) ? 0 : getSort().hashCode());
        result = prime * result + ((getServiceType() == null) ? 0 : getServiceType().hashCode());
        result = prime * result + ((getIsAllServiceApplicable() == null) ? 0 : getIsAllServiceApplicable().hashCode());
        result = prime * result + ((getSelectedServices() == null) ? 0 : getSelectedServices().hashCode());
        result = prime * result + ((getServiceItemType() == null) ? 0 : getServiceItemType().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        result = prime * result + ((getDeletedAt() == null) ? 0 : getDeletedAt().hashCode());
        return result;
    }
}
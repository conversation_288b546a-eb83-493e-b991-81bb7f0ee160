package com.moego.svc.online.booking.entity;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table booking_capacity_setting
 */
public class BookingCapacitySetting {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.name")
    private String name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.service_ids")
    private List<Long> serviceIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.is_all_service")
    private Boolean isAllService;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.created_at")
    private LocalDateTime createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.updated_at")
    private LocalDateTime updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.name")
    public void setName(String name) {
        this.name = name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.service_ids")
    public List<Long> getServiceIds() {
        return serviceIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.service_ids")
    public void setServiceIds(List<Long> serviceIds) {
        this.serviceIds = serviceIds;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.is_all_service")
    public Boolean getIsAllService() {
        return isAllService;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.is_all_service")
    public void setIsAllService(Boolean isAllService) {
        this.isAllService = isAllService;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.created_at")
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.created_at")
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.updated_at")
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.updated_at")
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", name=").append(name);
        sb.append(", serviceIds=").append(serviceIds);
        sb.append(", isAllService=").append(isAllService);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append("]");
        return sb.toString();
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BookingCapacitySetting other = (BookingCapacitySetting) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getServiceIds() == null ? other.getServiceIds() == null : this.getServiceIds().equals(other.getServiceIds()))
            && (this.getIsAllService() == null ? other.getIsAllService() == null : this.getIsAllService().equals(other.getIsAllService()))
            && (this.getCreatedAt() == null ? other.getCreatedAt() == null : this.getCreatedAt().equals(other.getCreatedAt()))
            && (this.getUpdatedAt() == null ? other.getUpdatedAt() == null : this.getUpdatedAt().equals(other.getUpdatedAt()));
    }

    @Override
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getServiceIds() == null) ? 0 : getServiceIds().hashCode());
        result = prime * result + ((getIsAllService() == null) ? 0 : getIsAllService().hashCode());
        result = prime * result + ((getCreatedAt() == null) ? 0 : getCreatedAt().hashCode());
        result = prime * result + ((getUpdatedAt() == null) ? 0 : getUpdatedAt().hashCode());
        return result;
    }
}
package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DateLimitType;
import com.moego.idl.models.online_booking.v1.DateRangeDef;
import com.moego.svc.online.booking.entity.BookingDateRangeSetting;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {DateConverter.class})
public interface DateRangeConverter {
    DateRangeConverter INSTANCE = Mappers.getMapper(DateRangeConverter.class);

    DateRangeDef entityToDef(BookingDateRangeSetting entity);

    BookingDateRangeSetting defToEntity(DateRangeDef def);

    default DateLimitType toLimitType(Integer value) {
        return DateLimitType.forNumber(value);
    }

    default Integer toInteger(DateLimitType limitType) {
        return limitType.getNumber();
    }
}

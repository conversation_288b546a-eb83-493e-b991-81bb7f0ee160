package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BlockCustomerDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BlockCustomer;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BlockCustomerMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BlockCustomerMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    BasicColumn[] selectList = BasicColumn.columnList(id, companyId, serviceItemType, customerId, isActive, updatedBy, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BlockCustomer> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BlockCustomer> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BlockCustomerResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="company_id", property="companyId", jdbcType=JdbcType.BIGINT),
        @Result(column="service_item_type", property="serviceItemType", jdbcType=JdbcType.INTEGER),
        @Result(column="customer_id", property="customerId", jdbcType=JdbcType.BIGINT),
        @Result(column="is_active", property="isActive", jdbcType=JdbcType.BIT),
        @Result(column="updated_by", property="updatedBy", jdbcType=JdbcType.BIGINT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BlockCustomer> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BlockCustomerResult")
    Optional<BlockCustomer> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int insertMultiple(Collection<BlockCustomer> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, blockCustomer, c ->
            c.map(companyId).toProperty("companyId")
            .map(serviceItemType).toProperty("serviceItemType")
            .map(customerId).toProperty("customerId")
            .map(isActive).toProperty("isActive")
            .map(updatedBy).toProperty("updatedBy")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int insertSelective(BlockCustomer row) {
        return MyBatis3Utils.insert(this::insert, row, blockCustomer, c ->
            c.map(companyId).toPropertyWhenPresent("companyId", row::getCompanyId)
            .map(serviceItemType).toPropertyWhenPresent("serviceItemType", row::getServiceItemType)
            .map(customerId).toPropertyWhenPresent("customerId", row::getCustomerId)
            .map(isActive).toPropertyWhenPresent("isActive", row::getIsActive)
            .map(updatedBy).toPropertyWhenPresent("updatedBy", row::getUpdatedBy)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default Optional<BlockCustomer> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default List<BlockCustomer> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default List<BlockCustomer> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default Optional<BlockCustomer> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, blockCustomer, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    static UpdateDSL<UpdateModel> updateAllColumns(BlockCustomer row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalTo(row::getCompanyId)
                .set(serviceItemType).equalTo(row::getServiceItemType)
                .set(customerId).equalTo(row::getCustomerId)
                .set(isActive).equalTo(row::getIsActive)
                .set(updatedBy).equalTo(row::getUpdatedBy)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BlockCustomer row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(companyId).equalToWhenPresent(row::getCompanyId)
                .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
                .set(customerId).equalToWhenPresent(row::getCustomerId)
                .set(isActive).equalToWhenPresent(row::getIsActive)
                .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: block_customer")
    default int updateByPrimaryKeySelective(BlockCustomer row) {
        return update(c ->
            c.set(companyId).equalToWhenPresent(row::getCompanyId)
            .set(serviceItemType).equalToWhenPresent(row::getServiceItemType)
            .set(customerId).equalToWhenPresent(row::getCustomerId)
            .set(isActive).equalToWhenPresent(row::getIsActive)
            .set(updatedBy).equalToWhenPresent(row::getUpdatedBy)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
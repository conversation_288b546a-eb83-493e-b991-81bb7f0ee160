package com.moego.svc.online.booking.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.AcceptCustomerSettingDynamicSqlSupport.acceptCustomerSetting;
import static com.moego.svc.online.booking.mapper.AcceptPetSettingDynamicSqlSupport.acceptPetSetting;
import static com.moego.svc.online.booking.mapper.BookingCapacitySettingDynamicSqlSupport.bookingCapacitySetting;
import static com.moego.svc.online.booking.mapper.BookingDateRangeSettingDynamicSqlSupport.bookingDateRangeSetting;
import static com.moego.svc.online.booking.mapper.BookingTimeRangeDetailDynamicSqlSupport.bookingTimeRangeDetail;
import static com.moego.svc.online.booking.mapper.BookingTimeRangeOverrideDynamicSqlSupport.bookingTimeRangeOverride;
import static com.moego.svc.online.booking.mapper.BookingTimeRangeSettingDynamicSqlSupport.bookingTimeRangeSetting;
import static com.moego.svc.online.booking.mapper.LodgingCapacityOverrideDynamicSqlSupport.lodgingCapacityOverride;
import static com.moego.svc.online.booking.mapper.LodgingCapacitySettingDynamicSqlSupport.lodgingCapacitySetting;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.moego.common.enums.BooleanEnum;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.AcceptCustomerType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeDef;
import com.moego.idl.models.online_booking.v1.AvailabilityType;
import com.moego.idl.models.online_booking.v1.BoardingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef;
import com.moego.idl.models.online_booking.v1.DateLimitType;
import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DaycareServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.EvaluationServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef;
import com.moego.idl.models.online_booking.v1.GroomingServiceAvailabilityModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef;
import com.moego.idl.models.online_booking.v1.ScheduleType;
import com.moego.idl.models.online_booking.v1.TimeAvailabilityType;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.svc.online.booking.entity.AcceptCustomerSetting;
import com.moego.svc.online.booking.entity.AcceptPetSetting;
import com.moego.svc.online.booking.entity.BookingCapacitySetting;
import com.moego.svc.online.booking.entity.BookingDateRangeSetting;
import com.moego.svc.online.booking.entity.BookingTimeRangeDetail;
import com.moego.svc.online.booking.entity.BookingTimeRangeOverride;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.entity.LodgingCapacityOverride;
import com.moego.svc.online.booking.entity.LodgingCapacitySetting;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.mapper.AcceptCustomerSettingMapper;
import com.moego.svc.online.booking.mapper.AcceptPetSettingMapper;
import com.moego.svc.online.booking.mapper.BookingCapacitySettingMapper;
import com.moego.svc.online.booking.mapper.BookingDateRangeSettingMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeDetailMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeOverrideMapper;
import com.moego.svc.online.booking.mapper.BookingTimeRangeSettingMapper;
import com.moego.svc.online.booking.mapper.LodgingCapacityOverrideMapper;
import com.moego.svc.online.booking.mapper.LodgingCapacitySettingMapper;
import com.moego.svc.online.booking.mapstruct.DateConverter;
import com.moego.svc.online.booking.mapstruct.DateRangeConverter;
import com.moego.svc.online.booking.mapstruct.LodgingCapacityConverter;
import com.moego.svc.online.booking.mapstruct.TimeRangeConverter;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class AvailabilitySettingService {
    private final IGroomingOnlineBookingService onlineBookingService;

    private final AcceptPetSettingMapper acceptPetSettingMapper;
    private final BookingDateRangeSettingMapper bookingDateRangeSettingMapper;
    private final BookingTimeRangeSettingMapper bookingTimeRangeSettingMapper;
    private final BookingTimeRangeDetailMapper bookingTimeRangeDetailMapper;
    private final BookingTimeRangeOverrideMapper bookingTimeRangeOverrideMapper;
    private final BookingCapacitySettingMapper bookingCapacitySettingMapper;
    private final AcceptCustomerSettingMapper acceptCustomerSettingMapper;
    private final LodgingCapacitySettingMapper lodgingCapacitySettingMapper;
    private final LodgingCapacityOverrideMapper lodgingCapacityOverrideMapper;

    // 9999-12-31
    private static final Date ENDLESS_DATE = new Date(253402185600000L);

    static final int EVALUATION_DEFAULT_PET_CAPACITY = 10;
    static final int TIME_RANGE_DEFAULT_START_AT = 540;
    static final int TIME_RANGE_DEFAULT_END_AT = 1080;
    private final BusinessHelper businessHelper;

    public BoardingServiceAvailabilityModel getBoardingServiceAvailability(long companyId, long businessId) {
        // query accept pet setting
        AcceptPetSetting acceptPetSetting = getAcceptPetSetting(companyId, businessId, ServiceItemType.BOARDING);

        // query booking date range setting
        BookingDateRangeSetting bookingDateRangeSetting =
                getBookingDateRangeSetting(companyId, businessId, ServiceItemType.BOARDING);

        // query arrival pick up time
        ArrivalPickUpTimeDef arrivalPickUpTimeRangeDef =
                getCalculateArrivalPickUpTimeRangeDef(companyId, businessId, ServiceItemType.BOARDING);

        // query accepted customer type
        AcceptCustomerSetting acceptCustomerSetting =
                getAcceptCustomerSetting(companyId, businessId, ServiceItemType.BOARDING);

        // query lodging capacity setting
        LodgingCapacitySetting lodgingCapacitySetting =
                getLodgingCapacitySetting(companyId, businessId, ServiceItemType.BOARDING);

        return BoardingServiceAvailabilityModel.newBuilder()
                .addAllAcceptedPetTypes(Arrays.stream(acceptPetSetting.getAcceptedPetTypes())
                        .map(PetType::forNumber)
                        .toList())
                .setBookingDateRange(DateRangeConverter.INSTANCE.entityToDef(bookingDateRangeSetting))
                .setArrivalPickUpTimeRange(arrivalPickUpTimeRangeDef)
                .setAcceptCustomerType(AcceptCustomerType.forNumber(acceptCustomerSetting.getAcceptedCustomerType()))
                .setLodgingAvailability(
                        LodgingCapacityConverter.INSTANCE.toLodgingAvailabilityDef(lodgingCapacitySetting))
                .build();
    }

    public DaycareServiceAvailabilityModel getDaycareServiceAvailability(long companyId, long businessId) {
        // query accept pet setting
        AcceptPetSetting acceptPetSetting = getAcceptPetSetting(companyId, businessId, ServiceItemType.DAYCARE);

        // query booking date range setting
        BookingDateRangeSetting bookingDateRangeSetting =
                getBookingDateRangeSetting(companyId, businessId, ServiceItemType.DAYCARE);

        // query arrival pick up time
        ArrivalPickUpTimeDef arrivalPickUpTimeRangeDef =
                getCalculateArrivalPickUpTimeRangeDef(companyId, businessId, ServiceItemType.DAYCARE);

        // query accepted customer type
        AcceptCustomerSetting acceptCustomerSetting =
                getAcceptCustomerSetting(companyId, businessId, ServiceItemType.DAYCARE);

        // query lodging capacity setting
        LodgingCapacitySetting lodgingCapacitySetting =
                getLodgingCapacitySetting(companyId, businessId, ServiceItemType.DAYCARE);

        List<LodgingCapacityOverride> lodgingCapacityOverrideList =
                getCapacityOverrideList(lodgingCapacitySetting.getId());

        Map<Long, String> locationDateTimeMap = businessHelper.batchGetBusinessDateTimeMap(List.of(businessId));

        return DaycareServiceAvailabilityModel.newBuilder()
                .addAllAcceptedPetTypes(Arrays.stream(acceptPetSetting.getAcceptedPetTypes())
                        .map(PetType::forNumber)
                        .toList())
                .setBookingDateRange(DateRangeConverter.INSTANCE.entityToDef(bookingDateRangeSetting))
                .setArrivalPickUpTimeRange(arrivalPickUpTimeRangeDef)
                .setAcceptCustomerType(AcceptCustomerType.forNumber(acceptCustomerSetting.getAcceptedCustomerType()))
                .setLodgingAvailability(
                        LodgingCapacityConverter.INSTANCE.toLodgingAvailabilityDef(lodgingCapacitySetting))
                .addAllCapacityOverrides(LodgingCapacityConverter.INSTANCE.toCapacityOverrideModels(
                        lodgingCapacityOverrideList,
                        locationDateTimeMap.getOrDefault(
                                businessId, LocalDate.now().toString())))
                .build();
    }

    public DogWalkingServiceAvailabilityModel getDogWalkingServiceAvailability(long companyId, long businessId) {
        // query booking date range setting
        var bookingDateRangeSetting = getBookingDateRangeSetting(companyId, businessId, ServiceItemType.DOG_WALKING);

        // query accepted customer type
        var acceptCustomerSetting = getAcceptCustomerSetting(companyId, businessId, ServiceItemType.DOG_WALKING);

        return DogWalkingServiceAvailabilityModel.newBuilder()
                .addAcceptedPetTypes(PetType.PET_TYPE_DOG) // only accept dog
                .setBookingDateRange(DateRangeConverter.INSTANCE.entityToDef(bookingDateRangeSetting))
                .setAcceptCustomerType(AcceptCustomerType.forNumber(acceptCustomerSetting.getAcceptedCustomerType()))
                .build();
    }

    public EvaluationServiceAvailabilityModel getEvaluationServiceAvailability(long companyId, long businessId) {
        // query booking date range setting
        BookingDateRangeSetting bookingDateRangeSetting =
                getBookingDateRangeSetting(companyId, businessId, ServiceItemType.EVALUATION);

        // query arrival pick up time
        ArrivalPickUpTimeDef arrivalPickUpTimeRangeDef =
                getCalculateArrivalPickUpTimeRangeDef(companyId, businessId, ServiceItemType.EVALUATION);

        return EvaluationServiceAvailabilityModel.newBuilder()
                .setBookingDateRange(DateRangeConverter.INSTANCE.entityToDef(bookingDateRangeSetting))
                .setArrivalPickUpTimeRange(arrivalPickUpTimeRangeDef)
                .build();
    }

    @Nullable
    public GroomingServiceAvailabilityModel getGroomingServiceAvailability(long companyId, long businessId) {
        // query accepted customer type
        AcceptCustomerSetting acceptCustomerSetting =
                getAcceptCustomerSetting(companyId, businessId, ServiceItemType.GROOMING);

        BookOnlineDTO obSetting = onlineBookingService.getOBSetting(Math.toIntExact(businessId));
        if (Objects.isNull(obSetting)) {
            log.error("Online Booking Setting not found, businessId: {}", businessId);
            return null;
        }
        var timeAvailabilityType = getTimeAvailabilityType(obSetting.getAvailableTimeType());
        var enableShiftSync = BooleanEnum.VALUE_TRUE.equals(obSetting.getAvailableTimeSync());

        return GroomingServiceAvailabilityModel.newBuilder()
                .setAcceptCustomerType(AcceptCustomerType.forNumber(acceptCustomerSetting.getAcceptedCustomerType()))
                .setTimeAvailabilityType(timeAvailabilityType)
                .setEnableShiftSync(enableShiftSync)
                .build();
    }

    @NotNull
    private TimeAvailabilityType getTimeAvailabilityType(final Byte availableTimeType) {
        if (Objects.isNull(availableTimeType)) {
            return TimeAvailabilityType.TIME_AVAILABILITY_TYPE_UNSPECIFIED;
        }
        var availabilityType = AvailabilityType.forNumber(availableTimeType);
        if (Objects.isNull(availabilityType)) {
            return TimeAvailabilityType.TIME_AVAILABILITY_TYPE_UNSPECIFIED;
        }
        return switch (availabilityType) {
            case AVAILABILITY_TYPE_BY_WORKING_HOURS -> TimeAvailabilityType.WORKING_HOUR;
            case AVAILABILITY_TYPE_BY_SLOTS -> TimeAvailabilityType.TIME_SLOT;
            case AVAILABILITY_TYPE_DISABLE_SELECT_TIME -> TimeAvailabilityType.DISABLE_OPTION;
            default -> TimeAvailabilityType.TIME_AVAILABILITY_TYPE_UNSPECIFIED;
        };
    }

    public Map<ServiceItemType, AcceptCustomerSetting> getAcceptCustomerSettingMap(
            long companyId, long businessId, List<ServiceItemType> serviceItemTypes) {
        if (CollectionUtils.isEmpty(serviceItemTypes)) {
            return Map.of();
        }
        return serviceItemTypes.stream()
                .collect(Collectors.toMap(
                        Function.identity(),
                        serviceItemType -> getAcceptCustomerSetting(companyId, businessId, serviceItemType)));
    }

    public AcceptCustomerSetting getAcceptCustomerSetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        Optional<AcceptCustomerSetting> result = acceptCustomerSettingMapper.selectOne(
                c -> c.where(acceptCustomerSetting.businessId, isEqualTo(businessId))
                        .and(acceptCustomerSetting.companyId, isEqualTo(companyId))
                        .and(acceptCustomerSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())));
        if (result.isPresent()) {
            return result.get();
        }

        // insert
        try {
            AcceptCustomerSetting setting = new AcceptCustomerSetting();
            setting.setCompanyId(companyId);
            setting.setBusinessId(businessId);
            setting.setServiceItemType(serviceItemType.getNumber());
            setting.setAcceptedCustomerType(AcceptCustomerType.BOTH_EXISTING_AND_NEW_CUSTOMER_VALUE);
            acceptCustomerSettingMapper.insertSelective(setting);
        } catch (DuplicateKeyException e) {
            log.warn(
                    "Duplicate key when inserting AcceptCustomerSetting for companyId={}, businessId={}, serviceItemType={}",
                    companyId,
                    businessId,
                    serviceItemType.getNumber());
        }

        // retry query
        Optional<AcceptCustomerSetting> retryResult = acceptCustomerSettingMapper.selectOne(
                c -> c.where(acceptCustomerSetting.businessId, isEqualTo(businessId))
                        .and(acceptCustomerSetting.companyId, isEqualTo(companyId))
                        .and(acceptCustomerSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())));
        if (retryResult.isPresent()) {
            return retryResult.get();
        } else {
            throw bizException(Code.CODE_SERVER_ERROR, "Failed to get AcceptCustomerSetting");
        }
    }

    public void batchSaveAcceptCustomerSetting(
            long companyId,
            long businessId,
            AcceptCustomerType acceptCustomerType,
            List<ServiceItemType> serviceItemTypes,
            Long staffId) {
        serviceItemTypes.forEach(serviceItemType -> {
            Optional<AcceptCustomerSetting> result = acceptCustomerSettingMapper.selectOne(
                    c -> c.where(acceptCustomerSetting.businessId, isEqualTo(businessId))
                            .and(acceptCustomerSetting.companyId, isEqualTo(companyId))
                            .and(acceptCustomerSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())));
            if (result.isPresent()) {
                AcceptCustomerSetting acceptCustomerSetting = result.get();
                acceptCustomerSetting.setAcceptedCustomerType(acceptCustomerType.getNumber());
                acceptCustomerSetting.setUpdatedAt(new Date());
                if (Objects.nonNull(staffId)) {
                    acceptCustomerSetting.setUpdateBy(staffId);
                }
                acceptCustomerSettingMapper.updateByPrimaryKeySelective(acceptCustomerSetting);
            } else {
                AcceptCustomerSetting acceptCustomerSetting = new AcceptCustomerSetting();
                acceptCustomerSetting.setCompanyId(companyId);
                acceptCustomerSetting.setBusinessId(businessId);
                acceptCustomerSetting.setServiceItemType(serviceItemType.getNumber());
                acceptCustomerSetting.setAcceptedCustomerType(acceptCustomerType.getNumber());
                if (Objects.nonNull(staffId)) {
                    acceptCustomerSetting.setUpdateBy(staffId);
                }
                acceptCustomerSettingMapper.insertSelective(acceptCustomerSetting);
            }
        });
    }

    @Transactional
    public void updateBoardingServiceAvailability(
            long companyId,
            long businessId,
            long staffId,
            BoardingServiceAvailabilityUpdateDef boardingServiceAvailabilityUpdateDef) {
        // update accept pet setting
        Long acceptPetSettingId = getAcceptPetSetting(companyId, businessId, ServiceItemType.BOARDING)
                .getId();
        if (acceptPetSettingId == null || acceptPetSettingId == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "AcceptPetSetting not found");
        }
        var acceptPetTypeIds = boardingServiceAvailabilityUpdateDef.getAcceptedPetTypesList().stream()
                .map(PetType::getNumber)
                .toList();
        acceptPetSettingMapper.update(c -> c.set(acceptPetSetting.acceptedPetTypes)
                .equalTo(acceptPetTypeIds.toArray(new Integer[0]))
                .where(acceptPetSetting.id, isEqualTo(acceptPetSettingId)));

        // update booking date range setting
        Long bookingDateRangeSettingId = getBookingDateRangeSetting(companyId, businessId, ServiceItemType.BOARDING)
                .getId();
        if (bookingDateRangeSettingId == null || bookingDateRangeSettingId == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "BookingDateRangeSetting not found");
        }
        BookingDateRangeSetting bookingDateRangeSettingToUpdate =
                DateRangeConverter.INSTANCE.defToEntity(boardingServiceAvailabilityUpdateDef.getAvailableDateRange());
        bookingDateRangeSettingMapper.update(c -> c.set(bookingDateRangeSetting.startDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getStartDateType())
                .set(bookingDateRangeSetting.maxStartDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxStartDateOffset())
                .set(bookingDateRangeSetting.specificStartDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificStartDate())
                .set(bookingDateRangeSetting.endDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getEndDateType())
                .set(bookingDateRangeSetting.maxEndDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxEndDateOffset())
                .set(bookingDateRangeSetting.specificEndDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificEndDate())
                .where(bookingDateRangeSetting.id, isEqualTo(bookingDateRangeSettingId)));

        // update arrival pick up time
        if (boardingServiceAvailabilityUpdateDef.hasArrivalPickUpTimeRange()) {
            updateTimeRange(
                    companyId,
                    businessId,
                    staffId,
                    boardingServiceAvailabilityUpdateDef.getArrivalPickUpTimeRange(),
                    ServiceItemType.BOARDING);
        }

        // update accepted customer type
        if (boardingServiceAvailabilityUpdateDef.hasAcceptCustomerType()) {
            updateAcceptedCustomerSetting(
                    companyId,
                    businessId,
                    staffId,
                    boardingServiceAvailabilityUpdateDef.getAcceptCustomerType(),
                    ServiceItemType.BOARDING);
        }

        // update lodging capacity setting
        if (boardingServiceAvailabilityUpdateDef.hasLodgingAvailability()) {
            upsertLodgingCapacitySetting(LodgingCapacityConverter.INSTANCE.toLodgingCapacitySetting(
                    companyId,
                    businessId,
                    ServiceItemType.BOARDING,
                    boardingServiceAvailabilityUpdateDef.getLodgingAvailability()));
        }
    }

    private void updateTimeRange(
            long companyId,
            long businessId,
            long staffId,
            ArrivalPickUpTimeDef arrivalPickUpTimeRange,
            ServiceItemType serviceItemType) {
        if (Objects.isNull(arrivalPickUpTimeRange)) {
            return;
        }

        BookingTimeRangeSetting newBookingTimeRangeSetting = new BookingTimeRangeSetting();
        newBookingTimeRangeSetting.setBusinessId(businessId);
        newBookingTimeRangeSetting.setCompanyId(companyId);
        newBookingTimeRangeSetting.setServiceItemType(serviceItemType.getNumber());
        newBookingTimeRangeSetting.setIsCustomized(arrivalPickUpTimeRange.getIsCustomized());
        newBookingTimeRangeSetting.setScheduleType(arrivalPickUpTimeRange.getScheduleTypeValue());
        newBookingTimeRangeSetting.setStartDate(
                Optional.ofNullable(DateConverter.INSTANCE.fromGoogleDate(arrivalPickUpTimeRange.getStartDate()))
                        .orElse(new Date()));
        newBookingTimeRangeSetting.setEndDate(
                Optional.ofNullable(DateConverter.INSTANCE.fromGoogleDate(arrivalPickUpTimeRange.getEndDate()))
                        .orElse(ENDLESS_DATE));
        newBookingTimeRangeSetting.setUpdatedBy(staffId);

        BookingTimeRangeDetail newArrivalBookingTimeRangeDetail =
                TimeRangeConverter.INSTANCE.defToPO(arrivalPickUpTimeRange.getArrivalTimeRange());
        BookingTimeRangeDetail newPickUpBookingTimeRangeDetail =
                TimeRangeConverter.INSTANCE.defToPO(arrivalPickUpTimeRange.getPickUpTimeRange());

        // 增加 capacity setting 以后，会有多条记录，无法兼容，只能返回 id 小的值，确保不报错
        var settingList = bookingTimeRangeSettingMapper.select(
                c -> c.where(bookingTimeRangeSetting.businessId, isEqualTo(businessId))
                        .and(bookingTimeRangeSetting.companyId, isEqualTo(companyId))
                        .and(bookingTimeRangeSetting.serviceItemType, isEqualTo(serviceItemType.getNumber()))
                        .orderBy(bookingDateRangeSetting.id));

        if (!CollectionUtils.isEmpty(settingList)) {
            var setting = settingList.get(0);
            newBookingTimeRangeSetting.setId(setting.getId());
            newBookingTimeRangeSetting.setUpdatedAt(new Date());
            bookingTimeRangeSettingMapper.updateByPrimaryKeySelective(newBookingTimeRangeSetting);
            bookingTimeRangeDetailMapper.update(c -> c.set(bookingTimeRangeDetail.firstWeek)
                    .equalTo(newArrivalBookingTimeRangeDetail.getFirstWeek())
                    .set(bookingTimeRangeDetail.secondWeek)
                    .equalTo(newArrivalBookingTimeRangeDetail.getSecondWeek())
                    .set(bookingTimeRangeDetail.thirdWeek)
                    .equalTo(newArrivalBookingTimeRangeDetail.getThirdWeek())
                    .set(bookingTimeRangeDetail.forthWeek)
                    .equalTo(newArrivalBookingTimeRangeDetail.getForthWeek())
                    .where(bookingTimeRangeDetail.settingId, isEqualTo(setting.getId()))
                    .and(bookingTimeRangeDetail.timeRangeType, isEqualTo(TimeRangeType.ARRIVAL_TIME_VALUE)));
            bookingTimeRangeDetailMapper.update(c -> c.set(bookingTimeRangeDetail.firstWeek)
                    .equalTo(newPickUpBookingTimeRangeDetail.getFirstWeek())
                    .set(bookingTimeRangeDetail.secondWeek)
                    .equalTo(newPickUpBookingTimeRangeDetail.getSecondWeek())
                    .set(bookingTimeRangeDetail.thirdWeek)
                    .equalTo(newPickUpBookingTimeRangeDetail.getThirdWeek())
                    .set(bookingTimeRangeDetail.forthWeek)
                    .equalTo(newPickUpBookingTimeRangeDetail.getForthWeek())
                    .where(bookingTimeRangeDetail.settingId, isEqualTo(setting.getId()))
                    .and(bookingTimeRangeDetail.timeRangeType, isEqualTo(TimeRangeType.PICK_UP_TIME_VALUE)));
        } else {
            bookingTimeRangeSettingMapper.insertSelective(newBookingTimeRangeSetting);
            long settingId = newBookingTimeRangeSetting.getId();
            newArrivalBookingTimeRangeDetail.setSettingId(settingId);
            newArrivalBookingTimeRangeDetail.setTimeRangeType(TimeRangeType.ARRIVAL_TIME_VALUE);
            bookingTimeRangeDetailMapper.insertSelective(newArrivalBookingTimeRangeDetail);
            newPickUpBookingTimeRangeDetail.setSettingId(settingId);
            newPickUpBookingTimeRangeDetail.setTimeRangeType(TimeRangeType.PICK_UP_TIME_VALUE);
            bookingTimeRangeDetailMapper.insertSelective(newPickUpBookingTimeRangeDetail);
        }
    }

    @Transactional
    public void updateDaycareServiceAvailability(
            long companyId,
            long businessId,
            long staffId,
            DaycareServiceAvailabilityUpdateDef daycareServiceAvailabilityUpdateDef) {
        // update accept pet setting
        Long acceptPetSettingId = getAcceptPetSetting(companyId, businessId, ServiceItemType.DAYCARE)
                .getId();
        var acceptPetTypeIds = daycareServiceAvailabilityUpdateDef.getAcceptedPetTypesList().stream()
                .map(PetType::getNumber)
                .toList();
        acceptPetSettingMapper.update(c -> c.set(acceptPetSetting.acceptedPetTypes)
                .equalTo(acceptPetTypeIds.toArray(new Integer[0]))
                .where(acceptPetSetting.id, isEqualTo(acceptPetSettingId)));

        // update booking date range setting
        Long bookingDateRangeSettingId = getBookingDateRangeSetting(companyId, businessId, ServiceItemType.DAYCARE)
                .getId();
        BookingDateRangeSetting bookingDateRangeSettingToUpdate =
                DateRangeConverter.INSTANCE.defToEntity(daycareServiceAvailabilityUpdateDef.getAvailableDateRange());
        bookingDateRangeSettingMapper.update(c -> c.set(bookingDateRangeSetting.startDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getStartDateType())
                .set(bookingDateRangeSetting.maxStartDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxStartDateOffset())
                .set(bookingDateRangeSetting.specificStartDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificStartDate())
                .set(bookingDateRangeSetting.endDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getEndDateType())
                .set(bookingDateRangeSetting.maxEndDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxEndDateOffset())
                .set(bookingDateRangeSetting.specificEndDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificEndDate())
                .where(bookingDateRangeSetting.id, isEqualTo(bookingDateRangeSettingId)));

        // update arrival pick up time
        if (daycareServiceAvailabilityUpdateDef.hasArrivalPickUpTimeRange()) {
            updateTimeRange(
                    companyId,
                    businessId,
                    staffId,
                    daycareServiceAvailabilityUpdateDef.getArrivalPickUpTimeRange(),
                    ServiceItemType.DAYCARE);
        }

        // update accepted customer type
        if (daycareServiceAvailabilityUpdateDef.hasAcceptCustomerType()) {
            updateAcceptedCustomerSetting(
                    companyId,
                    businessId,
                    staffId,
                    daycareServiceAvailabilityUpdateDef.getAcceptCustomerType(),
                    ServiceItemType.DAYCARE);
        }

        // update lodging capacity setting
        if (daycareServiceAvailabilityUpdateDef.hasLodgingAvailability()) {
            upsertLodgingCapacitySetting(LodgingCapacityConverter.INSTANCE.toLodgingCapacitySetting(
                    companyId,
                    businessId,
                    ServiceItemType.DAYCARE,
                    daycareServiceAvailabilityUpdateDef.getLodgingAvailability()));
        }
    }

    private void updateAcceptedCustomerSetting(
            long companyId,
            long businessId,
            long staffId,
            AcceptCustomerType acceptCustomerType,
            ServiceItemType serviceItemType) {
        if (Objects.isNull(acceptCustomerType)) {
            return;
        }

        AcceptCustomerSetting acceptCustomerSetting = getAcceptCustomerSetting(companyId, businessId, serviceItemType);
        acceptCustomerSetting.setAcceptedCustomerType(acceptCustomerType.getNumber());
        acceptCustomerSetting.setUpdateBy(staffId);
        acceptCustomerSetting.setUpdatedAt(new Date());
        acceptCustomerSettingMapper.updateByPrimaryKeySelective(acceptCustomerSetting);
    }

    private static AcceptPetSetting buildDefaultAcceptPetSetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        AcceptPetSetting acceptPetSetting = new AcceptPetSetting();
        acceptPetSetting.setCompanyId(companyId);
        acceptPetSetting.setBusinessId(businessId);
        acceptPetSetting.setServiceItemType(serviceItemType.getNumber());

        switch (serviceItemType) {
            case BOARDING, DAYCARE, EVALUATION, DOG_WALKING -> acceptPetSetting.setAcceptedPetTypes(
                    new Integer[] {PetType.PET_TYPE_DOG_VALUE});
            case GROOMING -> acceptPetSetting.setAcceptedPetTypes(Arrays.stream(PetType.values())
                    .filter(petType -> !petType.equals(PetType.UNRECOGNIZED))
                    .map(PetType::getNumber)
                    .toArray(Integer[]::new));
            default -> {}
        }
        return acceptPetSetting;
    }

    private BookingDateRangeSetting buildDefaultBookingDateRangeSetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        BookingDateRangeSetting bookingDateRangeSetting = new BookingDateRangeSetting();
        bookingDateRangeSetting.setCompanyId(companyId);
        bookingDateRangeSetting.setBusinessId(businessId);
        bookingDateRangeSetting.setServiceItemType(serviceItemType.getNumber());
        bookingDateRangeSetting.setStartDateType(DateLimitType.DATE_TYPE_OFFSET_VALUE);
        bookingDateRangeSetting.setMaxStartDateOffset(1);
        bookingDateRangeSetting.setEndDateType(DateLimitType.DATE_TYPE_OFFSET_VALUE);
        bookingDateRangeSetting.setMaxEndDateOffset(180);

        return bookingDateRangeSetting;
    }

    /**
     * 初始化 lodging capacity 配置
     */
    static LodgingCapacitySetting buildDefaultLodgingCapacitySetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {

        LodgingCapacitySetting setting = new LodgingCapacitySetting();
        setting.setCompanyId(companyId);
        setting.setBusinessId(businessId);
        setting.setServiceItemType(serviceItemType.getNumber());
        // boarding 新用户默认配置 limit 100；daycare 新用户默认不配置
        setting.setIsCapacityLimited(serviceItemType == ServiceItemType.BOARDING);
        setting.setCapacityLimit(100);
        setting.setAllowWaitlistSignups(false);
        return setting;
    }

    private AcceptPetSetting getAcceptPetSetting(long companyId, long businessId, ServiceItemType serviceItemType) {
        AcceptPetSetting setting = acceptPetSettingMapper
                .selectOne(c -> c.where(acceptPetSetting.businessId, isEqualTo(businessId))
                        .and(acceptPetSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())))
                .orElse(null);
        if (setting == null) {
            setting = buildDefaultAcceptPetSetting(companyId, businessId, serviceItemType);
            try {
                acceptPetSettingMapper.insertSelective(setting);
            } catch (Exception ignored) {
                // ignore
            }
        }
        return setting;
    }

    private BookingDateRangeSetting getBookingDateRangeSetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        BookingDateRangeSetting setting = bookingDateRangeSettingMapper
                .selectOne(c -> c.where(bookingDateRangeSetting.businessId, isEqualTo(businessId))
                        .and(bookingDateRangeSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())))
                .orElse(null);
        if (setting == null) {
            setting = buildDefaultBookingDateRangeSetting(companyId, businessId, serviceItemType);
            try {
                bookingDateRangeSettingMapper.insertSelective(setting);
            } catch (Exception ignored) {
                // ignore
            }
        }

        if (setting.getStartDateType() == DateLimitType.DATE_TYPE_OFFSET_VALUE) {
            setting.setSpecificStartDate(null);
        } else {
            setting.setMaxStartDateOffset(null);
        }

        if (setting.getEndDateType() == DateLimitType.DATE_TYPE_OFFSET_VALUE) {
            setting.setSpecificEndDate(null);
        } else {
            setting.setMaxEndDateOffset(null);
        }
        return setting;
    }

    private List<BookingCapacitySetting> selectByPrimaryList(Set<Long> capacitySettingIds) {
        if (CollectionUtils.isEmpty(capacitySettingIds)) {
            return List.of();
        }
        SelectStatementProvider capacitySelectStatement = select(
                        bookingCapacitySetting.id,
                        bookingCapacitySetting.isAllService,
                        bookingCapacitySetting.serviceIds)
                .from(bookingCapacitySetting)
                .where(bookingCapacitySetting.id, SqlBuilder.isIn(capacitySettingIds))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return bookingCapacitySettingMapper.selectMany(capacitySelectStatement);
    }

    private List<BookingTimeRangeSetting> filterValidTimeRangeSetting(
            List<BookingTimeRangeSetting> bookingTimeRangeSettings, List<Long> serviceIds) {

        if (CollectionUtils.isEmpty(bookingTimeRangeSettings)) {
            return List.of();
        }

        // 1. 优先返回 capacitySettingId = 0（代表 isAllService=true）的配置
        return bookingTimeRangeSettings.stream()
                .filter(setting -> setting.getCapacitySettingId() == 0)
                .findFirst()
                .map(List::of)
                .orElseGet(() -> {
                    // 2. 提取所有涉及的 capacitySettingId
                    Set<Long> capacitySettingIds = bookingTimeRangeSettings.stream()
                            .map(BookingTimeRangeSetting::getCapacitySettingId)
                            .collect(Collectors.toSet());

                    // 3. 查询对应的 capacitySetting 配置
                    List<BookingCapacitySetting> capacitySettings = selectByPrimaryList(capacitySettingIds);

                    // 4. 如果有 isAllService=true 的 capacitySetting，优先返回对应的 setting
                    Long finalCapacitySettingId = capacitySettings.stream()
                            .filter(BookingCapacitySetting::getIsAllService)
                            .map(BookingCapacitySetting::getId)
                            .findFirst()
                            .orElse(null);

                    if (finalCapacitySettingId != null) {
                        return bookingTimeRangeSettings.stream()
                                .filter(setting ->
                                        Objects.equals(setting.getCapacitySettingId(), finalCapacitySettingId))
                                .toList();
                    }

                    // 5. 过滤出 serviceIds 有交集的 capacitySetting
                    Set<Long> validCapacityIds = capacitySettings.stream()
                            .filter(capacitySetting -> !CollectionUtils.isEmpty(capacitySetting.getServiceIds())
                                    && capacitySetting.getServiceIds().stream().anyMatch(serviceIds::contains))
                            .map(BookingCapacitySetting::getId)
                            .collect(Collectors.toSet());

                    // 6. 返回匹配 validCapacityIds 的 setting
                    return bookingTimeRangeSettings.stream()
                            .filter(setting -> validCapacityIds.contains(setting.getCapacitySettingId()))
                            .toList();
                });
    }

    /**
     * for setting 场景使用，只查 booking availability 的数据，返回给前端
     * 也用于 C 端初始的配置获取
     * 这里不处理 service capacity 逻辑
     */
    public ArrivalPickUpTimeDef getCalculateArrivalPickUpTimeRangeDef(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        SelectStatementProvider selectStatement = select(bookingTimeRangeSetting.allColumns())
                .from(bookingTimeRangeSetting)
                .where(bookingTimeRangeSetting.businessId, isEqualTo(businessId))
                .and(bookingTimeRangeSetting.companyId, isEqualTo(companyId))
                .and(bookingTimeRangeSetting.serviceItemType, isEqualTo(serviceItemType.getNumber()))
                .orderBy(bookingTimeRangeSetting.id)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        var settingList = bookingTimeRangeSettingMapper.selectMany(selectStatement);
        if (CollectionUtils.isEmpty(settingList)) {
            return buildDefaultTimeDef(companyId, businessId, serviceItemType);
        }
        settingList.sort(Comparator.comparingLong(BookingTimeRangeSetting::getCapacitySettingId));
        return buildTimeDef(settingList.get(0));
    }

    /**
     * 组装 pick up time 的值
     */
    private ArrivalPickUpTimeDef buildTimeDef(BookingTimeRangeSetting timeRangeSetting) {
        ArrivalPickUpTimeDef.Builder builder = ArrivalPickUpTimeDef.newBuilder();
        builder.setTimeRangeSettingId(timeRangeSetting.getId());
        // 补充 time range 配置
        Map<Integer, BookingTimeRangeDetail> detailMap =
                bookingTimeRangeDetailMapper
                        .select(c -> c.where(bookingTimeRangeDetail.settingId, isEqualTo(timeRangeSetting.getId())))
                        .stream()
                        .collect(Collectors.toMap(
                                BookingTimeRangeDetail::getTimeRangeType, Function.identity(), (a, b) -> b));
        // 补充 service capacity 的配置
        if (timeRangeSetting.getCapacitySettingId() > 0) {
            BookingCapacitySetting bookingCapacitySetting = bookingCapacitySettingMapper
                    .selectByPrimaryKey(timeRangeSetting.getCapacitySettingId())
                    .orElseThrow(() ->
                            ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "BookingCapacitySetting not found"));
            builder.setIsAllService(bookingCapacitySetting.getIsAllService());
            if (!CollectionUtils.isEmpty(bookingCapacitySetting.getServiceIds())) {
                builder.addAllServiceIds(bookingCapacitySetting.getServiceIds());
            }
        } else {
            // default value
            builder.setIsAllService(true).addAllServiceIds(List.of());
        }

        builder.setIsCustomized(timeRangeSetting.getIsCustomized())
                .setStartDate(DateConverter.INSTANCE.toGoogleDate(timeRangeSetting.getStartDate()))
                .setEndDate(DateConverter.INSTANCE.toGoogleDate(timeRangeSetting.getEndDate()))
                .setScheduleType(ScheduleType.forNumber(timeRangeSetting.getScheduleType()))
                .setArrivalTimeRange(
                        TimeRangeConverter.INSTANCE.poToDef(detailMap.get(TimeRangeType.ARRIVAL_TIME_VALUE)))
                .setPickUpTimeRange(
                        TimeRangeConverter.INSTANCE.poToDef(detailMap.get(TimeRangeType.PICK_UP_TIME_VALUE)));
        return builder.build();
    }

    private ArrivalPickUpTimeDef buildDefaultTimeDefWithOutInsert(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        var setting = getDefalutBookingTimeRangeSetting(companyId, businessId, serviceItemType);

        ArrivalPickUpTimeDef.Builder builder = ArrivalPickUpTimeDef.newBuilder();
        builder.setIsCustomized(false)
                .setStartDate(DateConverter.INSTANCE.toGoogleDate(setting.getStartDate()))
                .setEndDate(DateConverter.INSTANCE.toGoogleDate(setting.getEndDate()))
                .setScheduleType(ScheduleType.forNumber(setting.getScheduleType()));
        return builder.build();
    }

    private static BookingTimeRangeSetting getDefalutBookingTimeRangeSetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        BookingTimeRangeSetting setting = new BookingTimeRangeSetting();
        setting.setBusinessId(businessId);
        setting.setCompanyId(companyId);
        setting.setServiceItemType(serviceItemType.getNumber());
        setting.setIsCustomized(false);
        setting.setScheduleType(ScheduleType.EVERY_WEEK_VALUE);
        setting.setCapacitySettingId(0L);
        setting.setStartDate(new Date());
        setting.setEndDate(ENDLESS_DATE);
        setting.setUpdatedBy(0L);
        return setting;
    }

    /**
     * 默认值构建函数
     */
    private ArrivalPickUpTimeDef buildDefaultTimeDef(long companyId, long businessId, ServiceItemType serviceItemType) {
        var setting = getDefalutBookingTimeRangeSetting(companyId, businessId, serviceItemType);
        bookingTimeRangeSettingMapper.insertSelective(setting);

        BookingTimeRangeDetail newArrivalBookingTimeRangeDetail =
                buildDefaultBookingTimeRangeDetail(setting.getId(), serviceItemType, TimeRangeType.ARRIVAL_TIME);
        bookingTimeRangeDetailMapper.insertSelective(newArrivalBookingTimeRangeDetail);

        BookingTimeRangeDetail newPickUpBookingTimeRangeDetail =
                buildDefaultBookingTimeRangeDetail(setting.getId(), serviceItemType, TimeRangeType.PICK_UP_TIME);
        bookingTimeRangeDetailMapper.insertSelective(newPickUpBookingTimeRangeDetail);

        ArrivalPickUpTimeDef.Builder builder = ArrivalPickUpTimeDef.newBuilder();
        builder.setIsCustomized(setting.getIsCustomized())
                .setStartDate(DateConverter.INSTANCE.toGoogleDate(setting.getStartDate()))
                .setEndDate(DateConverter.INSTANCE.toGoogleDate(setting.getEndDate()))
                .setScheduleType(ScheduleType.forNumber(setting.getScheduleType()))
                .setArrivalTimeRange(TimeRangeConverter.INSTANCE.poToDef(newArrivalBookingTimeRangeDetail))
                .setPickUpTimeRange(TimeRangeConverter.INSTANCE.poToDef(newPickUpBookingTimeRangeDetail))
                .setIsAllService(true)
                .addAllServiceIds(List.of());
        return builder.build();
    }

    /**
     * 优先使用 ob 配置，如果找不到 service 对应的配置，就返回 null
     * 如果任何配置都没有，代表没有初始化，就进行初始化
     * 这里只考虑有 serviceIds 的情况，如果没有其他，getCalculateArrivalPickUpTimeRangeDef(companyId, businessId, serviceItemType) 处理
     * @param companyId
     * @param businessId
     * @param serviceItemType
     * @param serviceIds
     * @return
     */
    public Map<Long, ArrivalPickUpTimeDef> getCalculateArrivalPickUpTimeRangeDef(
            long companyId, long businessId, ServiceItemType serviceItemType, List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        SelectStatementProvider selectStatement = select(bookingTimeRangeSetting.allColumns())
                .from(bookingTimeRangeSetting)
                .where(bookingTimeRangeSetting.businessId, isEqualTo(businessId))
                .and(bookingTimeRangeSetting.companyId, isEqualTo(companyId))
                .and(bookingTimeRangeSetting.serviceItemType, isEqualTo(serviceItemType.getNumber()))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        var settingList = bookingTimeRangeSettingMapper.selectMany(selectStatement);
        // calculate capacity setting id
        // 通过 capacity setting 和 service，过滤有效的 setting list
        var validSettingList = filterValidTimeRangeSetting(settingList, serviceIds);

        // 构建返回值
        List<ArrivalPickUpTimeDef> arrivalPickUpTimeRangeDefList;
        if (!CollectionUtils.isEmpty(validSettingList)) {
            arrivalPickUpTimeRangeDefList =
                    validSettingList.stream().map(this::buildTimeDef).toList();
        } else {
            // 没有配置，初始化一个配置，这里不保存，只用于计算
            var defaultTimeDef = buildDefaultTimeDefWithOutInsert(companyId, businessId, serviceItemType);
            defaultTimeDef.toBuilder().setIsAllService(false).addAllServiceIds(serviceIds);
            arrivalPickUpTimeRangeDefList = new ArrayList<>();
            arrivalPickUpTimeRangeDefList.add(defaultTimeDef);
        }

        Map<Long, ArrivalPickUpTimeDef> arrivalPickUpTimeRangeDefMap = new HashMap<>();
        serviceIds.forEach(serviceId -> {
            for (ArrivalPickUpTimeDef rangeDef : arrivalPickUpTimeRangeDefList) {
                if (rangeDef.getIsAllService() || rangeDef.getServiceIdsList().contains(serviceId)) {
                    arrivalPickUpTimeRangeDefMap.put(serviceId, rangeDef);
                }
            }
        });
        return arrivalPickUpTimeRangeDefMap;
    }

    public LodgingCapacitySetting getLodgingCapacitySetting(long companyId, long businessId, long id) {
        return lodgingCapacitySettingMapper
                .selectOne(c -> c.where(lodgingCapacitySetting.companyId, isEqualTo(companyId))
                        .and(lodgingCapacitySetting.businessId, isEqualTo(businessId))
                        .and(lodgingCapacitySetting.id, isEqualTo(id)))
                .orElse(null);
    }

    public LodgingCapacitySetting getLodgingCapacitySetting(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        LodgingCapacitySetting setting = lodgingCapacitySettingMapper
                .selectOne(c -> c.where(lodgingCapacitySetting.companyId, isEqualTo(companyId))
                        .and(lodgingCapacitySetting.businessId, isEqualTo(businessId))
                        .and(acceptPetSetting.serviceItemType, isEqualTo(serviceItemType.getNumber())))
                .orElse(null);
        if (setting == null) {
            setting = buildDefaultLodgingCapacitySetting(companyId, businessId, serviceItemType);
        }
        return setting;
    }

    private void upsertLodgingCapacitySetting(LodgingCapacitySetting newSetting) {
        // 只在更新时触发 upsert。冲突概率极低，不考虑并发问题
        LodgingCapacitySetting setting = lodgingCapacitySettingMapper
                .selectOne(c -> c.where(lodgingCapacitySetting.companyId, isEqualTo(newSetting.getCompanyId()))
                        .and(lodgingCapacitySetting.businessId, isEqualTo(newSetting.getBusinessId()))
                        .and(acceptPetSetting.serviceItemType, isEqualTo(newSetting.getServiceItemType())))
                .orElse(null);
        if (setting == null) {
            lodgingCapacitySettingMapper.insertSelective(newSetting);
            return;
        }
        lodgingCapacitySettingMapper.update(c -> c.set(lodgingCapacitySetting.isCapacityLimited)
                .equalTo(newSetting.getIsCapacityLimited())
                .set(lodgingCapacitySetting.capacityLimit)
                .equalTo(newSetting.getCapacityLimit())
                .set(lodgingCapacitySetting.allowWaitlistSignups)
                .equalToWhenPresent(newSetting.getAllowWaitlistSignups())
                .where(lodgingCapacitySetting.id, isEqualTo(setting.getId()))
                .and(lodgingCapacitySetting.companyId, isEqualTo(newSetting.getCompanyId())));
    }

    static DayOfWeekTimeRangeDef buildDefaultBookingDateRangeSetting(
            ServiceItemType serviceItemType, TimeRangeType timeRangeType) {
        DayTimeRangeDef.Builder dayTimeRangeDef = DayTimeRangeDef.newBuilder()
                .setStartTime(TIME_RANGE_DEFAULT_START_AT)
                .setEndTime(TIME_RANGE_DEFAULT_END_AT);
        if (serviceItemType == ServiceItemType.EVALUATION && timeRangeType == TimeRangeType.ARRIVAL_TIME) {
            dayTimeRangeDef.setPetCapacity(EVALUATION_DEFAULT_PET_CAPACITY);
        }
        DayTimeRangeDef dayTimeRangeDefData = dayTimeRangeDef.build();

        return DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(dayTimeRangeDefData)
                .addTuesday(dayTimeRangeDefData)
                .addWednesday(dayTimeRangeDefData)
                .addThursday(dayTimeRangeDefData)
                .addFriday(dayTimeRangeDefData)
                .addSaturday(dayTimeRangeDefData)
                .addSunday(dayTimeRangeDefData)
                .build();
    }

    static BookingTimeRangeDetail buildDefaultBookingTimeRangeDetail(
            Long settingId, ServiceItemType serviceItemType, TimeRangeType timeRangeType) {
        String weekDefStr = TimeRangeConverter.INSTANCE.toString(
                buildDefaultBookingDateRangeSetting(serviceItemType, timeRangeType));

        BookingTimeRangeDetail bookingTimeRangeDetail = new BookingTimeRangeDetail();
        bookingTimeRangeDetail.setSettingId(settingId);
        bookingTimeRangeDetail.setTimeRangeType(timeRangeType.getNumber());
        bookingTimeRangeDetail.setFirstWeek(weekDefStr);
        bookingTimeRangeDetail.setSecondWeek(weekDefStr);
        bookingTimeRangeDetail.setThirdWeek(weekDefStr);
        bookingTimeRangeDetail.setForthWeek(weekDefStr);
        return bookingTimeRangeDetail;
    }

    @Transactional
    public void updateEvaluationServiceAvailability(
            long companyId,
            long businessId,
            long staffId,
            EvaluationServiceAvailabilityUpdateDef evaluationServiceAvailability) {
        // update booking date range setting
        Long bookingDateRangeSettingId = getBookingDateRangeSetting(companyId, businessId, ServiceItemType.EVALUATION)
                .getId();
        if (bookingDateRangeSettingId == null || bookingDateRangeSettingId == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "BookingDateRangeSetting not found");
        }
        BookingDateRangeSetting bookingDateRangeSettingToUpdate =
                DateRangeConverter.INSTANCE.defToEntity(evaluationServiceAvailability.getAvailableDateRange());
        bookingDateRangeSettingMapper.update(c -> c.set(bookingDateRangeSetting.startDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getStartDateType())
                .set(bookingDateRangeSetting.maxStartDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxStartDateOffset())
                .set(bookingDateRangeSetting.specificStartDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificStartDate())
                .set(bookingDateRangeSetting.endDateType)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getEndDateType())
                .set(bookingDateRangeSetting.maxEndDateOffset)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getMaxEndDateOffset())
                .set(bookingDateRangeSetting.specificEndDate)
                .equalToWhenPresent(bookingDateRangeSettingToUpdate.getSpecificEndDate())
                .where(bookingDateRangeSetting.id, isEqualTo(bookingDateRangeSettingId)));

        // update arrival pick up time
        if (evaluationServiceAvailability.hasArrivalPickUpTimeRange()) {
            updateTimeRange(
                    companyId,
                    businessId,
                    staffId,
                    evaluationServiceAvailability.getArrivalPickUpTimeRange(),
                    ServiceItemType.EVALUATION);
        }
    }

    @Transactional
    public void updateGroomingServiceAvailability(
            long companyId,
            long businessId,
            long staffId,
            GroomingServiceAvailabilityUpdateDef groomingServiceAvailability) {

        // update accepted customer type
        if (groomingServiceAvailability.hasAcceptCustomerType()) {
            updateAcceptedCustomerSetting(
                    companyId,
                    businessId,
                    staffId,
                    groomingServiceAvailability.getAcceptCustomerType(),
                    ServiceItemType.GROOMING);
        }
    }

    public void batchCreateArrivalPickUpOverrides(List<BookingTimeRangeOverride> overrides) {
        if (CollectionUtils.isEmpty(overrides)) {
            return;
        }
        overrides.forEach(k -> {
            if (k.getCreatedAt() == null) {
                k.setCreatedAt(LocalDateTime.now());
            }
            if (k.getUpdatedAt() == null) {
                k.setUpdatedAt(LocalDateTime.now());
            }
        });
        bookingTimeRangeOverrideMapper.insertMultiple(overrides);
    }

    public void batchDeleteArrivalPickUpOverrides(@Nullable Long companyId, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<Long> settingIds = companyId == null
                ? null
                : getCompanyBookingTimeRangeSettings(companyId, null, null).stream()
                        .map(BookingTimeRangeSetting::getId)
                        .toList();
        bookingTimeRangeOverrideMapper.update(c -> c.set(bookingTimeRangeOverride.deletedAt)
                .equalTo(LocalDateTime.now())
                .where(bookingTimeRangeOverride.id, isIn(ids))
                .and(bookingTimeRangeOverride.settingId, isInWhenPresent(settingIds))
                .and(bookingTimeRangeOverride.deletedAt, isNull()));
    }

    public List<BookingTimeRangeOverride> batchUpdateArrivalPickUpOverrides(
            @Nullable Long companyId, List<BookingTimeRangeOverride> updates) {
        if (CollectionUtils.isEmpty(updates)) {
            return List.of();
        }

        List<Long> idsToUpdate =
                updates.stream().map(BookingTimeRangeOverride::getId).toList();

        // company 校验
        if (companyId != null) {
            List<Long> settingIds = getCompanyBookingTimeRangeSettings(companyId, null, null).stream()
                    .map(BookingTimeRangeSetting::getId)
                    .toList();
            List<BookingTimeRangeOverride> toUpdates = getArrivalPickUpOverrideByIds(idsToUpdate);
            toUpdates.forEach(k -> {
                if (!settingIds.contains(k.getSettingId())) {
                    throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "SettingId not exist");
                }
            });
        }

        updates.forEach(bookingTimeRangeOverrideMapper::updateByPrimaryKeySelective);
        return getArrivalPickUpOverrideByIds(idsToUpdate);
    }

    public List<BookingTimeRangeOverride> getArrivalPickUpOverrideBySettingIds(List<Long> settingIds) {
        if (CollectionUtils.isEmpty(settingIds)) {
            return List.of();
        }
        return bookingTimeRangeOverrideMapper.select(
                c -> c.where(bookingTimeRangeOverride.settingId, isInWhenPresent(settingIds))
                        .and(bookingTimeRangeOverride.deletedAt, isNull()));
    }

    public List<BookingTimeRangeOverride> getArrivalPickUpOverrideByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return bookingTimeRangeOverrideMapper.select(
                c -> c.where(bookingTimeRangeOverride.id, isIn(ids)).and(bookingTimeRangeOverride.deletedAt, isNull()));
    }

    public List<BookingTimeRangeSetting> getCompanyBookingTimeRangeSettings(
            Long companyId, @Nullable List<Long> businessIds, @Nullable List<ServiceItemType> serviceItemTypes) {
        List<Integer> itemTypes = CollectionUtils.isEmpty(serviceItemTypes)
                ? null
                : serviceItemTypes.stream()
                        .map(com.moego.idl.models.offering.v1.ServiceItemType::getNumber)
                        .toList();
        return bookingTimeRangeSettingMapper.select(
                c -> c.where(bookingTimeRangeSetting.companyId, isEqualTo(companyId))
                        .and(
                                bookingTimeRangeSetting.businessId,
                                isInWhenPresent(CollectionUtils.isEmpty(businessIds) ? null : businessIds))
                        .and(bookingTimeRangeSetting.serviceItemType, isInWhenPresent(itemTypes))
                        .orderBy(bookingCapacitySetting.id));
    }

    public List<BookingTimeRangeSetting> getBookingTimeRangeSettingByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return bookingTimeRangeSettingMapper.select(c ->
                c.where(bookingTimeRangeSetting.id, isIn(ids.stream().distinct().toList())));
    }

    public void createCapacityOverride(LodgingCapacityOverride capacityOverride) {
        if (capacityOverride.getCreatedAt() == null) {
            capacityOverride.setCreatedAt(LocalDateTime.now());
        }
        if (capacityOverride.getUpdatedAt() == null) {
            capacityOverride.setUpdatedAt(LocalDateTime.now());
        }
        lodgingCapacityOverrideMapper.insertSelective(capacityOverride);
    }

    public LodgingCapacityOverride getCapacityOverride(long id) {
        return lodgingCapacityOverrideMapper
                .selectOne(c -> c.where(lodgingCapacityOverride.id, isEqualTo(id))
                        .and(lodgingCapacityOverride.deletedAt, isNull()))
                .orElse(null);
    }

    public List<LodgingCapacityOverride> getCapacityOverrideList(@Nullable Long capacitySettingId) {
        if (capacitySettingId == null) {
            return List.of();
        }
        return lodgingCapacityOverrideMapper.select(
                c -> c.where(lodgingCapacityOverride.settingId, isEqualTo(capacitySettingId))
                        .and(lodgingCapacityOverride.deletedAt, isNull()));
    }

    public List<LodgingCapacityOverride> getCapacityOverrideList(
            long companyId, long businessId, ServiceItemType serviceItemType) {
        LodgingCapacitySetting lodgingCapacitySetting =
                getLodgingCapacitySetting(companyId, businessId, serviceItemType);
        if (Objects.isNull(lodgingCapacitySetting)) {
            return List.of();
        }
        return lodgingCapacityOverrideMapper.select(
                c -> c.where(lodgingCapacityOverride.settingId, isEqualTo(lodgingCapacitySetting.getId()))
                        .and(lodgingCapacityOverride.deletedAt, isNull()));
    }

    public void deleteCapacityOverride(long id) {
        var now = LocalDateTime.now();
        lodgingCapacityOverrideMapper.update(c -> c.set(lodgingCapacityOverride.deletedAt)
                .equalTo(now)
                .set(lodgingCapacityOverride.updatedAt)
                .equalTo(now)
                .where(lodgingCapacityOverride.id, isEqualTo(id)));
    }

    public void updateCapacityOverride(LodgingCapacityOverride capacityOverride) {
        if (capacityOverride.getUpdatedAt() == null) {
            capacityOverride.setUpdatedAt(LocalDateTime.now());
        }
        lodgingCapacityOverrideMapper.updateByPrimaryKeySelective(capacityOverride);
    }

    public LodgingCapacitySetting checkAndGetCapacitySettingByOverrideId(
            long companyId, long businessId, long capacityOverrideId) {
        LodgingCapacityOverride capacityOverride = getCapacityOverride(capacityOverrideId);
        if (Objects.isNull(capacityOverride)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "capacity override not found");
        }
        LodgingCapacitySetting lodgingCapacitySetting =
                getLodgingCapacitySetting(companyId, businessId, capacityOverride.getSettingId());
        if (Objects.isNull(lodgingCapacitySetting)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "settingId for capacity override not found");
        }
        return lodgingCapacitySetting;
    }
}

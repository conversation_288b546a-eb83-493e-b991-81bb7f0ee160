package com.moego.svc.online.booking.server;

import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeRequest;
import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeResponse;
import com.moego.idl.service.online_booking.v1.OBAvailableDateTimeServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.mapstruct.ArrivalPickUpTimeConverter;
import com.moego.svc.online.booking.service.OBAvailableDateTimeService;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class OBAvailableServer extends OBAvailableDateTimeServiceGrpc.OBAvailableDateTimeServiceImplBase {

    private final OBAvailableDateTimeService obAvailableDateTimeService;

    @Override
    public void getAvailableDateTime(
            GetAvailableDateTimeRequest request, StreamObserver<GetAvailableDateTimeResponse> responseObserver) {
        var timeRangeDefPair = obAvailableDateTimeService.getCareTypeAvailableTimeRange(
                request.getCompanyId(),
                request.getBusinessId(),
                ProtobufUtil.toLocalDate(request.getFromDate()),
                ProtobufUtil.toLocalDate(request.getToDate()),
                request.getServiceItemType(),
                request.getRelationServiceIdsList());
        responseObserver.onNext(GetAvailableDateTimeResponse.newBuilder()
                .addAllArrivalTimeRange(
                        ArrivalPickUpTimeConverter.INSTANCE.convertToDateTimeRange(timeRangeDefPair.getFirst()))
                .addAllPickUpTimeRange(
                        ArrivalPickUpTimeConverter.INSTANCE.convertToDateTimeRange(timeRangeDefPair.getSecond()))
                .build());
        responseObserver.onCompleted();
    }
}

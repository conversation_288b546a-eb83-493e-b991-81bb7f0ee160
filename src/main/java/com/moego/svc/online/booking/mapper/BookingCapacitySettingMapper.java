package com.moego.svc.online.booking.mapper;

import static com.moego.svc.online.booking.mapper.BookingCapacitySettingDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.svc.online.booking.entity.BookingCapacitySetting;
import com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface BookingCapacitySettingMapper extends CommonSelectMapper, CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper, DynamicDataSource<BookingCapacitySettingMapper> {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    BasicColumn[] selectList = BasicColumn.columnList(id, name, serviceIds, isAllService, createdAt, updatedAt);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<BookingCapacitySetting> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<BookingCapacitySetting> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="BookingCapacitySettingResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="service_ids", property="serviceIds", typeHandler=JsonArrayTypeHandler.class, jdbcType=JdbcType.OTHER),
        @Result(column="is_all_service", property="isAllService", jdbcType=JdbcType.BIT),
        @Result(column="created_at", property="createdAt", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="updated_at", property="updatedAt", jdbcType=JdbcType.TIMESTAMP)
    })
    List<BookingCapacitySetting> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("BookingCapacitySettingResult")
    Optional<BookingCapacitySetting> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int insertMultiple(Collection<BookingCapacitySetting> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, bookingCapacitySetting, c ->
            c.map(name).toProperty("name")
            .map(serviceIds).toProperty("serviceIds")
            .map(isAllService).toProperty("isAllService")
            .map(createdAt).toProperty("createdAt")
            .map(updatedAt).toProperty("updatedAt")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int insertSelective(BookingCapacitySetting row) {
        return MyBatis3Utils.insert(this::insert, row, bookingCapacitySetting, c ->
            c.map(name).toPropertyWhenPresent("name", row::getName)
            .map(serviceIds).toPropertyWhenPresent("serviceIds", row::getServiceIds)
            .map(isAllService).toPropertyWhenPresent("isAllService", row::getIsAllService)
            .map(createdAt).toPropertyWhenPresent("createdAt", row::getCreatedAt)
            .map(updatedAt).toPropertyWhenPresent("updatedAt", row::getUpdatedAt)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default Optional<BookingCapacitySetting> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default List<BookingCapacitySetting> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default List<BookingCapacitySetting> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default Optional<BookingCapacitySetting> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, bookingCapacitySetting, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    static UpdateDSL<UpdateModel> updateAllColumns(BookingCapacitySetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(name).equalTo(row::getName)
                .set(serviceIds).equalTo(row::getServiceIds)
                .set(isAllService).equalTo(row::getIsAllService)
                .set(createdAt).equalTo(row::getCreatedAt)
                .set(updatedAt).equalTo(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(BookingCapacitySetting row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(name).equalToWhenPresent(row::getName)
                .set(serviceIds).equalToWhenPresent(row::getServiceIds)
                .set(isAllService).equalToWhenPresent(row::getIsAllService)
                .set(createdAt).equalToWhenPresent(row::getCreatedAt)
                .set(updatedAt).equalToWhenPresent(row::getUpdatedAt);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    default int updateByPrimaryKeySelective(BookingCapacitySetting row) {
        return update(c ->
            c.set(name).equalToWhenPresent(row::getName)
            .set(serviceIds).equalToWhenPresent(row::getServiceIds)
            .set(isAllService).equalToWhenPresent(row::getIsAllService)
            .set(createdAt).equalToWhenPresent(row::getCreatedAt)
            .set(updatedAt).equalToWhenPresent(row::getUpdatedAt)
            .where(id, isEqualTo(row::getId))
        );
    }
}
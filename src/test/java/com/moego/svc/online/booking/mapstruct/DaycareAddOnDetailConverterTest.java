package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.service.online_booking.v1.UpdateDaycareAddOnDetailRequest;
import com.moego.idl.utils.v1.StringListValue;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.DaycareAddOnDetail;
import java.util.List;
import org.junit.jupiter.api.Test;

class DaycareAddOnDetailConverterTest {

    @Test
    void testUpdateRequestToEntity_withSpecificDate() {
        // Arrange
        var updateRequest = UpdateDaycareAddOnDetailRequest.newBuilder()
                .setId(123L)
                .setSpecificDates(StringListValue.newBuilder()
                        .addAllValues(List.of("2025-01-15", "2025-01-20"))
                        .build())
                .setQuantityPerDay(2)
                .build();

        // Act
        var result = DaycareAddOnDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);

        var expect = new DaycareAddOnDetail();
        expect.setId(123L);
        expect.setSpecificDates(JsonUtil.toJson(List.of("2025-01-15", "2025-01-20")));
        expect.setIsEveryday(false);
        expect.setQuantityPerDay(2);

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void testUpdateRequestToEntity_withEveryDay() {
        // Arrange
        var updateRequest = UpdateDaycareAddOnDetailRequest.newBuilder()
                .setId(123L)
                .setIsEveryday(true)
                .build();

        // Act
        var result = DaycareAddOnDetailConverter.INSTANCE.updateRequestToEntity(updateRequest);

        var expect = new DaycareAddOnDetail();
        expect.setId(123L);
        expect.setSpecificDates(JsonUtil.toJson(List.of()));
        expect.setIsEveryday(true);

        assertThat(result).isEqualTo(expect);
    }
}

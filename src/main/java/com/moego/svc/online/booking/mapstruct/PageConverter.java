/*
 * @since 2023-05-29 15:23:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.online.booking.mapstruct;

import com.moego.common.utils.Pagination;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PageConverter {
    PageConverter INSTANCE = Mappers.getMapper(PageConverter.class);

    default Pagination toPagination(PaginationRequest request) {
        var pageSize = request.hasPageSize() ? request.getPageSize() : 20;
        var pageNum = request.hasPageNum() ? request.getPageNum() : 1;
        return new Pagination(pageNum, pageSize, 0);
    }

    default Pagination toPagination(PaginationResponse response) {
        var pageSize = response.getPageSize();
        var pageNum = response.getPageNum();
        return new Pagination(pageNum < 1 ? 1 : 0, pageSize, 0);
    }

    PaginationRequest toRequest(Pagination pageInfo);

    @Named("toResponse")
    PaginationResponse toResponse(Pagination pageInfo);
}

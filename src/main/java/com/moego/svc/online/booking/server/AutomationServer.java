package com.moego.svc.online.booking.server;

import com.moego.common.utils.DateUtil;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.AutomationServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetAutomationSettingRequest;
import com.moego.idl.service.online_booking.v1.GetAutomationSettingResponse;
import com.moego.idl.service.online_booking.v1.UpdateAutomationSettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateAutomationSettingResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.entity.AutomationSetting;
import com.moego.svc.online.booking.mapper.AutomationSettingMapper;
import com.moego.svc.online.booking.mapstruct.AutomationConverter;
import com.moego.svc.online.booking.service.AutomationSettingService;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class AutomationServer extends AutomationServiceGrpc.AutomationServiceImplBase {

    private final AutomationSettingMapper automationSettingMapper;
    private final AutomationSettingService automationSettingService;

    @Override
    public void getAutomationSetting(
            GetAutomationSettingRequest request, StreamObserver<GetAutomationSettingResponse> responseObserver) {
        AutomationSetting setting = automationSettingService.getAutomationSetting(
                request.getCompanyId(), request.getBusinessId(), request.getServiceItemType());

        responseObserver.onNext(GetAutomationSettingResponse.newBuilder()
                .setAutomationSetting(AutomationConverter.INSTANCE.toModel(setting))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAutomationSetting(
            UpdateAutomationSettingRequest request, StreamObserver<UpdateAutomationSettingResponse> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = request.getCompanyId();
        long staffId = request.getStaffId();
        ServiceItemType serviceItemType = request.getServiceItemType();

        AutomationSetting setting =
                automationSettingService.getAutomationSetting(companyId, businessId, serviceItemType);
        setting.setUpdatedAt(DateUtil.getNowDate());
        setting.setUpdateBy(staffId);
        setting.setEnableAutoAccept(request.getEnableAutoAccept());
        setting.setAutoAcceptCondition(JsonUtil.toJson(request.getAutoAcceptCondition()));
        if (Objects.isNull(setting.getId())) {
            automationSettingMapper.insertSelective(setting);
        } else {
            automationSettingMapper.updateByPrimaryKeySelective(setting);
        }

        responseObserver.onNext(UpdateAutomationSettingResponse.newBuilder()
                .setAutomationSetting(AutomationConverter.INSTANCE.toModel(setting))
                .build());
        responseObserver.onCompleted();
    }
}

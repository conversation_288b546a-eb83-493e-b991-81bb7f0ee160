package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class DaycareServiceWaitlistDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_waitlist")
    public static final DaycareServiceWaitlist daycareServiceWaitlist = new DaycareServiceWaitlist();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.id")
    public static final SqlColumn<Long> id = daycareServiceWaitlist.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = daycareServiceWaitlist.bookingRequestId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.service_detail_id")
    public static final SqlColumn<Long> serviceDetailId = daycareServiceWaitlist.serviceDetailId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.specific_dates")
    public static final SqlColumn<List<LocalDate>> specificDates = daycareServiceWaitlist.specificDates;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = daycareServiceWaitlist.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = daycareServiceWaitlist.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: daycare_service_waitlist.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = daycareServiceWaitlist.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: daycare_service_waitlist")
    public static final class DaycareServiceWaitlist extends AliasableSqlTable<DaycareServiceWaitlist> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceDetailId = column("service_detail_id", JDBCType.BIGINT);

        public final SqlColumn<List<LocalDate>> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.DaycareServiceWaitlistSpecificDatesTypeHandler");

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public DaycareServiceWaitlist() {
            super("daycare_service_waitlist", DaycareServiceWaitlist::new);
        }
    }
}
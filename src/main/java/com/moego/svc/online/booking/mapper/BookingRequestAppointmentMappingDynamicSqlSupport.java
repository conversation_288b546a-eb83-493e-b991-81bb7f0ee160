package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingRequestAppointmentMappingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    public static final BookingRequestAppointmentMapping bookingRequestAppointmentMapping = new BookingRequestAppointmentMapping();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.id")
    public static final SqlColumn<Long> id = bookingRequestAppointmentMapping.id;

    /**
     * Database Column Remarks:
     *   booking request id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = bookingRequestAppointmentMapping.bookingRequestId;

    /**
     * Database Column Remarks:
     *   appointment id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.appointment_id")
    public static final SqlColumn<Long> appointmentId = bookingRequestAppointmentMapping.appointmentId;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.created_at")
    public static final SqlColumn<Date> createdAt = bookingRequestAppointmentMapping.createdAt;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_request_appointment_mapping.updated_at")
    public static final SqlColumn<Date> updatedAt = bookingRequestAppointmentMapping.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_request_appointment_mapping")
    public static final class BookingRequestAppointmentMapping extends AliasableSqlTable<BookingRequestAppointmentMapping> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> appointmentId = column("appointment_id", JDBCType.BIGINT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public BookingRequestAppointmentMapping() {
            super("booking_request_appointment_mapping", BookingRequestAppointmentMapping::new);
        }
    }
}
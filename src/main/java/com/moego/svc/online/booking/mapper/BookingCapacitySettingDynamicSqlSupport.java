package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import java.util.List;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingCapacitySettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    public static final BookingCapacitySetting bookingCapacitySetting = new BookingCapacitySetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.id")
    public static final SqlColumn<Long> id = bookingCapacitySetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.name")
    public static final SqlColumn<String> name = bookingCapacitySetting.name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.service_ids")
    public static final SqlColumn<List<Long>> serviceIds = bookingCapacitySetting.serviceIds;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.is_all_service")
    public static final SqlColumn<Boolean> isAllService = bookingCapacitySetting.isAllService;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = bookingCapacitySetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_capacity_setting.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = bookingCapacitySetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_capacity_setting")
    public static final class BookingCapacitySetting extends AliasableSqlTable<BookingCapacitySetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("\"name\"", JDBCType.VARCHAR);

        public final SqlColumn<List<Long>> serviceIds = column("service_ids", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler");

        public final SqlColumn<Boolean> isAllService = column("is_all_service", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public BookingCapacitySetting() {
            super("booking_capacity_setting", BookingCapacitySetting::new);
        }
    }
}
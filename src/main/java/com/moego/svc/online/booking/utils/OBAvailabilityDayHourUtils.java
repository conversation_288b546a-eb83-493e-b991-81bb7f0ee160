package com.moego.svc.online.booking.utils;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.organization.v1.BookingLimitation;
import com.moego.idl.models.organization.v1.LimitType;
import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.LimitationGroupDef;
import com.moego.idl.models.organization.v1.PetBreedLimitation;
import com.moego.idl.models.organization.v1.PetSizeLimitation;
import com.moego.idl.models.organization.v1.ServiceLimitation;
import com.moego.idl.models.organization.v1.SlotAvailabilityDayDef;
import com.moego.idl.models.organization.v1.StaffAvailabilityDef;
import com.moego.idl.models.organization.v1.TimeAvailabilityDayDef;
import com.moego.svc.online.booking.entity.DayHourLimit;
import com.moego.svc.online.booking.entity.DayHourLimitGroup;
import com.moego.svc.online.booking.entity.StaffAvailability;
import com.moego.svc.online.booking.entity.StaffAvailabilitySlotDay;
import com.moego.svc.online.booking.entity.StaffAvailabilityTimeDay;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

public class OBAvailabilityDayHourUtils {

    public static String getStaffAvailabilityDayKey(Long businessId, Long staffId, Integer dayOfWeek) {
        return businessId + "_" + staffId + "_" + dayOfWeek;
    }

    public static StaffAvailability buildStaffAvailability(
            Long companyId, Long businessId, StaffAvailabilityDef staffAvailabilityDef) {
        StaffAvailability staffAvailability = new StaffAvailability();
        staffAvailability.setCompanyId(companyId);
        staffAvailability.setBusinessId(businessId);
        staffAvailability.setStaffId(staffAvailabilityDef.getStaffId());
        staffAvailability.setIsAvailable(staffAvailabilityDef.getIsAvailable());
        staffAvailability.setCreatedAt(new Date());
        staffAvailability.setUpdatedAt(new Date());
        return staffAvailability;
    }

    public static StaffAvailabilitySlotDay buildStaffSlotDay(
            Long companyId,
            Long businessId,
            Long staffId,
            SlotAvailabilityDayDef slotAvailabilityDayDef,
            List<Long> limitIds) {
        StaffAvailabilitySlotDay staffSlotDay = new StaffAvailabilitySlotDay();
        staffSlotDay.setCompanyId(companyId);
        staffSlotDay.setBusinessId(businessId);
        staffSlotDay.setStaffId(staffId);
        staffSlotDay.setIsAvailable(slotAvailabilityDayDef.getIsAvailable());
        staffSlotDay.setDayOfWeek(slotAvailabilityDayDef.getDayOfWeekValue());
        staffSlotDay.setCapacity(slotAvailabilityDayDef.getSlotDailySetting().getCapacity());
        staffSlotDay.setStartTime(slotAvailabilityDayDef.getSlotDailySetting().getStartTime());
        staffSlotDay.setEndTime(slotAvailabilityDayDef.getSlotDailySetting().getEndTime());
        staffSlotDay.setLimitIds(limitIds);
        staffSlotDay.setCreatedAt(new Date());
        staffSlotDay.setUpdatedAt(new Date());
        return staffSlotDay;
    }

    public static StaffAvailabilityTimeDay buildStaffTimeDay(
            Long companyId,
            Long businessId,
            Long staffId,
            TimeAvailabilityDayDef timeAvailabilityDayDef,
            List<Long> limitIds) {
        StaffAvailabilityTimeDay staffTimeDay = new StaffAvailabilityTimeDay();
        staffTimeDay.setCompanyId(companyId);
        staffTimeDay.setBusinessId(businessId);
        staffTimeDay.setStaffId(staffId);
        staffTimeDay.setIsAvailable(timeAvailabilityDayDef.getIsAvailable());
        staffTimeDay.setDayOfWeek(timeAvailabilityDayDef.getDayOfWeekValue());
        staffTimeDay.setLimitIds(limitIds);
        staffTimeDay.setCreatedAt(new Date());
        staffTimeDay.setUpdatedAt(new Date());
        return staffTimeDay;
    }

    public static List<DayHourLimit> buildDayHourLimits(LimitationGroupDef limitationGroup, Long groupId) {
        List<DayHourLimit> limits = new ArrayList<>();
        limitationGroup.getServiceLimitsList().forEach(serviceLimit -> {
            DayHourLimit limit = new DayHourLimit();
            limit.setType(LimitType.SERVICE_LIMIT_VALUE);
            limit.setServiceIds(serviceLimit.getServiceIdsList());
            limit.setIsAllService(serviceLimit.getIsAllService());
            limit.setCapacity(serviceLimit.getCapacity());
            limit.setCreatedAt(new Date());
            limit.setUpdatedAt(new Date());
            if (CommonUtil.isNormal(groupId)) {
                limit.setGroupId(groupId);
            }
            limits.add(limit);
        });
        limitationGroup.getPetSizeLimitsList().forEach(petSizeLimit -> {
            DayHourLimit limit = new DayHourLimit();
            limit.setType(LimitType.PET_SIZE_LIMIT_VALUE);
            limit.setPetSizeIds(petSizeLimit.getPetSizeIdsList());
            limit.setCapacity(petSizeLimit.getCapacity());
            limit.setCreatedAt(new Date());
            limit.setUpdatedAt(new Date());
            if (CommonUtil.isNormal(groupId)) {
                limit.setGroupId(groupId);
            }
            limits.add(limit);
        });
        limitationGroup.getPetBreedLimitsList().forEach(petBreedLimit -> {
            DayHourLimit limit = new DayHourLimit();
            limit.setType(LimitType.PET_BREED_LIMIT_VALUE);
            limit.setBreedIds(petBreedLimit.getBreedIdsList());
            limit.setCapacity(petBreedLimit.getCapacity());
            limit.setPetTypeId(petBreedLimit.getPetTypeId());
            limit.setIsAllBreed(petBreedLimit.getIsAllBreed());
            limit.setCreatedAt(new Date());
            limit.setUpdatedAt(new Date());
            if (CommonUtil.isNormal(groupId)) {
                limit.setGroupId(groupId);
            }
            limits.add(limit);
        });
        return limits;
    }

    public static BookingLimitation buildBookingLimitationPBModelDeprecated(List<DayHourLimit> limits) {
        BookingLimitation.Builder builder = BookingLimitation.newBuilder();
        limits.forEach(limit -> {
            switch (limit.getType()) {
                case LimitType.SERVICE_LIMIT_VALUE:
                    builder.addServiceLimits(BookingLimitation.ServiceLimitation.newBuilder()
                            .addAllServiceIds(limit.getServiceIds())
                            .setCapacity(limit.getCapacity())
                            .setIsAllService(limit.getIsAllService())
                            .build());
                    break;
                case LimitType.PET_SIZE_LIMIT_VALUE:
                    builder.addPetSizeLimits(BookingLimitation.PetSizeLimitation.newBuilder()
                            .addAllPetSizeIds(limit.getPetSizeIds())
                            .setCapacity(limit.getCapacity())
                            .build());
                    break;
                case LimitType.PET_BREED_LIMIT_VALUE:
                    builder.addPetBreedLimits(BookingLimitation.PetBreedLimitation.newBuilder()
                            .addAllBreedIds(limit.getBreedIds())
                            .setCapacity(limit.getCapacity())
                            .setPetTypeId(limit.getPetTypeId())
                            .setIsAllBreed(limit.getIsAllBreed())
                            .build());
                    break;
                default:
                    break;
            }
        });
        return builder.build();
    }

    public static List<LimitationGroup> buildBookingLimitationPBModel(
            List<DayHourLimit> limits, final Map<Long, DayHourLimitGroup> dayHourLimitGroupMap) {
        List<LimitationGroup> limitationGroups = new ArrayList<>();
        limits.stream()
                .filter(limit ->
                        CommonUtil.isNormal(limit.getGroupId()) && dayHourLimitGroupMap.containsKey(limit.getGroupId()))
                .collect(Collectors.groupingBy(DayHourLimit::getGroupId))
                .forEach((groupId, currentLimits) -> {
                    DayHourLimitGroup dayHourLimitGroup = dayHourLimitGroupMap.get(groupId);
                    var groupBuilder = LimitationGroup.newBuilder()
                            .setOnlyAcceptSelected(dayHourLimitGroup.getOnlyAcceptSelected());
                    currentLimits.forEach(limit -> {
                        switch (limit.getType()) {
                            case LimitType.SERVICE_LIMIT_VALUE:
                                groupBuilder.addServiceLimits(getServiceLimitation(limit));
                                break;
                            case LimitType.PET_SIZE_LIMIT_VALUE:
                                groupBuilder.addPetSizeLimits(getPetSizeLimitation(limit));
                                break;
                            case LimitType.PET_BREED_LIMIT_VALUE:
                                groupBuilder.addPetBreedLimits(getPetBreedLimitation(limit));
                                break;
                            default:
                                break;
                        }
                    });
                    limitationGroups.add(groupBuilder.build());
                });

        var dayHourLimit = limits.stream()
                .filter(limit -> !CommonUtil.isNormal(limit.getGroupId()))
                .toList();
        if (!CollectionUtils.isEmpty(dayHourLimit)) {
            dayHourLimit.stream()
                    .collect(Collectors.groupingBy(DayHourLimit::getType))
                    .forEach((type, currentLimits) -> {
                        var groupBuilder = LimitationGroup.newBuilder().setOnlyAcceptSelected(false);
                        currentLimits.forEach(limit -> {
                            switch (limit.getType()) {
                                case LimitType.SERVICE_LIMIT_VALUE:
                                    groupBuilder.addServiceLimits(getServiceLimitation(limit));
                                    break;
                                case LimitType.PET_SIZE_LIMIT_VALUE:
                                    groupBuilder.addPetSizeLimits(getPetSizeLimitation(limit));
                                    break;
                                case LimitType.PET_BREED_LIMIT_VALUE:
                                    groupBuilder.addPetBreedLimits(getPetBreedLimitation(limit));
                                    break;
                                default:
                                    break;
                            }
                        });
                        limitationGroups.add(groupBuilder.build());
                    });
        }

        return limitationGroups;
    }

    private static PetBreedLimitation getPetBreedLimitation(final DayHourLimit limit) {
        return PetBreedLimitation.newBuilder()
                .addAllBreedIds(limit.getBreedIds())
                .setCapacity(limit.getCapacity())
                .setPetTypeId(limit.getPetTypeId())
                .setIsAllBreed(limit.getIsAllBreed())
                .build();
    }

    private static PetSizeLimitation getPetSizeLimitation(final DayHourLimit limit) {
        return PetSizeLimitation.newBuilder()
                .addAllPetSizeIds(limit.getPetSizeIds())
                .setCapacity(limit.getCapacity())
                .build();
    }

    private static ServiceLimitation getServiceLimitation(final DayHourLimit limit) {
        return ServiceLimitation.newBuilder()
                .addAllServiceIds(limit.getServiceIds())
                .setCapacity(limit.getCapacity())
                .setIsAllService(limit.getIsAllService())
                .build();
    }
}

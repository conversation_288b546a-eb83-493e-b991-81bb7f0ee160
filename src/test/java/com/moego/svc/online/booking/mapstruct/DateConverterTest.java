package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Date;
import java.time.LocalDate;
import org.junit.jupiter.api.Test;

class DateConverterTest {

    @Test
    void toGoogleDate_withValidLocalDate_shouldReturnCorrectDate() {
        LocalDate localDate = LocalDate.of(2023, 1, 1);

        Date result = DateConverter.INSTANCE.toGoogleDate(localDate);

        Date expect = Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build();

        assertThat(result).isEqualTo(expect);
    }

    @Test
    void toLocalDate_withValidDate_shouldReturnCorrectLocalDate() {
        Date date = Date.newBuilder().setYear(2023).setMonth(1).setDay(1).build();

        LocalDate result = DateConverter.INSTANCE.toLocalDate(date);

        assertThat(result).isEqualTo(LocalDate.of(2023, 1, 1));
    }
}

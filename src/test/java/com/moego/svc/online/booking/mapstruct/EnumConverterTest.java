package com.moego.svc.online.booking.mapstruct;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.exception.BizException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

class EnumConverterTest {

    @Test
    void shouldMapValidServiceItemType() {
        // Arrange
        int validValue = 1;

        // Act
        ServiceItemType result = EnumConverter.INSTANCE.mapServiceItemType(validValue);

        // Assert
        assertThat(result).isEqualTo(ServiceItemType.forNumber(validValue));
    }

    @Test
    void shouldThrowExceptionForInvalidServiceItemType() {
        // Arrange
        int invalidValue = 999;

        // Act & Assert
        assertThatThrownBy(() -> EnumConverter.INSTANCE.mapServiceItemType(invalidValue))
                .isInstanceOf(BizException.class)
                .hasFieldOrPropertyWithValue("code", Code.CODE_PARAMS_ERROR_VALUE)
                .hasMessage("Unsupported care type");
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3, 4})
    void shouldMapMultipleValidServiceItemTypes(int value) {
        // Act
        ServiceItemType result = EnumConverter.INSTANCE.mapServiceItemType(value);

        // Assert
        assertThat(result).isNotNull().isEqualTo(ServiceItemType.forNumber(value));
    }
}

package com.moego.svc.online.booking.service;

import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.svc.online.booking.client.OrganizationClient;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.dto.FinalWeekTimeRangeDTO;
import com.moego.svc.online.booking.dto.UsedLocalTimeDTO;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.mapstruct.ArrivalPickUpTimeConverter;
import com.moego.svc.online.booking.utils.AvailabilityUtil;
import com.moego.svc.online.booking.utils.TimeRangeUtils;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class OBAvailableDateTimeService {

    private final OrganizationClient organizationClient;
    private final AvailabilitySettingService availabilitySettingService;
    private final BusinessHelper businessHelper;
    private final BookingRequestService bookingRequestService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceBlockingStub;
    private final EvaluationTestDetailService evaluationTestDetailService;

    private static final List<WaitListStatus> WAIT_LIST_STATUSES_FOR_APPOINTMENT =
            List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST);

    public static final List<AppointmentStatus> IN_PROGRESS_STATUS_VALUE_SET = List.of(
            AppointmentStatus.UNCONFIRMED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.READY,
            AppointmentStatus.CHECKED_IN);

    /**
     * arrival/pick up 计算流程如下：
     * 1. 获取 business working hour setting。作为 arrival/pick up 默认的 working time ranges
     * 2. 获取 ob arrival/pick up working hour setting。作为 ob 的 working time ranges，优先级高于 business working hour setting
     * 3. 获取 ob overrides setting。作为 ob 的 working hour setting for care type. 优先级高于 ob working hour setting. ob overrides setting 独立于 ob arrival/pick up working hour setting
     *
     * @return <arrival time ranges, pick up time ranges>
     */
    public Pair<Map<LocalDate, List<DayTimeRangeDef>>, Map<LocalDate, List<DayTimeRangeDef>>>
            getCareTypeAvailableTimeRange(
                    Long companyId,
                    Long businessId,
                    LocalDate fromDate,
                    LocalDate toDate,
                    ServiceItemType serviceItemType,
                    List<Long> relationServiceIds) {
        // 获取 business working hour setting
        var businessWeekTimeRange = organizationClient.getAvailableTimeRange(companyId, businessId, fromDate, toDate);
        if (serviceItemType != ServiceItemType.BOARDING
                && serviceItemType != ServiceItemType.DAYCARE
                && serviceItemType != ServiceItemType.EVALUATION) {
            return Pair.of(businessWeekTimeRange, businessWeekTimeRange);
        }

        // query ob week time ranges
        var finalWeekTimeRangeDTO = queryFinalWeekTimeRange(
                companyId, businessId, fromDate, toDate, serviceItemType, relationServiceIds, businessWeekTimeRange);

        // 获取 business closed dates
        List<LocalDate> businessClosedDates =
                organizationClient.getBusinessClosedDatesWithinRange(companyId, businessId, fromDate, toDate);

        var finalArrivalWeekTimeRange =
                removeUnavailableDates(finalWeekTimeRangeDTO.arrivalTimeRangeMap(), businessClosedDates);
        var finalPickUpWeekTimeRange =
                removeUnavailableDates(finalWeekTimeRangeDTO.pickUpTimeRangeMap(), businessClosedDates);

        // 拉取 ob overrides setting
        List<ArrivalPickUpTimeOverrideModel> overrides =
                getArrivalPickUpTimeOverrides(businessId, finalWeekTimeRangeDTO.settingIds());
        List<ArrivalPickUpTimeOverrideModel> arrivalOverrides = overrides.stream()
                .filter(k -> k.getType() == TimeRangeType.ARRIVAL_TIME)
                .toList();
        List<ArrivalPickUpTimeOverrideModel> pickUpOverrides = overrides.stream()
                .filter(k -> k.getType() == TimeRangeType.PICK_UP_TIME)
                .toList();

        Map<LocalDate, List<DayTimeRangeDef>> arrivalAvailableTimeRange =
                AvailabilityUtil.getAvailableTimeInRange(fromDate, toDate, finalArrivalWeekTimeRange, arrivalOverrides);

        // 只有 evaluation 才有 pet capacity 逻辑，需要实时查询预约计算余额
        if (serviceItemType == ServiceItemType.EVALUATION) {
            arrivalAvailableTimeRange = getAvailableTimeRangeByPetCapacity(
                    companyId, businessId, fromDate, toDate, arrivalAvailableTimeRange);
        }

        // 只有 boarding 和 daycare，才需要计算 pick up 的时间范围
        Map<LocalDate, List<DayTimeRangeDef>> pickUpAvailableTimeRange = new HashMap<>();
        if (serviceItemType == ServiceItemType.BOARDING || serviceItemType == ServiceItemType.DAYCARE) {
            pickUpAvailableTimeRange = AvailabilityUtil.getAvailableTimeInRange(
                    fromDate, toDate, finalPickUpWeekTimeRange, pickUpOverrides);
        }

        return Pair.of(arrivalAvailableTimeRange, pickUpAvailableTimeRange);
    }

    public FinalWeekTimeRangeDTO queryFinalWeekTimeRange(
            Long companyId,
            Long businessId,
            LocalDate fromDate,
            LocalDate toDate,
            ServiceItemType serviceItemType,
            List<Long> relationServiceIds,
            Map<LocalDate, List<DayTimeRangeDef>> businessWeekTimeRange) {
        List<Long> timeDetailSettingIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(relationServiceIds)) {
            // 拉取 ob working hour setting
            var serviceArrivalPickUpDefMap = availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(
                    companyId, businessId, serviceItemType, relationServiceIds);

            Map<Long, Map<LocalDate, List<DayTimeRangeDef>>> serviceArrivalWeekTimeRange = new HashMap<>();
            Map<Long, Map<LocalDate, List<DayTimeRangeDef>>> servicePickUpWeekTimeRange = new HashMap<>();

            // 给每个 serviceId 都生产对应的时间范围
            for (Long relationServiceId : relationServiceIds) {
                var arrivalPickUpDef = serviceArrivalPickUpDefMap.get(relationServiceId);
                if (arrivalPickUpDef == null || !arrivalPickUpDef.getIsCustomized()) {
                    serviceArrivalWeekTimeRange.put(relationServiceId, businessWeekTimeRange);
                    servicePickUpWeekTimeRange.put(relationServiceId, businessWeekTimeRange);
                    continue;
                }
                Map<LocalDate, List<DayTimeRangeDef>> obArrivalWeekTimeRange = AvailabilityUtil.getAvailableTimeRanges(
                        arrivalPickUpDef, fromDate, toDate, TimeRangeType.ARRIVAL_TIME);
                serviceArrivalWeekTimeRange.put(relationServiceId, obArrivalWeekTimeRange);

                Map<LocalDate, List<DayTimeRangeDef>> obPickUpWeekTimeRange = AvailabilityUtil.getAvailableTimeRanges(
                        arrivalPickUpDef, fromDate, toDate, TimeRangeType.PICK_UP_TIME);
                servicePickUpWeekTimeRange.put(relationServiceId, obPickUpWeekTimeRange);
                if (arrivalPickUpDef.hasTimeRangeSettingId()) {
                    timeDetailSettingIdList.add(arrivalPickUpDef.getTimeRangeSettingId());
                }
            }
            // 合并多个 service 的时间范围
            var finalArrivalWeekTimeRange = mergeMultiServiceTimeRange(serviceArrivalWeekTimeRange);
            var finalPickUpWeekTimeRange = mergeMultiServiceTimeRange(servicePickUpWeekTimeRange);
            return FinalWeekTimeRangeDTO.builder()
                    .arrivalTimeRangeMap(finalArrivalWeekTimeRange)
                    .pickUpTimeRangeMap(finalPickUpWeekTimeRange)
                    .settingIds(timeDetailSettingIdList)
                    .build();

        } else {
            // 部分场景没有 relationServiceIds
            var arrivalPickUpTimeDef = availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(
                    companyId, businessId, serviceItemType);
            if (!arrivalPickUpTimeDef.getIsCustomized()) {
                return FinalWeekTimeRangeDTO.builder()
                        .arrivalTimeRangeMap(businessWeekTimeRange)
                        .pickUpTimeRangeMap(businessWeekTimeRange)
                        .build();
            } else {
                var obArrivalWeekTimeRange = AvailabilityUtil.getAvailableTimeRanges(
                        arrivalPickUpTimeDef, fromDate, toDate, TimeRangeType.ARRIVAL_TIME);
                var obPickUpWeekTimeRange = AvailabilityUtil.getAvailableTimeRanges(
                        arrivalPickUpTimeDef, fromDate, toDate, TimeRangeType.PICK_UP_TIME);
                if (arrivalPickUpTimeDef.hasTimeRangeSettingId()) {
                    timeDetailSettingIdList.add(arrivalPickUpTimeDef.getTimeRangeSettingId());
                }
                return FinalWeekTimeRangeDTO.builder()
                        .arrivalTimeRangeMap(obArrivalWeekTimeRange)
                        .pickUpTimeRangeMap(obPickUpWeekTimeRange)
                        .settingIds(timeDetailSettingIdList)
                        .build();
            }
        }
    }

    public Map<LocalDate, List<DayTimeRangeDef>> getAvailableTimeRangeByPetCapacity(
            long companyId,
            long businessId,
            LocalDate startDate,
            LocalDate endDate,
            Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday) {
        var usedBookingAppointmentTimeList = getAppointmentByTimeRange(
                companyId,
                List.of(businessId),
                List.of(ServiceItemType.EVALUATION),
                IN_PROGRESS_STATUS_VALUE_SET,
                startDate,
                endDate,
                organizationClient.getCompanyTimeZoneName(companyId));
        var usedBookingRequestTimeList = queryDateRangeEvaluationBookingRequest(
                List.of(businessId), List.of(ServiceItemType.EVALUATION), startDate, endDate);

        return TimeRangeUtils.getAvailableTimeRange(
                timeRangeForEveryday, usedBookingAppointmentTimeList, usedBookingRequestTimeList, startDate, endDate);
    }

    /**
     * 将多个 service 的时间聚合在一起
     */
    public Map<LocalDate, List<DayTimeRangeDef>> mergeMultiServiceTimeRange(
            Map<Long, Map<LocalDate, List<DayTimeRangeDef>>> serviceArrivalWeekTimeRange) {
        Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange = null;
        if (serviceArrivalWeekTimeRange.size() == 1) {
            finalTimeRange = serviceArrivalWeekTimeRange.values().iterator().next();
        } else {
            // merge all service time ranges
            for (Map<LocalDate, List<DayTimeRangeDef>> timeRange : serviceArrivalWeekTimeRange.values()) {
                if (finalTimeRange == null) {
                    finalTimeRange = timeRange;
                    continue;
                }
                finalTimeRange = AvailabilityUtil.mergeTimeRanges(finalTimeRange, timeRange);
            }
        }
        if (CollectionUtils.isEmpty(finalTimeRange)) {
            return Map.of();
        }
        return finalTimeRange;
    }
    /**
     * 置空不可用的时间
     */
    private static Map<LocalDate, List<DayTimeRangeDef>> removeUnavailableDates(
            Map<LocalDate, List<DayTimeRangeDef>> finalTimeRange, List<LocalDate> businessClosedDates) {
        if (!CollectionUtils.isEmpty(finalTimeRange)) {
            // remove business closed dates
            for (Map.Entry<LocalDate, List<DayTimeRangeDef>> timeRangeEntry : finalTimeRange.entrySet()) {
                if (businessClosedDates.contains(timeRangeEntry.getKey())) {
                    finalTimeRange.put(timeRangeEntry.getKey(), List.of());
                }
            }
        }
        return finalTimeRange;
    }

    public List<UsedLocalTimeDTO> queryDateRangeEvaluationBookingRequest(
            List<Long> businessIds, List<ServiceItemType> serviceItems, LocalDate startDate, LocalDate endDate) {

        var pagination = Pagination.builder().pageNum(1).pageSize(1000).build();
        var filter = new BookingRequestFilterDTO()
                .setBusinessIds(businessIds)
                .setStatuses(List.of(BookingRequestStatus.SUBMITTED_VALUE))
                .setStartDate(startDate.toString())
                .setEndDate(endDate.toString())
                .setServiceItems(serviceItems)
                .setPaymentStatuses(List.of(
                        BookingRequestModel.PaymentStatus.NO_PAYMENT,
                        BookingRequestModel.PaymentStatus.PROCESSING,
                        BookingRequestModel.PaymentStatus.SUCCESS));
        filter.setOrderBys(List.of());
        var bookingRequstList =
                bookingRequestService.listByBusinessFilter(filter, pagination).getFirst();

        var bookingIds =
                bookingRequstList.stream().map(BookingRequest::getId).distinct().toList();
        if (CollectionUtils.isEmpty(bookingRequstList)) {
            return List.of();
        }

        List<EvaluationTestDetail> evaluationDetailList =
                evaluationTestDetailService.listByBookingRequestId(bookingIds);

        Map<Long, Map<LocalDate, Set<Integer>>> petIdToLocalTimeMap = new HashMap<>();
        for (EvaluationTestDetail detail : evaluationDetailList) {
            long petId = detail.getPetId();
            LocalDate date = LocalDate.parse(detail.getStartDate());
            int time = detail.getStartTime();

            petIdToLocalTimeMap
                    .computeIfAbsent(petId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(time);
        }

        List<UsedLocalTimeDTO> result = new ArrayList<>();
        for (Map<LocalDate, Set<Integer>> dateMap : petIdToLocalTimeMap.values()) {
            for (Map.Entry<LocalDate, Set<Integer>> entry : dateMap.entrySet()) {
                for (Integer time : entry.getValue()) {
                    result.add(new UsedLocalTimeDTO(entry.getKey(), time));
                }
            }
        }

        return result;
    }

    public List<ArrivalPickUpTimeOverrideModel> getArrivalPickUpTimeOverrides(
            Long businessId, List<Long> timeRangeSettingIds) {
        if (CollectionUtils.isEmpty(timeRangeSettingIds)) {
            return List.of();
        }
        var settingsMap = availabilitySettingService.getBookingTimeRangeSettingByIds(timeRangeSettingIds).stream()
                .collect(Collectors.toMap(BookingTimeRangeSetting::getId, s -> s, (k1, k2) -> k2));
        // businessId to current date
        var overrides = availabilitySettingService.getArrivalPickUpOverrideBySettingIds(timeRangeSettingIds);
        return ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideModel(
                overrides, settingsMap, businessHelper.batchGetBusinessDateTimeMap(List.of(businessId)));
    }

    /**
     * 只查询了前 1000 条预约记录
     */
    public List<UsedLocalTimeDTO> getAppointmentByTimeRange(
            long companyId,
            List<Long> businessIds,
            List<ServiceItemType> serviceItemTypes,
            List<AppointmentStatus> statuses,
            LocalDate startDate,
            LocalDate endDate,
            String timezone) {
        int pageNum = 1;
        int pageSize = 1000;

        ZonedDateTime start = startDate.atStartOfDay().atZone(ZoneId.of(timezone));
        ZonedDateTime end = endDate.atStartOfDay().atZone(ZoneId.of(timezone)).plusDays(1);

        List<Integer> serviceTypes = ServiceItemEnum.convertServiceItemListToBitValueList(
                serviceItemTypes.stream().map(ServiceItemType::getNumber).toList());

        // 限制查询 60 天，避免查询过多数据导致索引失效
        ListAppointmentsRequest.Filter filter = ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(buildInterval(end.minusDays(60).toEpochSecond(), end.toEpochSecond() - 1))
                .setEndTimeRange(
                        buildInterval(start.toEpochSecond(), start.plusDays(60).toEpochSecond() - 1))
                .addAllStatus(statuses)
                .addAllServiceTypeIncludes(serviceTypes)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterBookingRequest(true)
                .build();

        ListAppointmentsResponse response =
                appointmentServiceBlockingStub.listAppointments(ListAppointmentsRequest.newBuilder()
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(pageNum)
                                .setPageSize(pageSize))
                        .setCompanyId(companyId)
                        .addAllBusinessIds(businessIds)
                        .setFilter(filter)
                        .build());

        var bookingIds = response.getAppointmentsList().stream()
                .map(AppointmentModel::getId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(bookingIds)) {
            return List.of();
        }
        var petDetailList = petDetailServiceBlockingStub.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(bookingIds)
                .build());
        Map<Long, Map<LocalDate, Set<Integer>>> petIdToLocalTimeMap = new HashMap<>();

        // 组装每个 pet 的 evaluation 时间
        for (EvaluationServiceModel evaluationServiceModel : petDetailList.getPetEvaluationsList()) {
            long petId = evaluationServiceModel.getPetId();
            LocalDate date = LocalDate.parse(evaluationServiceModel.getStartDate()); // 假设是 yyyy-MM-dd 格式
            int time = evaluationServiceModel.getStartTime();

            petIdToLocalTimeMap
                    .computeIfAbsent(petId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(time);
        }

        // 转换为 List<UsedLocalTimeDTO>
        List<UsedLocalTimeDTO> result = new ArrayList<>();
        for (Map<LocalDate, Set<Integer>> dateMap : petIdToLocalTimeMap.values()) {
            for (Map.Entry<LocalDate, Set<Integer>> entry : dateMap.entrySet()) {
                LocalDate date = entry.getKey();
                for (Integer time : entry.getValue()) {
                    result.add(UsedLocalTimeDTO.builder()
                            .startDate(date)
                            .startTime(time)
                            .build());
                }
            }
        }

        return result;
    }

    private Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }
}

package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.lib.common.exception.ExceptionUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/12/27
 */
@Mapper
public interface EnumConverter {

    EnumConverter INSTANCE = Mappers.getMapper(EnumConverter.class);

    default ServiceItemType mapServiceItemType(int value) {
        var type = ServiceItemType.forNumber(value);
        if (type == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported care type");
        }
        return type;
    }
}

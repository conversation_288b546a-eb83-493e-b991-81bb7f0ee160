package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.math.BigDecimal;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class GroupClassServiceDetailDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    public static final GroupClassServiceDetail groupClassServiceDetail = new GroupClassServiceDetail();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.id")
    public static final SqlColumn<Long> id = groupClassServiceDetail.id;

    /**
     * Database Column Remarks:
     *   The id of booking request
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.booking_request_id")
    public static final SqlColumn<Long> bookingRequestId = groupClassServiceDetail.bookingRequestId;

    /**
     * Database Column Remarks:
     *   The id of pet, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.pet_id")
    public static final SqlColumn<Long> petId = groupClassServiceDetail.petId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.class_instance_id")
    public static final SqlColumn<Long> classInstanceId = groupClassServiceDetail.classInstanceId;

    /**
     * Database Column Remarks:
     *   The id of trainer, associated with the current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.staff_id")
    public static final SqlColumn<Long> staffId = groupClassServiceDetail.staffId;

    /**
     * Database Column Remarks:
     *   The id of current service
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_id")
    public static final SqlColumn<Long> serviceId = groupClassServiceDetail.serviceId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.service_price")
    public static final SqlColumn<BigDecimal> servicePrice = groupClassServiceDetail.servicePrice;

    /**
     * Database Column Remarks:
     *   The date list of the group class session, ["yyyy-MM-dd"]
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.specific_dates")
    public static final SqlColumn<String> specificDates = groupClassServiceDetail.specificDates;

    /**
     * Database Column Remarks:
     *   The start time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.start_time")
    public static final SqlColumn<Integer> startTime = groupClassServiceDetail.startTime;

    /**
     * Database Column Remarks:
     *   The end time of the service, unit minute, 540 means 09:00
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.end_time")
    public static final SqlColumn<Integer> endTime = groupClassServiceDetail.endTime;

    /**
     * Database Column Remarks:
     *   Duration of each session in minutes, only for training
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.duration_per_session")
    public static final SqlColumn<Integer> durationPerSession = groupClassServiceDetail.durationPerSession;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = groupClassServiceDetail.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = groupClassServiceDetail.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: group_class_service_detail.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = groupClassServiceDetail.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: group_class_service_detail")
    public static final class GroupClassServiceDetail extends AliasableSqlTable<GroupClassServiceDetail> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> bookingRequestId = column("booking_request_id", JDBCType.BIGINT);

        public final SqlColumn<Long> petId = column("pet_id", JDBCType.BIGINT);

        public final SqlColumn<Long> classInstanceId = column("class_instance_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Long> serviceId = column("service_id", JDBCType.BIGINT);

        public final SqlColumn<BigDecimal> servicePrice = column("service_price", JDBCType.NUMERIC);

        public final SqlColumn<String> specificDates = column("specific_dates", JDBCType.OTHER, "com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler");

        public final SqlColumn<Integer> startTime = column("start_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> endTime = column("end_time", JDBCType.INTEGER);

        public final SqlColumn<Integer> durationPerSession = column("duration_per_session", JDBCType.INTEGER);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public GroupClassServiceDetail() {
            super("group_class_service_detail", GroupClassServiceDetail::new);
        }
    }
}
package com.moego.svc.online.booking.mapstruct;

import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetailModel;
import com.moego.idl.service.online_booking.v1.CreateDogWalkingServiceDetailRequest;
import com.moego.svc.online.booking.entity.DogWalkingServiceDetail;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        uses = {TimestampConverter.class})
public interface DogWalkingServiceDetailConverter {
    DogWalkingServiceDetailConverter INSTANCE = Mappers.getMapper(DogWalkingServiceDetailConverter.class);

    DogWalkingServiceDetailModel entityToModel(DogWalkingServiceDetail entity);

    DogWalkingServiceDetail createRequestToEntity(CreateDogWalkingServiceDetailRequest createRequest);

    default int pbEnumToInt(com.google.protobuf.ProtocolMessageEnum enumValue) {
        return enumValue.getNumber();
    }

    default com.moego.idl.models.offering.v1.ServiceScopeType dateToPBTimestamp(int i) {
        return com.moego.idl.models.offering.v1.ServiceScopeType.forNumber(i);
    }
}

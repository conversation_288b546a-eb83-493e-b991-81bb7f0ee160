package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BoardingAddOnDetailDynamicSqlSupport.boardingAddOnDetail;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.online.booking.entity.BoardingAddOnDetail;
import com.moego.svc.online.booking.mapper.BoardingAddOnDetailMapper;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
public class BoardingAddOnDetailService {

    private final BoardingAddOnDetailMapper boardingAddOnDetailMapper;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    public BoardingAddOnDetail get(long id) {
        return boardingAddOnDetailMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(BoardingAddOnDetail entity) {

        if (entity.getDateType() == PetDetailDateType.PET_DETAIL_DATE_DATE_POINT) {
            setStartDateAndSpecificDates(entity);
        }

        check(entity);

        boardingAddOnDetailMapper.insertSelective(entity);
        return entity.getId();
    }

    private static void check(BoardingAddOnDetail entity) {
        if (!CommonUtil.isNormal(entity.getBookingRequestId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "bookingRequestId is required");
        }
        if (!CommonUtil.isNormal(entity.getServiceDetailId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "serviceDetailId is required");
        }
        if (!CommonUtil.isNormal(entity.getPetId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "petId is required");
        }
        if (!CommonUtil.isNormal(entity.getAddOnId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "addOnId is required");
        }
    }

    private static void setStartDateAndSpecificDates(BoardingAddOnDetail entity) {
        if (hasSpecificDates(entity) && !hasStartDate(entity)) {
            entity.setStartDate(entity.getSpecificDates().get(0));
        } else if (hasStartDate(entity) && !hasSpecificDates(entity)) {
            entity.setSpecificDates(List.of(entity.getStartDate()));
        }
    }

    private static boolean hasStartDate(BoardingAddOnDetail entity) {
        return entity.getStartDate() != null;
    }

    private static boolean hasSpecificDates(BoardingAddOnDetail entity) {
        return !ObjectUtils.isEmpty(entity.getSpecificDates());
    }

    /**
     * Update a record by id, null properties will be ignored.
     *
     * @param entity entity
     * @return affected rows
     */
    public int update(BoardingAddOnDetail entity) {
        entity.setUpdatedAt(new Date());
        return boardingAddOnDetailMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * Delete a record by id.
     *
     * @param id id
     * @return deleted rows
     */
    public int delete(long id) {
        return boardingAddOnDetailMapper.update(c -> c.set(boardingAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(boardingAddOnDetail.id, isEqualTo(id))
                .and(boardingAddOnDetail.deletedAt, isNull()));
    }

    /**
     * List boarding add-on detail by serviceDetailId, not include deleted record.
     *
     * @param serviceDetailId serviceDetailId
     * @return list of boarding add-on detail
     */
    public List<BoardingAddOnDetail> listByServiceDetailId(long serviceDetailId) {
        return boardingAddOnDetailMapper.select(
                c -> c.where(boardingAddOnDetail.serviceDetailId, isEqualTo(serviceDetailId))
                        .and(boardingAddOnDetail.deletedAt, isNull()));
    }

    /**
     * List boarding add-on detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestId booking request id
     * @return boarding service detail id -> list of boarding add-on detail
     */
    public Map<Long, List<BoardingAddOnDetail>> listByBookingRequestId(long bookingRequestId) {
        return boardingAddOnDetailMapper
                .select(c -> c.where(boardingAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                        .and(boardingAddOnDetail.deletedAt, isNull()))
                .stream()
                .collect(Collectors.groupingBy(BoardingAddOnDetail::getServiceDetailId));
    }

    /**
     * List boarding add-on detail by bookingRequestId, not include deleted record.
     *
     * @param bookingRequestIds list of booking request id
     * @return bookingRequestId -> boardingServiceDetailId -> list of boarding add-on detail
     */
    public Map<Long, Map<Long, List<BoardingAddOnDetail>>> listByBookingRequestId(List<Long> bookingRequestIds) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }
        return boardingAddOnDetailMapper
                .select(c -> c.where(boardingAddOnDetail.bookingRequestId, isIn(bookingRequestIds))
                        .and(boardingAddOnDetail.deletedAt, isNull()))
                .stream()
                .collect(Collectors.groupingBy(
                        BoardingAddOnDetail::getBookingRequestId,
                        Collectors.groupingBy(BoardingAddOnDetail::getServiceDetailId)));
    }
}

package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class BookingDateRangeSettingDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    public static final BookingDateRangeSetting bookingDateRangeSetting = new BookingDateRangeSetting();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.id")
    public static final SqlColumn<Long> id = bookingDateRangeSetting.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.business_id")
    public static final SqlColumn<Long> businessId = bookingDateRangeSetting.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.company_id")
    public static final SqlColumn<Long> companyId = bookingDateRangeSetting.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.service_item_type")
    public static final SqlColumn<Integer> serviceItemType = bookingDateRangeSetting.serviceItemType;

    /**
     * Database Column Remarks:
     *   0: no limit, 1: offset from today, 2: specific date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.start_date_type")
    public static final SqlColumn<Integer> startDateType = bookingDateRangeSetting.startDateType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_start_date")
    public static final SqlColumn<Date> specificStartDate = bookingDateRangeSetting.specificStartDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_start_date_offset")
    public static final SqlColumn<Integer> maxStartDateOffset = bookingDateRangeSetting.maxStartDateOffset;

    /**
     * Database Column Remarks:
     *   0: no limit, 1: offset from today, 2: specific date
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.end_date_type")
    public static final SqlColumn<Integer> endDateType = bookingDateRangeSetting.endDateType;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.specific_end_date")
    public static final SqlColumn<Date> specificEndDate = bookingDateRangeSetting.specificEndDate;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.max_end_date_offset")
    public static final SqlColumn<Integer> maxEndDateOffset = bookingDateRangeSetting.maxEndDateOffset;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.created_at")
    public static final SqlColumn<Date> createdAt = bookingDateRangeSetting.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_at")
    public static final SqlColumn<Date> updatedAt = bookingDateRangeSetting.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: booking_date_range_setting.updated_by")
    public static final SqlColumn<Long> updatedBy = bookingDateRangeSetting.updatedBy;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: booking_date_range_setting")
    public static final class BookingDateRangeSetting extends AliasableSqlTable<BookingDateRangeSetting> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Integer> serviceItemType = column("service_item_type", JDBCType.INTEGER);

        public final SqlColumn<Integer> startDateType = column("start_date_type", JDBCType.INTEGER);

        public final SqlColumn<Date> specificStartDate = column("specific_start_date", JDBCType.DATE);

        public final SqlColumn<Integer> maxStartDateOffset = column("max_start_date_offset", JDBCType.INTEGER);

        public final SqlColumn<Integer> endDateType = column("end_date_type", JDBCType.INTEGER);

        public final SqlColumn<Date> specificEndDate = column("specific_end_date", JDBCType.DATE);

        public final SqlColumn<Integer> maxEndDateOffset = column("max_end_date_offset", JDBCType.INTEGER);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Long> updatedBy = column("updated_by", JDBCType.BIGINT);

        public BookingDateRangeSetting() {
            super("booking_date_range_setting", BookingDateRangeSetting::new);
        }
    }
}
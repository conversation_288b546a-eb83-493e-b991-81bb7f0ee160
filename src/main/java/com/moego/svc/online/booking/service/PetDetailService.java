package com.moego.svc.online.booking.service;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class PetDetailService {

    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailService;

    public static List<BoardingServiceDetailModel> getBoardingServiceDetails(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(service -> service.getBoarding().getService())
                .toList();
    }

    public static List<DaycareServiceDetailModel> getDaycareServiceDetails(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasDaycare)
                .map(service -> service.getDaycare().getService())
                .toList();
    }

    public static List<EvaluationTestDetailModel> getEvaluationServiceDetails(BookingRequestModel bookingRequest) {
        return bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(service -> service.getEvaluation().getService())
                .toList();
    }

    public static List<Long> getPetIds(BookingRequestModel bookingRequest) {
        List<Long> petIds = new ArrayList<>();
        bookingRequest.getServicesList().forEach(service -> {
            switch (service.getServiceCase()) {
                case BOARDING -> petIds.add(service.getBoarding().getService().getPetId());
                case DAYCARE -> petIds.add(service.getDaycare().getService().getPetId());
                case GROOMING -> petIds.add(service.getGrooming().getService().getPetId());
                case EVALUATION -> petIds.add(
                        service.getEvaluation().getService().getPetId());
                case DOG_WALKING -> petIds.add(
                        service.getDogWalking().getService().getPetId());
                case GROUP_CLASS -> petIds.add(
                        service.getGroupClass().getService().getPetId());
                default -> {}
            }
        });
        return petIds.stream()
                .filter(id -> Objects.nonNull(id) && id > 0)
                .distinct()
                .toList();
    }

    /**
     * 拉取 boarding、daycare、grooming 服务详情
     */
    public static List<Long> getServiceIds(BookingRequestModel bookingRequest) {
        List<Long> serviceIds = new ArrayList<>();
        bookingRequest.getServicesList().forEach(service -> {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    serviceIds.add(service.getBoarding().getService().getServiceId());
                    service.getBoarding().getAddonsList().forEach(addOn -> {
                        serviceIds.add(addOn.getAddOnId());
                    });
                }
                case DAYCARE -> {
                    serviceIds.add(service.getDaycare().getService().getServiceId());
                    service.getDaycare().getAddonsList().forEach(addOn -> {
                        serviceIds.add(addOn.getAddOnId());
                    });
                }
                case GROOMING -> {
                    serviceIds.add(service.getGrooming().getService().getServiceId());
                    service.getGrooming().getAddonsList().forEach(addOn -> {
                        serviceIds.add(addOn.getAddOnId());
                    });
                }
                    // TODO dog walking add on
                case DOG_WALKING -> serviceIds.add(
                        service.getDogWalking().getService().getServiceId());
                default -> {}
            }
        });

        return serviceIds.stream()
                .filter(id -> Objects.nonNull(id) && id > 0)
                .distinct()
                .toList();
    }

    public static Long getPetSizeId(String weight, List<BusinessPetSizeModel> petSizeList) {
        if (!StringUtils.hasText(weight)) {
            return null;
        }
        BigDecimal weightDecimal;
        try {
            weightDecimal = new BigDecimal(weight);
        } catch (NumberFormatException e) {
            return null;
        }

        var roundedWeight = weightDecimal.setScale(0, RoundingMode.HALF_UP).intValue();
        BusinessPetSizeModel targetSize = petSizeList.stream()
                .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                .findFirst()
                .orElse(null);
        if (targetSize == null) {
            return null;
        }
        return targetSize.getId();
    }

    public Map<Long, Long> getLastLodgingUnitId(
            Long companyId, Long businessId, Long customerId, Collection<Long> petIds, Collection<Long> serviceIds) {
        return petDetailService
                .getLastPetDetail(GetLastPetDetailRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addCustomerId(customerId)
                        .addAllPetId(petIds.stream().distinct().toList())
                        .setFilter(GetLastPetDetailRequest.Filter.newBuilder()
                                .setBusinessId(businessId)
                                .addAllServiceIds(serviceIds.stream().distinct().toList())
                                .build())
                        .build())
                .getPetDetailsList()
                .stream()
                .filter(petDetail -> CommonUtil.isNormal(petDetail.getLodgingId()))
                .collect(Collectors.toMap(PetDetailModel::getPetId, PetDetailModel::getLodgingId));
    }
}

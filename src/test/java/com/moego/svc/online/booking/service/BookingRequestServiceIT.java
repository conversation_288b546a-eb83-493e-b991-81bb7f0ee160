package com.moego.svc.online.booking.service;

import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link BookingRequestService}
 */
@SpringBootTest
@Disabled("For local test")
class BookingRequestServiceIT {

    @Autowired
    BookingRequestService bookingRequestService;

    @Autowired
    BookingRequestMapper bookingRequestMapper;

    /**
     * {@link BookingRequestService#update(BookingRequest)}
     */
    @Test
    @Transactional
    void testUpdateAttr() {

        var oldestBookingRequest = bookingRequestMapper
                .selectOne(c -> c.where(bookingRequest.deletedAt, isNull())
                        .orderBy(bookingRequest.id)
                        .limit(1))
                .orElseThrow();

        var updateBean = new BookingRequest();
        updateBean.setId(oldestBookingRequest.getId());
        updateBean.setAttr(
                BookingRequestModel.Attr.newBuilder().setIsNewVisitor(true).build());

        bookingRequestService.update(updateBean);

        var updatedBookingRequest = bookingRequestMapper
                .selectByPrimaryKey(oldestBookingRequest.getId())
                .orElseThrow();

        assertThat(updatedBookingRequest.getAttr()).isNotNull();
        assertThat(updatedBookingRequest.getAttr().getIsNewVisitor()).isTrue();
    }
}

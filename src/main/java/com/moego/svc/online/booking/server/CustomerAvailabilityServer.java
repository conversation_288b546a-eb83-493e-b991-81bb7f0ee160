package com.moego.svc.online.booking.server;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import com.github.pagehelper.page.PageMethod;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerRequest;
import com.moego.idl.service.online_booking.v1.ListBlockedCustomerResponse;
import com.moego.idl.service.online_booking.v1.SetCustomerBlockStatusRequest;
import com.moego.idl.service.online_booking.v1.SetCustomerBlockStatusResponse;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.online.booking.dto.CustomerBlockInfoDTO;
import com.moego.svc.online.booking.entity.BlockCustomer;
import com.moego.svc.online.booking.mapper.BlockCustomerDynamicSqlSupport;
import com.moego.svc.online.booking.mapper.BlockCustomerMapper;
import com.moego.svc.online.booking.mapstruct.PageConverter;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.DerivedColumn;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.data.util.Pair;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class CustomerAvailabilityServer extends CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceImplBase {

    private final BlockCustomerMapper blockCustomerMapper;

    @Override
    public void setCustomerBlockStatus(
            SetCustomerBlockStatusRequest request, StreamObserver<SetCustomerBlockStatusResponse> responseObserver) {
        List<Long> newCustomerIds = new ArrayList<>(request.getCustomerIdsList());
        List<Long> existCustomerIds = blockCustomerMapper
                .select(c -> c.where(BlockCustomerDynamicSqlSupport.customerId, isIn(newCustomerIds))
                        .and(
                                BlockCustomerDynamicSqlSupport.serviceItemType,
                                isIn(request.getServiceItemTypesValueList()))
                        .and(BlockCustomerDynamicSqlSupport.companyId, isEqualTo(request.getCompanyId())))
                .stream()
                .map(BlockCustomer::getCustomerId)
                .distinct()
                .toList();
        newCustomerIds.removeAll(existCustomerIds);

        if (!CollectionUtils.isEmpty(newCustomerIds)) {
            List<BlockCustomer> customers = newCustomerIds.stream()
                    .map(customerId -> request.getServiceItemTypesValueList().stream()
                            .map(serviceItemType -> {
                                BlockCustomer blockCustomer = new BlockCustomer();
                                blockCustomer.setCustomerId(customerId);
                                blockCustomer.setServiceItemType(serviceItemType);
                                blockCustomer.setCompanyId(request.getCompanyId());
                                blockCustomer.setIsActive(request.getNeedBlock());
                                blockCustomer.setUpdatedBy(request.getStaffId());
                                blockCustomer.setCreatedAt(new Date());
                                blockCustomer.setUpdatedAt(new Date());
                                return blockCustomer;
                            })
                            .toList())
                    .flatMap(List::stream)
                    .toList();
            blockCustomerMapper.insertMultiple(customers);
        }

        if (!CollectionUtils.isEmpty(existCustomerIds)) {
            blockCustomerMapper.update(c -> c.set(BlockCustomerDynamicSqlSupport.isActive)
                    .equalTo(request.getNeedBlock())
                    .set(BlockCustomerDynamicSqlSupport.updatedBy)
                    .equalTo(request.getStaffId())
                    .set(BlockCustomerDynamicSqlSupport.updatedAt)
                    .equalTo(new Date())
                    .where(BlockCustomerDynamicSqlSupport.customerId, isIn(existCustomerIds))
                    .and(BlockCustomerDynamicSqlSupport.serviceItemType, isIn(request.getServiceItemTypesValueList()))
                    .and(BlockCustomerDynamicSqlSupport.companyId, isEqualTo(request.getCompanyId())));
        }

        responseObserver.onNext(SetCustomerBlockStatusResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void listBlockedCustomer(
            ListBlockedCustomerRequest request, StreamObserver<ListBlockedCustomerResponse> responseObserver) {
        // check param
        if (request.getCompanyId() == 0 && request.getCustomerIdsCount() == 0) {
            throw new BizException(
                    Code.CODE_PARAMS_ERROR_VALUE, "companyId and customerIds cannot be empty at the same time");
        }

        Pair<List<CustomerBlockInfoDTO>, Pagination> listPaginationPair = paginateCustomerBlockService(request);

        ListBlockedCustomerResponse response = ListBlockedCustomerResponse.newBuilder()
                .addAllCustomerBlockInfos(listPaginationPair.getFirst().stream()
                        .map(info -> ListBlockedCustomerResponse.CustomerBlockInfo.newBuilder()
                                .setCustomerId(info.getCustomerId())
                                .addAllServiceItemTypes(Arrays.stream(info.getServiceItemTypes())
                                        .map(ServiceItemType::forNumber)
                                        .toList())
                                .build())
                        .distinct()
                        .toList())
                .setPagination(PageConverter.INSTANCE.toResponse(listPaginationPair.getSecond()))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    /*private*/ Pair<List<CustomerBlockInfoDTO>, Pagination> paginateCustomerBlockService(
            ListBlockedCustomerRequest request) {
        /*
         * select array(select jsonb_array_elements_text(jsonb_agg(service_item_type))::integer) as service_item_types, customer_id
         * from block_customer
         * where company_id = ...
         *   and service_item_type in (...)
         *   and customer_id in (...)
         *   and is_active = true
         * group by customer_id LIMIT 10;
         */
        DerivedColumn<Integer> serviceItemTypes =
                DerivedColumn.of("array(select jsonb_array_elements_text(jsonb_agg(service_item_type))::integer)");
        BasicColumn[] selectList = Stream.concat(
                        Stream.of(serviceItemTypes.as("service_item_types")),
                        Stream.of(BlockCustomerDynamicSqlSupport.customerId))
                .toArray(BasicColumn[]::new);
        SelectStatementProvider selectStatement = select(selectList)
                .from(BlockCustomerDynamicSqlSupport.blockCustomer)
                .where(
                        BlockCustomerDynamicSqlSupport.companyId,
                        isEqualToWhenPresent(request.getCompanyId() > 0 ? request.getCompanyId() : null))
                .and(BlockCustomerDynamicSqlSupport.serviceItemType, isIn(request.getServiceItemTypesValueList()))
                .and(BlockCustomerDynamicSqlSupport.customerId, isInWhenPresent(request.getCustomerIdsList()))
                .and(BlockCustomerDynamicSqlSupport.isActive, isEqualTo(true))
                .groupBy(BlockCustomerDynamicSqlSupport.customerId)
                .build()
                .render(RenderingStrategies.MYBATIS3);

        var pagination = PageConverter.INSTANCE.toPagination(request.getPagination());

        try (var page = PageMethod.<Map<String, Object>>startPage(pagination.pageNum(), pagination.pageSize())) {

            blockCustomerMapper.selectManyMappedRows(selectStatement);

            var list = page.getResult().stream()
                    .map(row -> {
                        var c = new CustomerBlockInfoDTO();
                        c.setCustomerId((Long) row.get("customer_id"));
                        c.setServiceItemTypes((Integer[]) row.get("service_item_types"));
                        return c;
                    })
                    .toList();

            return Pair.of(list, new Pagination(pagination.pageNum(), pagination.pageSize(), (int) page.getTotal()));
        }
    }
}

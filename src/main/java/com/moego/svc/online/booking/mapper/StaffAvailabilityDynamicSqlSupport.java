package com.moego.svc.online.booking.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class StaffAvailabilityDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability")
    public static final StaffAvailability staffAvailability = new StaffAvailability();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.id")
    public static final SqlColumn<Long> id = staffAvailability.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.company_id")
    public static final SqlColumn<Long> companyId = staffAvailability.companyId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.business_id")
    public static final SqlColumn<Long> businessId = staffAvailability.businessId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.staff_id")
    public static final SqlColumn<Long> staffId = staffAvailability.staffId;

    /**
     * Database Column Remarks:
     *   staff available of ob
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.is_available")
    public static final SqlColumn<Boolean> isAvailable = staffAvailability.isAvailable;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.created_at")
    public static final SqlColumn<Date> createdAt = staffAvailability.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: staff_availability.updated_at")
    public static final SqlColumn<Date> updatedAt = staffAvailability.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: staff_availability")
    public static final class StaffAvailability extends AliasableSqlTable<StaffAvailability> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> companyId = column("company_id", JDBCType.BIGINT);

        public final SqlColumn<Long> businessId = column("business_id", JDBCType.BIGINT);

        public final SqlColumn<Long> staffId = column("staff_id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> isAvailable = column("is_available", JDBCType.BIT);

        public final SqlColumn<Date> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public StaffAvailability() {
            super("staff_availability", StaffAvailability::new);
        }
    }
}
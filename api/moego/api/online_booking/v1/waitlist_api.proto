syntax = "proto3";

package moego.api.online_booking.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/appointment_defs.proto";
import "moego/models/appointment/v1/appointment_note_defs.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/membership/v1/subscription_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/online_booking/v1/booking_request_models.proto";
import "moego/models/online_booking/v1/waitlist_defs.proto";
import "moego/utils/v2/condition_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.online_booking.v1";

// create waitlist params
message CreateWaitlistParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Pet services
  repeated moego.models.online_booking.v1.Service services = 3 [(validate.rules).repeated = {min_items: 1}];
  // comment
  optional string comment = 4;
  // start date, format: yyyy-mm-dd
  string start_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date, format: yyyy-mm-dd
  string end_date = 6 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// create waitlist result
message CreateWaitlistResult {
  // waitlist id
  int64 waitlist_id = 1;
}

// update waitlist params
message UpdateWaitlistParams {
  // waitlist id
  int64 waitlist_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Pet services
  repeated moego.models.online_booking.v1.Service services = 2;
  // comment
  optional string comment = 3;
  // start date, format: yyyy-mm-dd
  optional string start_date = 4 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
  // end date, format: yyyy-mm-dd
  optional string end_date = 5 [(validate.rules).string = {pattern: "^\\d{4}-\\d{2}-\\d{2}$"}];
}

// update waitlist result
message UpdateWaitlistResult {}

// delete waitlist params
message DeleteWaitlistParams {
  // waitlist id
  int64 waitlist_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Delete waitlist result
message DeleteWaitlistResult {}

// get waitlist
message GetWaitlistParams {
  // waitlist id
  int64 waitlist_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get waitlist result
message GetWaitlistResult {
  // waitlist
  WaitlistView waitlist = 1;
}

// list waitlist params
message ListWaitlistParams {
  // business_id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  //service type 只能选 2 3
  models.offering.v1.ServiceItemType service_type = 2 [(validate.rules).enum = {
    defined_only: true
    in: [
      2,
      3
    ]
  }];
  // filter
  optional Filters filters = 3;
  // order by
  repeated moego.utils.v2.OrderBy order_bys = 4;
  // waitlist order by price desc
  optional bool order_price_desc = 5;
  // Pagination
  utils.v2.PaginationRequest pagination = 20 [(validate.rules).message = {required: true}];

  // filter
  message Filters {
    // key word
    optional string keyword = 1 [(validate.rules).string = {max_len: 50}];
    // start date
    optional google.type.Date start_date = 2;
    // end date
    optional google.type.Date end_date = 3;
    // created from ob
    optional bool is_from_ob = 4;
    // is expired
    optional bool is_expired = 5;
    // is available
    optional bool is_available = 6;
  }
}

// list waitlist result
message ListWaitlistResult {
  // waitlist
  repeated WaitlistView waitlists = 1;

  // Pagination
  utils.v2.PaginationResponse pagination = 2;
}

//waitlist view
message WaitlistView {
  //waitlist
  models.online_booking.v1.BookingRequestModel waitlist = 1;
  // is available
  bool is_available = 2;
  // is expired
  bool is_expired = 3;
  //client info
  CustomerInfo client = 4;
  //pet info
  repeated PetInfo pets = 5;
  //create by
  optional CreateBy create_by = 6;
  //total price
  double total_price = 7;
  //membership
  optional moego.models.membership.v1.MembershipSubscriptionListModel membership_subscriptions = 8;
  // profile has request update
  bool has_request_update = 9;
  //create by
  message CreateBy {
    // id
    int64 id = 1;
    // lastName
    string last_name = 2;
    // firstName
    string first_name = 3;
    // avatarPath
    string avatar_path = 4;
  }

  // client information
  message CustomerInfo {
    // client ID
    int64 id = 1;
    // client's last name
    string last_name = 2;
    // client's first name
    string first_name = 3;
    // client label color
    string client_color = 4;
    // path to client's avatar image
    string avatar_path = 5;
    // client's phone number
    string phone_number = 6;
    // is deleted
    bool is_deleted = 7;
  }

  // service information
  message ServiceInfo {
    // service ID
    int64 service_id = 1;
    // service name
    string service_name = 2;
    // service price
    double service_price = 3;
    //service item type
    models.offering.v1.ServiceItemType service_item_type = 4;
    //service price unit
    models.offering.v1.ServicePriceUnit price_unit = 5;
  }

  // pet information
  message PetInfo {
    // pet ID
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // breed of the pet
    string breed = 3;
    // type ID of the pet (e.g., dog, cat)
    int32 pet_type_id = 4;
    // list of services associated with the pet
    repeated ServiceInfo services = 5;
    // avatar path
    string avatar_path = 6;
  }
}

// book waitlist params
message BookWaitlistParams {
  // waitlist id
  int64 waitlist_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // appointment
  models.appointment.v1.AppointmentCreateDef appointment = 3 [(validate.rules).message = {required: true}];
  //下面两个 def，是复用的 createAppointment
  // Pet to services mapping
  repeated models.appointment.v1.PetDetailDef pet_details = 4 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
  // Appointment notes, contains ticket comments and alert notes
  // Ticket comments: For this appointment. Private to business only
  // Alert notes: It will be shown as a red exclamation mark on the appointment card. Private for business.
  repeated models.appointment.v1.AppointmentNoteCreateDef notes = 5 [(validate.rules).repeated = {
    min_items: 0
    max_items: 100
    items: {
      message: {required: true}
    }
  }];
}

// book waitlist result
message BookWaitlistResult {
  // appointment id
  int64 appointment_id = 1;
}

// waitlist service
service WaitlistService {
  // create waitlist
  rpc CreateWaitlist(CreateWaitlistParams) returns (CreateWaitlistResult);
  // update waitlist
  rpc UpdateWaitlist(UpdateWaitlistParams) returns (UpdateWaitlistResult);
  // delete waitlist
  rpc DeleteWaitlist(DeleteWaitlistParams) returns (DeleteWaitlistResult);
  // get waitlist
  rpc GetWaitlist(GetWaitlistParams) returns (GetWaitlistResult);
  // list waitlist
  rpc ListWaitlist(ListWaitlistParams) returns (ListWaitlistResult);
  // book waitlist
  rpc BookWaitlist(BookWaitlistParams) returns (BookWaitlistResult);
}
